// 测试智能边界检查功能
console.log("=== 智能边界检查测试 ===");

// 测试用例
const testCases = [
  {
    message: "晶格大小100*100，运行一下模拟",
    simulationType: "schelling",
    expectedResult: "应该识别为网格尺寸，允许通过"
  },
  {
    message: "设置网格尺寸为50x50",
    simulationType: "schelling", 
    expectedResult: "应该识别为网格尺寸，允许通过"
  },
  {
    message: "调整分辨率到200",
    simulationType: "schelling",
    expectedResult: "应该识别为网格尺寸，允许通过"
  },
  {
    message: "设置温度为2.5",
    simulationType: "schelling",
    expectedResult: "应该识别为无效参数，阻止通过"
  },
  {
    message: "相似性阈值设为0.7",
    simulationType: "schelling",
    expectedResult: "应该识别为相似性阈值，允许通过"
  },
  {
    message: "你好，请介绍一下Schelling模型",
    simulationType: "schelling",
    expectedResult: "普通对话，允许通过"
  }
];

// 同义词映射测试
console.log("\n=== 同义词映射测试 ===");
const schellingMappings = {
  'gridSize': ['晶格', '晶格大小', '网格', '网格大小', '网格尺寸', '分辨率', '格子', '格子大小'],
  'similarityThreshold': ['相似性', '阈值', '满意度', '相似度', '相似性阈值'],
  'density': ['密度', '人口密度', '填充率', '占用率'],
  'groupRatio': ['比例', '群体比例', '种族比例', '组别比例']
};

console.log("Schelling模型参数同义词映射:");
Object.entries(schellingMappings).forEach(([param, synonyms]) => {
  console.log(`${param}: ${synonyms.join(', ')}`);
});

// 智能检测逻辑测试
console.log("\n=== 智能检测逻辑测试 ===");
testCases.forEach((testCase, index) => {
  console.log(`\n测试${index + 1}: "${testCase.message}"`);
  console.log(`模拟类型: ${testCase.simulationType}`);
  console.log(`期望结果: ${testCase.expectedResult}`);
  
  // 简化的检测逻辑
  const messageWords = testCase.message.toLowerCase();
  const foundParams = [];
  
  Object.entries(schellingMappings).forEach(([param, synonyms]) => {
    synonyms.forEach(synonym => {
      if (messageWords.includes(synonym)) {
        foundParams.push(`${synonym} -> ${param}`);
      }
    });
  });
  
  if (foundParams.length > 0) {
    console.log(`✅ 检测到参数: ${foundParams.join(', ')}`);
  } else {
    console.log(`🔄 未检测到特定参数，允许作为普通对话`);
  }
});

console.log("\n=== 边界检查优化方案 ===");
console.log("1. 先进行同义词映射，理解用户真实意图");
console.log("2. 检查映射后的参数是否在当前主题中存在");
console.log("3. 如果参数存在，允许通过并进行Function Calling");
console.log("4. 如果参数不存在，提供友好的错误提示");
console.log("5. 如果没有检测到参数，允许作为普通对话");

console.log("\n✅ 智能边界检查测试完成！");
