# AI导师边界检查功能使用示例

## 功能概述

AI导师边界检查系统确保用户只能在正确的主题页面讨论相关内容，并验证所询问的参数是否存在于当前主题中。

## 使用场景

### 1. 主题范围限制

**场景：** 用户在"生命游戏"页面询问Boids相关问题

**用户输入：** "如何调整鸟群的分离权重？"

**AI回应：**
```
🚨 主题边界提醒

您的问题涉及"Boids鸟群模拟"主题，但当前页面是"生命游戏"。

为了获得准确的指导，请：
1. 先切换到"Boids鸟群模拟"页面
2. 然后再提问相关内容

这样我就能为您提供该主题的专业指导了！
```

### 2. 参数验证

**场景：** 用户在"生命游戏"页面询问不存在的参数

**用户输入：** "如何调整晶格大小？"

**AI回应：**
```
🚨 参数验证提醒

您提到的参数"晶格"在当前的"生命游戏"主题中不存在。

生命游戏支持的参数包括：
• 运行速度：控制模拟运行的速度
• 步数：批量运行的步数
• 生存规则：细胞存活的邻居数要求
• 诞生规则：新细胞诞生的邻居数要求
• 预设图案：可加载的预设图案
• 活细胞颜色：活细胞的显示颜色
• 死细胞颜色：死细胞的显示颜色

如果您需要了解其他主题的参数，请切换到相应的主题页面。
```

### 3. 正常使用

**场景：** 用户在"生命游戏"页面询问有效问题

**用户输入：** "如何调整生命游戏的运行速度？"

**AI回应：**
```
在生命游戏中，您可以通过以下方式调整运行速度：

1. **使用控制面板**：在右侧控制面板中找到"速度"滑块
2. **参数范围**：速度值范围是1-100，数值越大运行越快
3. **实时调整**：可以在模拟运行时实时调整速度

如果启用了Function Calling，我还可以直接为您设置速度：
[执行速度设置操作]
```

## 支持的主题和参数

### 生命游戏 (Game of Life)
- 基础控制：运行速度、步数
- 规则设置：生存规则、诞生规则
- 图案设置：预设图案
- 外观设置：活细胞颜色、死细胞颜色

### Boids鸟群模拟
- 基础参数：鸟群数量、最大速度、感知半径
- 行为权重：分离权重、对齐权重、聚合权重

### 伊辛模型 (Ising Model)
- 物理参数：温度、外磁场
- 状态设置：自旋配置

### Physarum黏菌模拟
- 代理参数：代理数量、步长
- 传感器设置：传感器角度、传感器距离
- 信息素系统：沉积量、衰减率

### Schelling分离模型
- 人口参数：相似性阈值、人口密度、群体比例

## 用户界面改进

### 1. 当前主题提示
- 在输入框上方显示当前主题名称
- 提醒用户边界条件

### 2. 状态指示器
- 显示是否在有效主题页面
- 提示用户切换页面

### 3. 输入提示
- 根据当前主题调整placeholder文本
- 提供相关的输入建议

### 4. 初始欢迎消息
- 根据当前主题显示不同的欢迎消息
- 提供使用提示和边界条件说明

## 技术实现

### 核心模块
- `BoundaryCheck.ts` - 边界检查核心逻辑
- `useAITutorBoundaryCheck.ts` - React Hook
- 集成到现有的AI导师系统中

### 检查流程
1. 用户输入消息
2. 预处理检查边界条件
3. 如果违规，直接返回警告
4. 如果正常，继续处理并发送给LLM

### 系统提示词增强
- 自动在系统提示词中添加边界条件
- 确保LLM理解并遵守边界规则

## 优势

1. **提升用户体验**：清晰的指导和友好的提示
2. **减少混淆**：避免跨主题的错误回答
3. **提高准确性**：确保回答与当前主题相关
4. **节省Token**：拦截无效问题，减少不必要的API调用
5. **教育价值**：帮助用户理解不同主题的边界

## 总结

AI导师边界检查系统显著提升了用户体验，确保用户在正确的上下文中获得准确的指导。通过智能的边界检测和友好的提示，用户可以更高效地学习和探索不同的模拟主题。
