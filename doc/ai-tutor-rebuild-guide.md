### Phase 4: 控件操作集成
为五个主题的页面设计页面控件的操作接口函数，调用这些函数即可实现对页面控件元素的控制。

以下规划的所有接口函数，其输入输出设计均应遵循以下结构：
- **输入：**
  - （必须）具体控制指令，比如`start_simulation`, `stepwise_simulation`, `stop_simulation`, `pause_simulation` 等
  - （可选）某些状态参数，需要它们来确定状态更改操作，比如当我们启动/暂停仿真运行的时候，需要结合当前仿真运行状态来判断是否需要进行操作，因此就需要将当前状态作为输入参数，`current_state`等
- **输出：**
  - 对需要返回当前状态的情景，需要判断接口函数的操作是否生效，如`simulation_is_running`, `simulation_is_paused`等等

以下分别为五个主题页的中所用的页面控件元素接口功能描述：

##### **1. 生命游戏页面** - 实现完整的网页控件元素操作控制接口

  - **基础控制类：**
    - 仿真的「开始」、「暂停」控制函数，对应同一个按钮的两个不同状态，函数用来实现仿真工作开始和暂停的切换功能，并返回仿真实际的运行状态！
    - 「单步运行」控制函数，循环调用此函数可以实现连续单步运行
    - 「重置」仿真函数，点击后主画布恢复最初的状态
  - **参数设置类：**
    - 「模拟速度」控制函数，用来改变滑杆数值，需要返回滑杆的实际值
    - 「环形边界」控制函数，用来改变滑杆数值，需要返回滑杆的实际值
    - 「生命规则」四个子选项控制函数，用来改变每个滑杆数值，需要返回滑杆的实际值
    - 「预设图案」选择函数，改变下拉菜单的选项，需要返回下拉菜单的实际选项
  - **状态查询类：**
    - 当前运行状态、代数、活细胞数量等必要参数的查询函数，需要返回这些参数的实时取值

##### **2. Boids仿真页面** - 实现完整的网页控件元素操作控制接口

  - **基础控制类：**
    - 仿真的「开始」、「暂停」控制函数，对应同一个按钮的两个不同状态，函数用来实现仿真工作开始和暂停的切换功能，并返回仿真实际的运行状态！
    - 「单步运行」控制函数，循环调用此函数可以实现连续单步运行
    - 「重置」仿真函数，点击后主画布恢复最初的状态
  - **参数设置类：**
    - 「鸟群数量」控制函数，用来改变滑杆数值，需要返回滑杆的实际值
    - 「分离权重」控制函数，用来改变滑杆数值，需要返回滑杆的实际值
    - 「对齐权重」控制函数，用来改变滑杆数值，需要返回滑杆的实际值
    - 「聚合权重」控制函数，用来改变滑杆数值，需要返回滑杆的实际值
    - 「最大速度」控制函数，用来改变滑杆数值，需要返回滑杆的实际值
    - 「感知半径」控制函数，用来改变滑杆数值，需要返回滑杆的实际值
  - **状态查询类：**
    - 当前运行状态、鸟群数量、各权重参数、速度和感知半径等必要参数的查询函数，需要返回这些参数的实时取值

##### **3. Ising模型页面** - 实现完整的网页控件元素操作控制接口

  - **基础控制类：**
    - 仿真的「开始」、「暂停」控制函数，对应同一个按钮的两个不同状态，函数用来实现仿真工作开始和暂停的切换功能，并返回仿真实际的运行状态！
    - 「单步运行」控制函数，循环调用此函数可以实现连续单步运行
    - 「重置」仿真函数，点击后主画布恢复最初的状态
  - **参数设置类：**
    - 「温度」控制函数，用来改变滑杆数值，需要返回滑杆的实际值
    - 「外磁场」控制函数，用来改变滑杆数值，需要返回滑杆的实际值
    - 「模拟速度」控制函数，用来改变滑杆数值，需要返回滑杆的实际值
  - **特殊操作类：**
    - 「随机化」控制函数，将所有自旋状态随机化
    - 「全部向上」控制函数，将所有自旋设置为向上状态
    - 「全部向下」控制函数，将所有自旋设置为向下状态
  - **状态查询类：**
    - 当前运行状态、温度、磁化强度、能量等必要参数的查询函数，需要返回这些参数的实时取值

##### **4. Physarum仿真页面** - 实现完整的网页控件元素操作控制接口

  - **基础控制类：**
    - 仿真的「开始」、「暂停」控制函数，对应同一个按钮的两个不同状态，函数用来实现仿真工作开始和暂停的切换功能，并返回仿真实际的运行状态！
    - 「单步运行」控制函数，循环调用此函数可以实现连续单步运行
    - 「重置」仿真函数，点击后主画布恢复最初的状态
  - **参数设置类：**
    - 「代理数量」控制函数，用来改变滑杆数值，需要返回滑杆的实际值
    - 「传感器角度」控制函数，用来改变滑杆数值，需要返回滑杆的实际值
    - 「传感器距离」控制函数，用来改变滑杆数值，需要返回滑杆的实际值
    - 「信息素沉积量」控制函数，用来改变滑杆数值，需要返回滑杆的实际值
    - 「信息素衰减率」控制函数，用来改变滑杆数值，需要返回滑杆的实际值
    - 「代理步长」控制函数，用来改变滑杆数值，需要返回滑杆的实际值
  - **状态查询类：**
    - 当前运行状态、代理数量、传感器参数、信息素参数等必要参数的查询函数，需要返回这些参数的实时取值

##### **5. Schelling模型页面** - 实现完整的网页控件元素操作控制接口

  - **基础控制类：**
    - 仿真的「开始」、「暂停」控制函数，对应同一个按钮的两个不同状态，函数用来实现仿真工作开始和暂停的切换功能，并返回仿真实际的运行状态！
    - 「单步运行」控制函数，循环调用此函数可以实现连续单步运行
    - 「重置」仿真函数，点击后主画布恢复最初的状态
  - **参数设置类：**
    - 「相似性阈值」控制函数，用来改变滑杆数值，需要返回滑杆的实际值
    - 「人口密度」控制函数，用来改变滑杆数值，需要返回滑杆的实际值
    - 「群体比例」控制函数，用来改变滑杆数值，需要返回滑杆的实际值
  - **状态查询类：**
    - 当前运行状态、相似性阈值、分离指数、满意度等必要参数的查询函数，需要返回这些参数的实时取值