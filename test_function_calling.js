#!/usr/bin/env node

/**
 * Function Calling快速测试脚本
 * 验证AI导师控制功能修复后是否正常工作
 */

const { FunctionCallExecutor } = require('./src/components/ai-tutor/FunctionCallExecutor.ts');
const { generateSimulationControlFunctions, parseFunctionName, isValidFunctionForSimulation } = require('./src/components/ai-tutor/FunctionDefinitions.ts');

async function testFunctionCalling() {
  console.log('🧪 开始测试Function Calling修复...');
  
  // 测试函数名解析
  console.log('\n📋 测试函数名解析:');
  const testCases = [
    'game_of_life_start',
    'game_of_life_set_speed',
    'boids_start',
    'ising_model_start'
  ];
  
  testCases.forEach(functionName => {
    const parsed = parseFunctionName(functionName);
    console.log(`  ${functionName} -> `, parsed);
  });
  
  // 测试函数验证
  console.log('\n✅ 测试函数验证:');
  const validationTests = [
    { functionName: 'game_of_life_start', simulationType: 'game-of-life' },
    { functionName: 'game_of_life_set_speed', simulationType: 'game-of-life' },
    { functionName: 'boids_start', simulationType: 'boids' }
  ];
  
  validationTests.forEach(({ functionName, simulationType }) => {
    const isValid = isValidFunctionForSimulation(functionName, simulationType);
    console.log(`  ${functionName} 对于 ${simulationType}: ${isValid ? '✅ 有效' : '❌ 无效'}`);
  });
  
  // 测试函数生成
  console.log('\n🔧 测试函数生成:');
  const gameOfLifeFunctions = generateSimulationControlFunctions('game-of-life');
  console.log(`  生成了 ${gameOfLifeFunctions.length} 个生命游戏函数:`);
  gameOfLifeFunctions.slice(0, 5).forEach(func => {
    console.log(`    - ${func.name}: ${func.description}`);
  });
  
  console.log('\n🎉 Function Calling测试完成!');
}

if (require.main === module) {
  testFunctionCalling().catch(console.error);
}

module.exports = { testFunctionCalling };
