// 测试速度参数修复
console.log("=== 生命游戏速度参数测试 ===");

// 测试速度转换函数
function testSpeedConversion() {
  console.log("百分比 -> 毫秒转换:");
  
  // 测试不同百分比值
  const testCases = [1, 25, 50, 75, 100];
  
  testCases.forEach(percentage => {
    const milliseconds = 500 - (percentage * 4.9);
    const backToPercentage = Math.round((500 - milliseconds) / 4.9);
    console.log(`${percentage}% -> ${milliseconds}ms -> ${backToPercentage}%`);
  });
  
  console.log("\n控制面板显示百分比计算:");
  testCases.forEach(percentage => {
    const milliseconds = 500 - (percentage * 4.9);
    const displayPercentage = Math.round((500 - milliseconds) / 4.9);
    console.log(`${percentage}% -> ${milliseconds}ms -> 显示: ${displayPercentage}%`);
  });
}

testSpeedConversion();

console.log("\n=== 其他仿真类型速度参数检查 ===");

// 检查不同仿真类型的速度参数范围
const simulationTypes = {
  "生命游戏": { min: 10, max: 500, unit: "ms", conversion: "反向" },
  "Boids": { min: 0.5, max: 5, unit: "unit/frame", conversion: "直接" },
  "Ising": { min: 1, max: 20, unit: "MCS/frame", conversion: "直接" }, 
  "Physarum": { min: 0.5, max: 3, unit: "unit/frame", conversion: "直接" },
  "Schelling": { value: 1, unit: "固定值", conversion: "无" }
};

Object.entries(simulationTypes).forEach(([name, params]) => {
  console.log(`${name}: ${JSON.stringify(params)}`);
});
