<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>多周期指令分解测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-result {
            background: #f8f9fa;
            padding: 15px;
            margin: 10px 0;
            border-left: 4px solid #007bff;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        .success { border-left-color: #28a745; }
        .error { border-left-color: #dc3545; }
        .warning { border-left-color: #ffc107; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        button:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }
        .cycle-status {
            display: inline-block;
            padding: 5px 10px;
            border-radius: 3px;
            margin: 5px;
            font-weight: bold;
        }
        .cycle-pending { background: #ffc107; color: #000; }
        .cycle-running { background: #17a2b8; color: #fff; }
        .cycle-success { background: #28a745; color: #fff; }
        .cycle-failed { background: #dc3545; color: #fff; }
    </style>
</head>
<body>
    <h1>多周期指令分解测试</h1>
    <p>测试指令分解系统是否能够正确处理多个连续的复杂指令分解周期</p>
    
    <div class="test-section">
        <h2>测试状态</h2>
        <div id="cycle-status">
            <span class="cycle-status cycle-pending">周期1: 待测试</span>
            <span class="cycle-status cycle-pending">周期2: 待测试</span>
            <span class="cycle-status cycle-pending">周期3: 待测试</span>
        </div>
    </div>
    
    <div class="test-section">
        <h2>测试控制</h2>
        <button id="start-test" onclick="startMultiCycleTest()">开始多周期测试</button>
        <button id="reset-test" onclick="resetTest()">重置测试</button>
        <button id="single-cycle" onclick="testSingleCycle()">测试单个周期</button>
    </div>
    
    <div class="test-section">
        <h2>测试结果</h2>
        <div id="test-results" class="test-result"></div>
    </div>
    
    <div class="test-section">
        <h2>详细日志</h2>
        <div id="detailed-logs" class="test-result"></div>
    </div>

    <script>
        // 模拟指令分解器
        class TestInstructionDecomposer {
            constructor() {
                this.currentInstructions = null;
                this.reset();
            }
            
            reset() {
                console.log('[测试分解器] 🔧 重置状态');
                this.currentInstructions = null;
            }
            
            analyzeUserMessage(message, toolCalls) {
                this.log(`[分析] 消息: "${message}"`);
                this.log(`[分析] 函数调用数量: ${toolCalls.length}`);
                
                if (toolCalls.length <= 1) {
                    return { needsDecomposition: false };
                }
                
                const hasParameter = toolCalls.some(call => 
                    call.function.name.includes('set_')
                );
                const hasControl = toolCalls.some(call => 
                    call.function.name.includes('start') || 
                    call.function.name.includes('reset')
                );
                
                if (hasParameter && hasControl) {
                    return {
                        needsDecomposition: true,
                        reason: '检测到参数设置和控制操作的组合'
                    };
                }
                
                return { needsDecomposition: false };
            }
            
            decomposeInstructions(message, toolCalls) {
                this.log(`[分解] 开始分解指令`);
                
                // 清除之前的状态
                this.currentInstructions = null;
                
                const steps = toolCalls.map((call, index) => ({
                    id: `step_${index}`,
                    description: this.getStepDescription(call),
                    toolCalls: [call],
                    status: 'pending'
                }));
                
                this.currentInstructions = {
                    id: `inst_${Date.now()}`,
                    originalMessage: message,
                    steps,
                    currentStepIndex: 0,
                    isCompleted: false,
                    createdAt: Date.now()
                };
                
                this.log(`[分解] 分解为 ${steps.length} 个步骤`);
                return this.currentInstructions;
            }
            
            getCurrentStep() {
                if (!this.currentInstructions || this.currentInstructions.isCompleted) {
                    return null;
                }
                
                const index = this.currentInstructions.currentStepIndex;
                if (index >= this.currentInstructions.steps.length) {
                    return null;
                }
                
                return this.currentInstructions.steps[index];
            }
            
            completeCurrentStep(result) {
                if (!this.currentInstructions) {
                    return { hasNextStep: false, isAllCompleted: true };
                }
                
                const currentIndex = this.currentInstructions.currentStepIndex;
                const currentStep = this.currentInstructions.steps[currentIndex];
                
                if (currentStep) {
                    currentStep.status = 'completed';
                    currentStep.result = result;
                    currentStep.timestamp = Date.now();
                }
                
                this.currentInstructions.currentStepIndex++;
                
                const nextIndex = this.currentInstructions.currentStepIndex;
                const hasNextStep = nextIndex < this.currentInstructions.steps.length;
                
                if (!hasNextStep) {
                    this.currentInstructions.isCompleted = true;
                }
                
                return {
                    hasNextStep,
                    nextStep: hasNextStep ? this.currentInstructions.steps[nextIndex] : undefined,
                    isAllCompleted: !hasNextStep
                };
            }
            
            clearCurrentInstructions() {
                this.log(`[清理] 清除当前指令状态`);
                this.currentInstructions = null;
            }
            
            getStepDescription(toolCall) {
                const name = toolCall.function.name;
                if (name.includes('set_grid_size')) return '设置网格大小';
                if (name.includes('set_similarity')) return '设置相似性阈值';
                if (name.includes('start')) return '启动仿真';
                if (name.includes('reset')) return '重置仿真';
                return `执行 ${name}`;
            }
            
            log(message) {
                console.log(message);
                const logElement = document.getElementById('detailed-logs');
                if (logElement) {
                    logElement.textContent += message + '\n';
                    logElement.scrollTop = logElement.scrollHeight;
                }
            }
        }
        
        // 测试数据
        const testCycles = [
            {
                name: '周期1: 设置网格+启动仿真',
                message: '设置晶格数量为150，然后运行仿真',
                toolCalls: [
                    { id: 'call_1', type: 'function', function: { name: 'schelling_set_grid_size', arguments: '{"size": 150}' }},
                    { id: 'call_2', type: 'function', function: { name: 'schelling_start', arguments: '{}' }}
                ]
            },
            {
                name: '周期2: 设置阈值+重置',
                message: '设置相似性阈值为0.8，然后重置仿真',
                toolCalls: [
                    { id: 'call_3', type: 'function', function: { name: 'schelling_set_similarity_threshold', arguments: '{"threshold": 0.8}' }},
                    { id: 'call_4', type: 'function', function: { name: 'schelling_reset', arguments: '{}' }}
                ]
            },
            {
                name: '周期3: 多参数设置+启动',
                message: '设置网格大小为200，相似性阈值为0.7，然后启动仿真',
                toolCalls: [
                    { id: 'call_5', type: 'function', function: { name: 'schelling_set_grid_size', arguments: '{"size": 200}' }},
                    { id: 'call_6', type: 'function', function: { name: 'schelling_set_similarity_threshold', arguments: '{"threshold": 0.7}' }},
                    { id: 'call_7', type: 'function', function: { name: 'schelling_start', arguments: '{}' }}
                ]
            }
        ];
        
        let decomposer = new TestInstructionDecomposer();
        let currentCycleIndex = 0;
        let testResults = [];
        
        function updateCycleStatus(cycleIndex, status) {
            const statusElements = document.querySelectorAll('.cycle-status');
            if (statusElements[cycleIndex]) {
                statusElements[cycleIndex].className = `cycle-status cycle-${status}`;
                statusElements[cycleIndex].textContent = `周期${cycleIndex + 1}: ${getStatusText(status)}`;
            }
        }
        
        function getStatusText(status) {
            const statusMap = {
                'pending': '待测试',
                'running': '测试中',
                'success': '成功',
                'failed': '失败'
            };
            return statusMap[status] || status;
        }
        
        function logResult(message, isError = false) {
            const resultElement = document.getElementById('test-results');
            const timestamp = new Date().toLocaleTimeString();
            resultElement.textContent += `[${timestamp}] ${message}\n`;
            resultElement.scrollTop = resultElement.scrollHeight;
            
            if (isError) {
                resultElement.className = 'test-result error';
            }
        }
        
        async function testSingleCycle(cycleIndex = 0) {
            const cycle = testCycles[cycleIndex];
            logResult(`\n=== 开始测试 ${cycle.name} ===`);
            
            updateCycleStatus(cycleIndex, 'running');
            
            try {
                // 1. 分析指令
                const analysis = decomposer.analyzeUserMessage(cycle.message, cycle.toolCalls);
                logResult(`分析结果: ${analysis.needsDecomposition ? '需要分解' : '不需要分解'}`);
                
                if (!analysis.needsDecomposition) {
                    throw new Error('应该需要分解但分析结果为不需要分解');
                }
                
                // 2. 分解指令
                const decomposed = decomposer.decomposeInstructions(cycle.message, cycle.toolCalls);
                logResult(`分解为 ${decomposed.steps.length} 个步骤`);
                
                // 3. 模拟执行每个步骤
                let stepCount = 0;
                while (true) {
                    const currentStep = decomposer.getCurrentStep();
                    if (!currentStep) break;
                    
                    stepCount++;
                    logResult(`执行步骤 ${stepCount}: ${currentStep.description}`);
                    
                    // 模拟执行结果
                    const mockResult = [{ success: true, message: `${currentStep.description} 执行成功` }];
                    
                    // 完成步骤
                    const progress = decomposer.completeCurrentStep(mockResult);
                    logResult(`步骤 ${stepCount} 完成，${progress.hasNextStep ? '有下一步' : '所有步骤完成'}`);
                    
                    if (!progress.hasNextStep) break;
                    
                    // 模拟用户确认延迟
                    await new Promise(resolve => setTimeout(resolve, 100));
                }
                
                // 4. 清理状态
                decomposer.clearCurrentInstructions();
                
                updateCycleStatus(cycleIndex, 'success');
                logResult(`${cycle.name} 测试成功！`);
                
                return true;
                
            } catch (error) {
                updateCycleStatus(cycleIndex, 'failed');
                logResult(`${cycle.name} 测试失败: ${error.message}`, true);
                return false;
            }
        }
        
        async function startMultiCycleTest() {
            document.getElementById('start-test').disabled = true;
            
            logResult('开始多周期指令分解测试...');
            
            let allPassed = true;
            
            for (let i = 0; i < testCycles.length; i++) {
                const success = await testSingleCycle(i);
                if (!success) {
                    allPassed = false;
                    break;
                }
                
                // 在周期之间添加延迟
                await new Promise(resolve => setTimeout(resolve, 500));
            }
            
            logResult(`\n=== 测试完成 ===`);
            logResult(`总体结果: ${allPassed ? '✅ 所有周期测试通过' : '❌ 存在失败的周期'}`, !allPassed);
            
            document.getElementById('start-test').disabled = false;
        }
        
        function resetTest() {
            // 重置分解器
            decomposer = new TestInstructionDecomposer();
            
            // 重置UI状态
            for (let i = 0; i < 3; i++) {
                updateCycleStatus(i, 'pending');
            }
            
            // 清空日志
            document.getElementById('test-results').textContent = '';
            document.getElementById('detailed-logs').textContent = '';
            document.getElementById('test-results').className = 'test-result';
            
            logResult('测试已重置');
        }
        
        // 页面加载时初始化
        window.addEventListener('load', () => {
            logResult('多周期指令分解测试系统已就绪');
        });
    </script>
</body>
</html>
