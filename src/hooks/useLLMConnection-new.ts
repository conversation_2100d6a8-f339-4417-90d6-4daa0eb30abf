import { useState, useCallback, useRef } from 'react';
import type { 
  UseLLMConnectionReturn,
  ConnectionStatus,
  LLMConfig,
  LLMResponse,
  LLMStreamResponse,
  ConnectionTestResult,
  FunctionDefinition
} from '@/types/ai-tutor';
import { createLLMClient, LLMClient } from '@/utils/llm-client';

/**
 * LLM连接管理Hook
 * 处理与LLM服务的连接、消息发送和状态管理（支持Function Calling）
 */
export function useLLMConnection(): UseLLMConnectionReturn {
  const [status, setStatus] = useState<ConnectionStatus>('disconnected');
  const clientRef = useRef<LLMClient | null>(null);
  const [currentConfig, setCurrentConfig] = useState<LLMConfig | null>(null);

  /**
   * 初始化或更新LLM客户端
   */
  const initializeClient = useCallback((config: LLMConfig) => {
    try {
      if (clientRef.current) {
        clientRef.current.updateConfig(config);
      } else {
        clientRef.current = createLLMClient(config);
      }
      setCurrentConfig(config);
      return true;
    } catch (error) {
      console.error('初始化LLM客户端失败:', error);
      return false;
    }
  }, []);

  /**
   * 发送消息并获取完整响应（支持Function Calling）
   */
  const sendMessage = useCallback(async (
    message: string, 
    config: LLMConfig,
    functions?: FunctionDefinition[]
  ): Promise<LLMResponse> => {
    if (!initializeClient(config)) {
      throw new Error('LLM客户端初始化失败');
    }

    const client = clientRef.current!;
    setStatus('connecting');

    try {
      const messages = [
        { role: 'user' as const, content: message }
      ];

      const response = await client.sendMessage(messages, config.systemPrompt, functions);
      setStatus('connected');
      return response;
    } catch (error) {
      setStatus('error');
      throw error;
    }
  }, [initializeClient]);

  /**
   * 发送消息并获取流式响应（支持Function Calling）
   */
  const sendStreamMessage = useCallback(async (
    message: string,
    config: LLMConfig,
    onChunk: (chunk: LLMStreamResponse) => void,
    functions?: FunctionDefinition[]
  ): Promise<void> => {
    if (!initializeClient(config)) {
      throw new Error('LLM客户端初始化失败');
    }

    const client = clientRef.current!;
    setStatus('connecting');

    try {
      const messages = [
        { role: 'user' as const, content: message }
      ];

      await client.sendStreamMessage(messages, config.systemPrompt, (chunk) => {
        setStatus('connected');
        onChunk(chunk);
      }, functions);
    } catch (error) {
      setStatus('error');
      throw error;
    }
  }, [initializeClient]);

  /**
   * 发送带历史的消息（支持Function Calling）
   */
  const sendMessageWithHistory = useCallback(async (
    messages: Array<{ role: 'user' | 'assistant' | 'system' | 'tool'; content: string; tool_calls?: any[]; tool_call_id?: string }>,
    config: LLMConfig,
    functions?: FunctionDefinition[]
  ): Promise<LLMResponse> => {
    if (!initializeClient(config)) {
      throw new Error('LLM客户端初始化失败');
    }

    const client = clientRef.current!;
    setStatus('connecting');

    try {
      const response = await client.sendMessage(messages, config.systemPrompt, functions);
      setStatus('connected');
      return response;
    } catch (error) {
      setStatus('error');
      throw error;
    }
  }, [initializeClient]);

  /**
   * 发送带历史的流式消息（支持Function Calling）
   */
  const sendStreamMessageWithHistory = useCallback(async (
    messages: Array<{ role: 'user' | 'assistant' | 'system' | 'tool'; content: string; tool_calls?: any[]; tool_call_id?: string }>,
    config: LLMConfig,
    onChunk: (chunk: LLMStreamResponse) => void,
    functions?: FunctionDefinition[]
  ): Promise<void> => {
    if (!initializeClient(config)) {
      throw new Error('LLM客户端初始化失败');
    }

    const client = clientRef.current!;
    setStatus('connecting');

    try {
      await client.sendStreamMessage(messages, config.systemPrompt, (chunk) => {
        setStatus('connected');
        onChunk(chunk);
      }, functions);
    } catch (error) {
      setStatus('error');
      throw error;
    }
  }, [initializeClient]);

  /**
   * 测试连接
   */
  const testConnection = useCallback(async (config: LLMConfig): Promise<ConnectionTestResult> => {
    if (!initializeClient(config)) {
      return {
        success: false,
        error: 'LLM客户端初始化失败'
      };
    }

    const client = clientRef.current!;
    setStatus('connecting');

    try {
      const result = await client.testConnection();
      setStatus(result.success ? 'connected' : 'error');
      return result;
    } catch (error) {
      setStatus('error');
      return {
        success: false,
        error: error instanceof Error ? error.message : '连接测试失败'
      };
    }
  }, [initializeClient]);

  /**
   * 取消当前请求
   */
  const cancelRequest = useCallback(() => {
    if (clientRef.current) {
      clientRef.current.abort();
    }
    setStatus('disconnected');
  }, []);

  return {
    status,
    sendMessage,
    sendMessageWithHistory,
    sendStreamMessage,
    sendStreamMessageWithHistory,
    testConnection,
    cancelRequest
  };
}
