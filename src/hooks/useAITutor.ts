import { useState, useCallback, useEffect } from 'react';
import type { 
  UseAITutorReturn, 
  AITutorState, 
  LLMConfig, 
  ConnectionTestResult,
  TutorMode,
  SimulationContext,
  ControlAction
} from '@/types/ai-tutor';
import { APIKeyManager, ChatHistoryManager } from '@/utils/encryption';

/**
 * AI导师核心逻辑Hook
 * 集成安全存储和状态管理
 */
export function useAITutor(): UseAITutorReturn {
  const [state, setState] = useState<AITutorState>({
    isConfigured: false,
    isConnected: false,
    isLoading: false,
    currentMode: 'introduction',
    simulationContext: null,
    chatHistory: null,
    config: null,
    error: null,
  });

  // 初始化时加载保存的配置
  useEffect(() => {
    const loadStoredConfig = () => {
      try {
        const storedConfig = APIKeyManager.retrieve();
        if (storedConfig) {
          setState(prev => ({
            ...prev,
            config: storedConfig,
            isConfigured: true
          }));
        }

        const chatHistory = ChatHistoryManager.load();
        if (chatHistory.length > 0) {
          setState(prev => ({
            ...prev,
            chatHistory: {
              messages: chatHistory,
              sessionId: 'main',
              createdAt: Date.now(),
              updatedAt: Date.now()
            }
          }));
        }
      } catch (error) {
        console.error('加载存储配置失败:', error);
        setState(prev => ({
          ...prev,
          error: '配置加载失败，请重新设置'
        }));
      }
    };

    loadStoredConfig();
  }, []);

  const sendMessage = useCallback(async (message: string) => {
    if (!state.config) {
      setState(prev => ({ ...prev, error: '请先配置LLM连接' }));
      return;
    }

    setState(prev => ({ ...prev, isLoading: true, error: null }));
    
    try {
      // 这里将集成真实的LLM API调用
      // 目前是占位符实现
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const newMessage = {
        id: Date.now().toString(),
        role: 'user' as const,
        content: message,
        timestamp: Date.now()
      };

      const botResponse = {
        id: (Date.now() + 1).toString(),
        role: 'assistant' as const,
        content: `这是一个AI导师的回复示例。您说了: "${message}"`,
        timestamp: Date.now() + 1
      };

      const updatedMessages = [
        ...(state.chatHistory?.messages || []),
        newMessage,
        botResponse
      ];

      // 保存到本地存储
      ChatHistoryManager.save(updatedMessages);

      setState(prev => ({
        ...prev,
        isLoading: false,
        chatHistory: {
          messages: updatedMessages,
          sessionId: prev.chatHistory?.sessionId || 'main',
          createdAt: prev.chatHistory?.createdAt || Date.now(),
          updatedAt: Date.now()
        }
      }));
    } catch (error) {
      setState(prev => ({ 
        ...prev, 
        isLoading: false,
        error: error instanceof Error ? error.message : '发送消息失败'
      }));
    }
  }, [state.config, state.chatHistory]);

  const clearHistory = useCallback(() => {
    ChatHistoryManager.clear();
    setState(prev => ({ ...prev, chatHistory: null }));
  }, []);

  const updateConfig = useCallback((config: Partial<LLMConfig>) => {
    try {
      const newConfig = state.config ? { ...state.config, ...config } : config as LLMConfig;
      
      // 保存到安全存储
      APIKeyManager.store(newConfig);
      
      setState(prev => ({
        ...prev,
        config: newConfig,
        isConfigured: true,
        error: null
      }));
    } catch (error) {
      setState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : '配置保存失败'
      }));
    }
  }, [state.config]);

  const testConnection = useCallback(async (config?: LLMConfig): Promise<ConnectionTestResult> => {
    const testConfig = config || state.config;
    
    if (!testConfig) {
      return {
        success: false,
        error: '没有可用的配置'
      };
    }

    setState(prev => ({ ...prev, isLoading: true, error: null }));
    
    try {
      // 这里将实现真实的连接测试
      // 目前是模拟实现
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      // 模拟随机的成功/失败
      const success = Math.random() > 0.2; // 80%成功率
      
      const result: ConnectionTestResult = success ? {
        success: true,
        latency: Math.floor(Math.random() * 500) + 100,
        modelInfo: {
          name: testConfig.model,
          capabilities: ['text-generation', 'conversation']
        }
      } : {
        success: false,
        error: '连接超时或API密钥无效'
      };
      
      setState(prev => ({ 
        ...prev, 
        isLoading: false,
        isConnected: result.success,
        error: result.success ? null : result.error || '连接失败'
      }));
      
      return result;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '连接测试失败';
      
      setState(prev => ({ 
        ...prev, 
        isLoading: false,
        isConnected: false,
        error: errorMessage
      }));
      
      return {
        success: false,
        error: errorMessage
      };
    }
  }, [state.config]);

  const setMode = useCallback((mode: TutorMode) => {
    setState(prev => ({ ...prev, currentMode: mode }));
  }, []);

  const setSimulationContext = useCallback((context: SimulationContext) => {
    setState(prev => ({ ...prev, simulationContext: context }));
  }, []);

  const executeControlAction = useCallback(async (action: ControlAction) => {
    setState(prev => ({ ...prev, isLoading: true }));
    
    try {
      // 这里将实现真实的控件操作
      // 目前是占位符实现
      await new Promise(resolve => setTimeout(resolve, 500));
      
      console.log('执行控件操作:', action);
      
      setState(prev => ({ ...prev, isLoading: false }));
    } catch (error) {
      setState(prev => ({ 
        ...prev, 
        isLoading: false,
        error: error instanceof Error ? error.message : '控件操作失败'
      }));
    }
  }, []);

  // 清除所有数据
  const clearAllData = useCallback(() => {
    APIKeyManager.clear();
    ChatHistoryManager.clear();
    setState({
      isConfigured: false,
      isConnected: false,
      isLoading: false,
      currentMode: 'introduction',
      simulationContext: null,
      chatHistory: null,
      config: null,
      error: null,
    });
  }, []);

  return {
    state,
    sendMessage,
    clearHistory,
    updateConfig,
    testConnection,
    setMode,
    setSimulationContext,
    executeControlAction,
    clearAllData
  };
}
