/**
 * AI导师演示运行器Hook
 * 处理演示脚本的执行和控制
 */

import { useState, useCallback, useRef } from 'react';
import { useAISimulationControl } from '@/hooks/useAISimulationControl';
import type { DemoScript, DemoStep } from '@/components/ai-tutor/DemoScripts';
import type { AIControlResult } from '@/types/simulation-controls';

export interface DemoState {
  isRunning: boolean;
  currentDemo: DemoScript | null;
  currentStepIndex: number;
  isPaused: boolean;
  results: AIControlResult[];
}

export const useAIDemoRunner = () => {
  const { executeCommands } = useAISimulationControl();
  
  const [demoState, setDemoState] = useState<DemoState>({
    isRunning: false,
    currentDemo: null,
    currentStepIndex: -1,
    isPaused: false,
    results: []
  });

  const timeoutRef = useRef<NodeJS.Timeout>();
  const callbackRef = useRef<{
    onStepStart?: (step: DemoStep, index: number) => void;
    onStepComplete?: (step: DemoStep, index: number, results: AIControlResult[]) => void;
    onDemoComplete?: (demo: DemoScript) => void;
    onDemoError?: (error: Error, step?: DemoStep) => void;
  }>({});

  /**
   * 开始演示
   */
  const startDemo = useCallback(async (demo: DemoScript, callbacks?: typeof callbackRef.current) => {
    if (demoState.isRunning) {
      console.warn('演示已在运行中');
      return;
    }

    // 设置回调
    callbackRef.current = callbacks || {};

    // 初始化状态
    setDemoState({
      isRunning: true,
      currentDemo: demo,
      currentStepIndex: 0,
      isPaused: false,
      results: []
    });

    console.log(`[AI演示] 开始演示: ${demo.title}`);
    
    // 开始执行第一步
    try {
      await executeStep(demo, 0);
    } catch (error) {
      console.error('[AI演示] 启动演示失败:', error);
      callbackRef.current.onDemoError?.(error instanceof Error ? error : new Error('未知错误'));
      stopDemo();
    }
  }, [demoState.isRunning]);

  /**
   * 执行单个演示步骤
   */
  const executeStep = useCallback(async (demo: DemoScript, stepIndex: number) => {
    if (stepIndex >= demo.steps.length) {
      // 演示完成
      console.log(`[AI演示] 演示完成: ${demo.title}`);
      callbackRef.current.onDemoComplete?.(demo);
      setDemoState(prev => ({ ...prev, isRunning: false }));
      return;
    }

    const step = demo.steps[stepIndex];
    console.log(`[AI演示] 执行步骤 ${stepIndex + 1}/${demo.steps.length}: ${step.title}`);
    
    try {
      // 通知步骤开始
      callbackRef.current.onStepStart?.(step, stepIndex);
      
      // 执行控制命令
      const results = await executeCommands(step.commands);
      
      // 更新状态
      setDemoState(prev => ({
        ...prev,
        currentStepIndex: stepIndex,
        results: [...prev.results, ...results]
      }));
      
      // 通知步骤完成
      callbackRef.current.onStepComplete?.(step, stepIndex, results);
      
      // 如果需要等待用户输入，暂停
      if (step.waitForUserInput) {
        console.log(`[AI演示] 等待用户输入...`);
        setDemoState(prev => ({ ...prev, isPaused: true }));
        return;
      }
      
      // 设置下一步的延迟
      const delay = step.duration || 2000;
      timeoutRef.current = setTimeout(() => {
        executeStep(demo, stepIndex + 1);
      }, delay);
      
    } catch (error) {
      console.error(`[AI演示] 步骤执行失败:`, error);
      callbackRef.current.onDemoError?.(error instanceof Error ? error : new Error('步骤执行失败'), step);
      stopDemo();
    }
  }, [executeCommands]);

  /**
   * 继续演示（从暂停状态）
   */
  const continueDemo = useCallback(() => {
    if (!demoState.currentDemo || !demoState.isPaused) {
      return;
    }

    console.log('[AI演示] 继续演示');
    setDemoState(prev => ({ ...prev, isPaused: false }));
    
    // 继续下一步
    const nextStepIndex = demoState.currentStepIndex + 1;
    executeStep(demoState.currentDemo, nextStepIndex);
  }, [demoState.currentDemo, demoState.isPaused, demoState.currentStepIndex, executeStep]);

  /**
   * 暂停演示
   */
  const pauseDemo = useCallback(() => {
    if (!demoState.isRunning || demoState.isPaused) {
      return;
    }

    console.log('[AI演示] 暂停演示');
    
    // 清除定时器
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = undefined;
    }
    
    setDemoState(prev => ({ ...prev, isPaused: true }));
  }, [demoState.isRunning, demoState.isPaused]);

  /**
   * 停止演示
   */
  const stopDemo = useCallback(() => {
    console.log('[AI演示] 停止演示');
    
    // 清除定时器
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = undefined;
    }
    
    // 重置状态
    setDemoState({
      isRunning: false,
      currentDemo: null,
      currentStepIndex: -1,
      isPaused: false,
      results: []
    });
    
    // 清除回调
    callbackRef.current = {};
  }, []);

  /**
   * 跳到下一步
   */
  const nextStep = useCallback(() => {
    if (!demoState.currentDemo || !demoState.isRunning) {
      return;
    }

    // 清除当前定时器
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = undefined;
    }

    const nextStepIndex = demoState.currentStepIndex + 1;
    console.log(`[AI演示] 跳转到步骤 ${nextStepIndex + 1}`);
    
    setDemoState(prev => ({ ...prev, isPaused: false }));
    executeStep(demoState.currentDemo, nextStepIndex);
  }, [demoState.currentDemo, demoState.isRunning, demoState.currentStepIndex, executeStep]);

  /**
   * 重新开始当前演示
   */
  const restartDemo = useCallback(() => {
    if (!demoState.currentDemo) {
      return;
    }

    const demo = demoState.currentDemo;
    stopDemo();
    
    // 短暂延迟后重新开始
    setTimeout(() => {
      startDemo(demo, callbackRef.current);
    }, 500);
  }, [demoState.currentDemo, stopDemo, startDemo]);

  /**
   * 获取当前步骤信息
   */
  const getCurrentStep = useCallback((): DemoStep | null => {
    if (!demoState.currentDemo || demoState.currentStepIndex < 0) {
      return null;
    }
    
    return demoState.currentDemo.steps[demoState.currentStepIndex] || null;
  }, [demoState.currentDemo, demoState.currentStepIndex]);

  /**
   * 获取演示进度
   */
  const getProgress = useCallback((): { current: number; total: number; percentage: number } => {
    if (!demoState.currentDemo) {
      return { current: 0, total: 0, percentage: 0 };
    }
    
    const current = Math.max(0, demoState.currentStepIndex + 1);
    const total = demoState.currentDemo.steps.length;
    const percentage = total > 0 ? (current / total) * 100 : 0;
    
    return { current, total, percentage };
  }, [demoState.currentDemo, demoState.currentStepIndex]);

  return {
    // 状态
    demoState,
    currentStep: getCurrentStep(),
    progress: getProgress(),
    
    // 控制方法
    startDemo,
    continueDemo,
    pauseDemo,
    stopDemo,
    nextStep,
    restartDemo,
    
    // 状态查询
    isRunning: demoState.isRunning,
    isPaused: demoState.isPaused,
    canContinue: demoState.isPaused && demoState.currentDemo !== null,
    canNext: demoState.isRunning && !demoState.isPaused
  };
};
