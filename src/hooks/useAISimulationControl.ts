/**
 * AI导师控制功能Hook
 * 处理AI对模拟的控制操作
 */

import { useCallback } from 'react';
import { useAITutorContext } from '@/components/ai-tutor/AITutorProvider';
import { simulationControlManager } from '@/components/ai-tutor/SimulationControlManager';
import type { AIControlCommand, AIControlResult } from '@/types/simulation-controls';

export const useAISimulationControl = () => {
  const { state } = useAITutorContext();

  /**
   * 解析AI消息中的控制命令
   */
  const parseControlCommands = useCallback((message: string): AIControlCommand[] => {
    const commands: AIControlCommand[] = [];
    
    // 匹配控制命令的正则表达式
    const commandPattern = /\[CONTROL:(\w+):(\w+)(?::([^\\]]+))?\]/g;
    let match;
    
    while ((match = commandPattern.exec(message)) !== null) {
      const [, simulationType, action, parametersStr] = match;
      
      let parameters: any = {};
      if (parametersStr) {
        try {
          parameters = JSON.parse(parametersStr);
        } catch (error) {
          console.warn('解析控制参数失败:', parametersStr, error);
        }
      }
      
      commands.push({
        simulationType,
        action,
        parameters,
        description: `控制${simulationType}执行${action}`
      });
    }
    
    return commands;
  }, []);

  /**
   * 执行控制命令
   */
  const executeCommands = useCallback(async (commands: AIControlCommand[]): Promise<AIControlResult[]> => {
    const results: AIControlResult[] = [];
    
    for (const command of commands) {
      try {
        const result = await simulationControlManager.executeCommand(command);
        results.push(result);
        
        // 记录控制操作
        console.log(`[AI控制] ${command.simulationType}:${command.action}`, result);
      } catch (error) {
        console.error('[AI控制] 命令执行失败:', command, error);
        results.push({
          success: false,
          message: `执行失败: ${error instanceof Error ? error.message : '未知错误'}`,
          error: 'EXECUTION_ERROR'
        });
      }
    }
    
    return results;
  }, []);

  /**
   * 为AI提供当前模拟状态信息
   */
  const getCurrentSimulationInfo = useCallback(() => {
    const currentSim = state.simulationContext?.type;
    if (!currentSim) {
      return null;
    }

    // 设置当前模拟
    simulationControlManager.setCurrentSimulation(currentSim);
    
    // 获取当前状态
    const currentState = simulationControlManager.getCurrentState();
    const availableCommands = simulationControlManager.getAvailableCommands(currentSim);
    
    return {
      simulationType: currentSim,
      currentState,
      availableCommands,
      controlFormat: `使用 [CONTROL:${currentSim}:命令名:参数JSON] 格式来控制模拟`
    };
  }, [state.simulationContext]);

  /**
   * 生成带控制能力的系统提示词
   */
  const getControlEnhancedPrompt = useCallback((basePrompt: string): string => {
    const simInfo = getCurrentSimulationInfo();
    if (!simInfo) {
      return basePrompt;
    }

    const controlInstructions = `

## 模拟控制能力

你现在可以直接控制${simInfo.simulationType}模拟！使用以下格式在回复中嵌入控制命令：

**控制格式：** \`[CONTROL:${simInfo.simulationType}:动作:参数]\`

**可用操作：**
${simInfo.availableCommands.map(cmd => `- ${cmd}`).join('\n')}

**当前状态：**
\`\`\`json
${JSON.stringify(simInfo.currentState, null, 2)}
\`\`\`

**使用示例：**
- 开始模拟：\`[CONTROL:${simInfo.simulationType}:play]\`
- 设置参数：\`[CONTROL:${simInfo.simulationType}:setSpeed:{"speed":100}]\`
- 重置模拟：\`[CONTROL:${simInfo.simulationType}:reset]\`

**重要提示：**
1. 控制命令会自动执行，请根据教学需要合理使用
2. 可以在解释的同时控制参数，让学生看到实时效果
3. 建议先解释要做什么，再执行控制命令
4. 每次控制后可以引导学生观察变化`;

    return basePrompt + controlInstructions;
  }, [getCurrentSimulationInfo]);

  /**
   * 处理包含控制命令的AI回复
   */
  const processAIReply = useCallback(async (message: string): Promise<{
    processedMessage: string;
    controlResults: AIControlResult[];
  }> => {
    // 解析控制命令
    const commands = parseControlCommands(message);
    
    // 执行控制命令
    const controlResults = commands.length > 0 ? await executeCommands(commands) : [];
    
    // 移除消息中的控制命令标记
    const processedMessage = message.replace(/\[CONTROL:[^\]]+\]/g, '').trim();
    
    return {
      processedMessage,
      controlResults
    };
  }, [parseControlCommands, executeCommands]);

  /**
   * 获取控制操作的反馈信息
   */
  const getControlFeedback = useCallback((results: AIControlResult[]): string => {
    if (results.length === 0) {
      return '';
    }

    const feedback = results.map(result => {
      if (result.success) {
        return `✅ ${result.message}`;
      } else {
        return `❌ ${result.message}`;
      }
    }).join('\n');

    return `\n\n**操作结果：**\n${feedback}`;
  }, []);

  /**
   * 注册模拟控制接口
   */
  const registerSimulationControls = useCallback((simulationType: string, controls: any) => {
    simulationControlManager.registerControls(simulationType, controls);
  }, []);

  /**
   * 取消注册模拟控制接口
   */
  const unregisterSimulationControls = useCallback((simulationType: string) => {
    simulationControlManager.unregisterControls(simulationType);
  }, []);

  return {
    // 状态查询
    getCurrentSimulationInfo,
    
    // 消息处理
    parseControlCommands,
    processAIReply,
    getControlFeedback,
    getControlEnhancedPrompt,
    
    // 控制管理
    registerSimulationControls,
    unregisterSimulationControls,
    executeCommands,
    
    // 便捷属性
    hasCurrentSimulation: !!state.simulationContext?.type,
    currentSimulation: state.simulationContext?.type || null
  };
};
