
import { useState, useCallback, useRef, useEffect } from 'react';
import { createGrid, computeNextGeneration, GameRules } from '@/lib/game-of-life/core';
import { Pattern } from '@/lib/game-of-life/patterns';

interface GameOfLifeConfig {
  width: number;
  height: number;
  speed: number;
  wrapEdges: boolean;
  rules: GameRules;
  isActive: boolean;
}

export function useGameOfLife(config: GameOfLifeConfig) {
  // 确保config中的尺寸参数有效
  const safeConfig = {
    ...config,
    width: Math.max(1, Math.floor(config.width) || 1),
    height: Math.max(1, Math.floor(config.height) || 1)
  };
  
  const [grid, setGrid] = useState(() => {
    console.log('Creating initial grid with size:', safeConfig.width, 'x', safeConfig.height);
    return createGrid(safeConfig.width, safeConfig.height);
  });
  const [isRunning, setIsRunning] = useState(false);
  const [generation, setGeneration] = useState(0);
  const [liveCellCount, setLiveCellCount] = useState(0);
  
  const runningRef = useRef(isRunning);
  runningRef.current = isRunning;

  const speedRef = useRef(safeConfig.speed);
  speedRef.current = safeConfig.speed;

  const animationFrameId = useRef<number>();
  const lastUpdateTime = useRef(0);

  // 当config的尺寸变化时，重新创建grid
  useEffect(() => {
    const newWidth = Math.max(1, Math.floor(config.width) || 1);
    const newHeight = Math.max(1, Math.floor(config.height) || 1);
    
    console.log('Config size changed, checking if grid needs update:', {
      oldSize: { width: safeConfig.width, height: safeConfig.height },
      newSize: { width: newWidth, height: newHeight }
    });
    
    // 直接比较数值而不是引用
    if (newWidth !== safeConfig.width || newHeight !== safeConfig.height) {
      console.log('Grid size changed, creating new grid');
      setGrid(createGrid(newWidth, newHeight));
      setGeneration(0);
      setLiveCellCount(0);
    }
  }, [config.width, config.height, safeConfig.width, safeConfig.height]);

  const runSimulation = useCallback((timestamp: number) => {
    if (!runningRef.current || !safeConfig.isActive) return;

    if (timestamp - lastUpdateTime.current >= speedRef.current) {
      lastUpdateTime.current = timestamp;
      setGrid((g) => {
        const { nextGrid, liveCells } = computeNextGeneration(g, safeConfig.width, safeConfig.height, safeConfig.wrapEdges, safeConfig.rules);
        setLiveCellCount(liveCells);
        return nextGrid;
      });
      setGeneration((g) => g + 1);
    }
    
    if (safeConfig.isActive) {
      animationFrameId.current = requestAnimationFrame(runSimulation);
    }
  }, [safeConfig.width, safeConfig.height, safeConfig.wrapEdges, safeConfig.rules, safeConfig.isActive]);

  // Stop animation when not active
  useEffect(() => {
    if (!safeConfig.isActive && animationFrameId.current) {
      cancelAnimationFrame(animationFrameId.current);
      setIsRunning(false);
    }
  }, [safeConfig.isActive]);

  const togglePlay = useCallback(() => {
    if (!safeConfig.isActive) return;
    
    setIsRunning(!isRunning);
    if (!isRunning) {
      runningRef.current = true;
      lastUpdateTime.current = performance.now();
      animationFrameId.current = requestAnimationFrame(runSimulation);
    } else {
      if (animationFrameId.current) {
        cancelAnimationFrame(animationFrameId.current);
      }
    }
  }, [isRunning, runSimulation, safeConfig.isActive]);

  const step = useCallback(() => {
    if (!safeConfig.isActive) return;
    setGrid((g) => {
      const { nextGrid, liveCells } = computeNextGeneration(g, safeConfig.width, safeConfig.height, safeConfig.wrapEdges, safeConfig.rules);
      setLiveCellCount(liveCells);
      return nextGrid;
    });
    setGeneration((g) => g + 1);
  }, [safeConfig.width, safeConfig.height, safeConfig.wrapEdges, safeConfig.rules, safeConfig.isActive]);

  const reset = useCallback(() => {
    setIsRunning(false);
    if (animationFrameId.current) {
      cancelAnimationFrame(animationFrameId.current);
    }
    setGrid(createGrid(safeConfig.width, safeConfig.height));
    setGeneration(0);
    setLiveCellCount(0);
  }, [safeConfig.width, safeConfig.height]);
  
  const setCellState = useCallback((row: number, col: number, state: number) => {
      console.log('setCellState called', { row, col, state, isRunning, isActive: safeConfig.isActive });
      console.log('Current grid state:', { gridExists: !!grid, gridLength: grid?.length, gridHeight: safeConfig.height, gridWidth: safeConfig.width });
      
      // 移除所有阻止交互的条件，用户随时都可以交互
      const newGrid = grid.map(r => [...r]);
      if (row >= 0 && row < safeConfig.height && col >= 0 && col < safeConfig.width && 
          newGrid[row] && Array.isArray(newGrid[row])) {
        const oldState = newGrid[row][col];
        console.log('Updating cell state', { row, col, oldState, newState: state });
        newGrid[row][col] = state;
        setGrid(newGrid);
        
        // 更新活细胞计数
        const countChange = state === 1 ? 1 : (oldState === 1 ? -1 : 0);
        setLiveCellCount(c => {
          const newCount = c + countChange;
          console.log('Live cell count updated:', { oldCount: c, change: countChange, newCount });
          return newCount;
        });
      } else {
        console.log('setCellState validation failed', { 
          rowValid: row >= 0 && row < safeConfig.height,
          colValid: col >= 0 && col < safeConfig.width,
          gridRowExists: newGrid[row] && Array.isArray(newGrid[row]),
          gridDetails: { row, col, height: safeConfig.height, width: safeConfig.width }
        });
      }
  }, [grid, safeConfig.width, safeConfig.height, isRunning, safeConfig.isActive]);

  const loadPattern = useCallback((pattern: Pattern) => {
      if (!safeConfig.isActive) return;
      reset();
      const newGrid = createGrid(safeConfig.width, safeConfig.height);
      const offsetX = Math.floor(safeConfig.width / 2 - Math.max(...pattern.map(p => p[0])) / 2);
      const offsetY = Math.floor(safeConfig.height / 2 - Math.max(...pattern.map(p => p[1])) / 2);
      let liveCells = 0;
      pattern.forEach(([x, y]) => {
          const gridX = x + offsetX;
          const gridY = y + offsetY;
          if (gridX >= 0 && gridX < safeConfig.width && gridY >= 0 && gridY < safeConfig.height &&
              newGrid[gridY] && Array.isArray(newGrid[gridY])) {
              newGrid[gridY][gridX] = 1;
              liveCells++;
          }
      });
      setGrid(newGrid);
      setLiveCellCount(liveCells);
      setGeneration(0);
  }, [safeConfig.width, safeConfig.height, safeConfig.isActive, reset]);

  return { 
    grid, 
    isRunning, 
    generation, 
    liveCellCount, 
    togglePlay, 
    start: () => {
      if (!isRunning && safeConfig.isActive) {
        setIsRunning(true);
        runningRef.current = true;
        lastUpdateTime.current = performance.now();
        animationFrameId.current = requestAnimationFrame(runSimulation);
      }
    },
    pause: () => {
      if (isRunning) {
        setIsRunning(false);
        runningRef.current = false;
        if (animationFrameId.current) {
          cancelAnimationFrame(animationFrameId.current);
        }
      }
    },
    step, 
    reset, 
    setCellState, 
    loadPattern 
  };
}
