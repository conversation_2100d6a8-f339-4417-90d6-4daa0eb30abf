
import { useState, useCallback, useRef, useEffect } from 'react';
import { 
  createIsingGrid, 
  metropolisStep, 
  calculateTotalEnergy, 
  calculateTotalMagnetization, 
  initialParams, 
  IsingParams,
  SpinGrid 
} from '@/lib/ising-model/core';

interface IsingModelConfig {
  isActive: boolean;
}

export function useIsingModel(config: IsingModelConfig) {
  const [grid, setGrid] = useState<SpinGrid>(() => createIsingGrid(initialParams.gridSize, 'Random'));
  const [params, setParams] = useState<IsingParams>(initialParams);
  const [isRunning, setIsRunning] = useState(false);
  const [initialState, setInitialState] = useState<'Random' | 'Ordered'>('Random');
  const [currentEnergy, setCurrentEnergy] = useState(0);
  const [currentMagnetization, setCurrentMagnetization] = useState(0);
  const [stepCount, setStepCount] = useState(0);
  
  const runningRef = useRef(isRunning);
  const animationFrameRef = useRef<number>();
  const lastUpdateTimeRef = useRef(0);
  const paramsRef = useRef(params);
  
  runningRef.current = isRunning;
  paramsRef.current = params;

  // 初始化能量和磁化强度
  useEffect(() => {
    const energy = calculateTotalEnergy(grid, params);
    const magnetization = calculateTotalMagnetization(grid);
    setCurrentEnergy(energy);
    setCurrentMagnetization(magnetization);
  }, [grid, params]);

  // 当页面变为非活跃状态时停止运行
  useEffect(() => {
    if (!config.isActive && animationFrameRef.current) {
      cancelAnimationFrame(animationFrameRef.current);
      setIsRunning(false);
    }
  }, [config.isActive]);

  const runSimulation = useCallback((timestamp: number, stepsPerFrame: number) => {
    if (!runningRef.current || !config.isActive) return;

    if (timestamp - lastUpdateTimeRef.current >= 50) {
      lastUpdateTimeRef.current = timestamp;
      
      setGrid(currentGrid => {
        setCurrentEnergy(currentEnergy => {
          setCurrentMagnetization(currentMagnetization => {
            const steps = Math.round(stepsPerFrame);
            let tempGrid = currentGrid;
            let tempEnergy = currentEnergy;
            let tempMagnetization = currentMagnetization;
            
            for (let i = 0; i < steps; i++) {
              const result = metropolisStep(tempGrid, paramsRef.current, tempEnergy, tempMagnetization);
              tempGrid = result.newGrid;
              tempEnergy = result.newEnergy;
              tempMagnetization = result.newMagnetization;
            }
            
            setCurrentEnergy(tempEnergy);
            setCurrentMagnetization(tempMagnetization);
            setStepCount(prev => prev + steps);
            
            return tempMagnetization;
          });
          return currentEnergy;
        });
        
        return currentGrid;
      });
    }
    
    if (config.isActive) {
      animationFrameRef.current = requestAnimationFrame((ts) => runSimulation(ts, stepsPerFrame));
    }
  }, [config.isActive]);

  const togglePlay = useCallback((stepsPerFrame: number) => {
    if (!config.isActive) return;
    
    setIsRunning(!isRunning);
    if (!isRunning) {
      lastUpdateTimeRef.current = performance.now();
      animationFrameRef.current = requestAnimationFrame((ts) => runSimulation(ts, stepsPerFrame));
    } else {
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
      }
    }
  }, [isRunning, runSimulation, config.isActive]);

  const reset = useCallback(() => {
    setIsRunning(false);
    if (animationFrameRef.current) {
      cancelAnimationFrame(animationFrameRef.current);
    }
    const newGrid = createIsingGrid(params.gridSize, initialState);
    setGrid(newGrid);
    setCurrentEnergy(calculateTotalEnergy(newGrid, params));
    setCurrentMagnetization(calculateTotalMagnetization(newGrid));
    setStepCount(0);
  }, [params.gridSize, initialState, params]);

  const flipSpin = useCallback((x: number, y: number) => {
    // 只在非运行状态且页面活跃时允许交互
    if (isRunning || !config.isActive) return;
    
    setGrid(currentGrid => {
      if (!currentGrid[y] || !Array.isArray(currentGrid[y]) || currentGrid[y][x] === undefined) {
        return currentGrid;
      }
      
      const newGrid = currentGrid.map(row => [...row]);
      newGrid[y][x] *= -1;
      const newEnergy = calculateTotalEnergy(newGrid, params);
      const newMagnetization = calculateTotalMagnetization(newGrid);
      setCurrentEnergy(newEnergy);
      setCurrentMagnetization(newMagnetization);
      return newGrid;
    });
  }, [isRunning, params, config.isActive]);

  // 当网格大小变化时重置网格
  useEffect(() => {
    const newGrid = createIsingGrid(params.gridSize, initialState);
    setGrid(newGrid);
    setCurrentEnergy(calculateTotalEnergy(newGrid, params));
    setCurrentMagnetization(calculateTotalMagnetization(newGrid));
    setStepCount(0);
  }, [params.gridSize, initialState]);

  return {
    grid,
    params,
    setParams,
    isRunning,
    initialState,
    setInitialState,
    currentEnergy,
    currentMagnetization,
    stepCount,
    togglePlay,
    reset,
    flipSpin
  };
}
