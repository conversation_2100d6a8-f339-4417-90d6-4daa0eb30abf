/**
 * AI导师边界检查Hook
 * 处理页面检测和参数验证
 */

import { useCallback } from 'react';
import { useAITutorContext } from '@/components/ai-tutor/AITutorProvider';
import { 
  checkTopicBoundary, 
  checkParameterValidity, 
  generateBoundaryCheckPrompt,
  getAllTopicMappings
} from '@/components/ai-tutor/BoundaryCheck';
import type { SimulationType } from '@/types/ai-tutor';

interface BoundaryCheckResult {
  isValid: boolean;
  warningMessage?: string;
  suggestedTopic?: SimulationType;
  blockedReason?: 'topic' | 'parameter';
}

export const useAITutorBoundaryCheck = () => {
  const { state } = useAITutorContext();
  const currentSimulation = state.simulationContext?.type || null;

  /**
   * 检查用户消息是否符合边界条件
   */
  const checkMessageBoundary = useCallback((userMessage: string): BoundaryCheckResult => {
    // 1. 检查主题边界
    const topicCheck = checkTopicBoundary(userMessage, currentSimulation);
    if (!topicCheck.isValidTopic) {
      return {
        isValid: false,
        warningMessage: topicCheck.warningMessage,
        suggestedTopic: topicCheck.suggestedTopic,
        blockedReason: 'topic'
      };
    }

    // 2. 检查参数有效性
    const paramCheck = checkParameterValidity(userMessage, currentSimulation);
    if (!paramCheck.isValidParameter) {
      return {
        isValid: false,
        warningMessage: paramCheck.warningMessage,
        blockedReason: 'parameter'
      };
    }

    return { isValid: true };
  }, [currentSimulation]);

  /**
   * 生成增强的系统提示词（包含边界检查）
   */
  const getBoundaryEnhancedPrompt = useCallback((basePrompt: string): string => {
    const boundaryPrompt = generateBoundaryCheckPrompt(currentSimulation);
    return basePrompt + boundaryPrompt;
  }, [currentSimulation]);

  /**
   * 生成边界违规的警告消息
   */
  const generateBoundaryWarning = useCallback((userMessage: string): string | null => {
    const check = checkMessageBoundary(userMessage);
    if (check.isValid) {
      return null;
    }

    // 生成友好的警告消息
    let warningMessage = check.warningMessage || '';
    
    if (check.blockedReason === 'topic' && check.suggestedTopic) {
      const topicMappings = getAllTopicMappings();
      const suggestedTopicName = topicMappings[check.suggestedTopic]?.displayName || check.suggestedTopic;
      const currentTopicName = currentSimulation ? topicMappings[currentSimulation]?.displayName : '未知';
      
      warningMessage = `🚨 **主题边界提醒**

您的问题涉及"${suggestedTopicName}"主题，但当前页面是"${currentTopicName}"。

为了获得准确的指导，请：
1. 先切换到"${suggestedTopicName}"页面
2. 然后再提问相关内容

这样我就能为您提供该主题的专业指导了！`;
    } else if (check.blockedReason === 'parameter') {
      warningMessage = `🚨 **参数验证提醒**

${warningMessage}

如果您需要了解其他主题的参数，请切换到相应的主题页面。`;
    }

    return warningMessage;
  }, [checkMessageBoundary, currentSimulation]);

  /**
   * 智能预处理用户消息，结合LLM进行语义理解
   */
  const preprocessUserMessage = useCallback(async (userMessage: string): Promise<{
    shouldBlock: boolean;
    warningMessage?: string;
    processedMessage: string;
    parameterMapping?: Record<string, string>;
  }> => {
    // 1. 首先检查主题边界
    const topicCheck = checkTopicBoundary(userMessage, currentSimulation);
    if (!topicCheck.isValidTopic) {
      return {
        shouldBlock: true,
        warningMessage: `🚨 **主题边界提醒**

您的问题涉及"${topicCheck.suggestedTopic ? getAllTopicMappings()[topicCheck.suggestedTopic]?.displayName : '其他'}"主题，但当前页面是"${currentSimulation ? getAllTopicMappings()[currentSimulation]?.displayName : '未知'}"。

为了获得准确的指导，请：
1. 先切换到相应的主题页面
2. 然后再提问相关内容

这样我就能为您提供专业指导了！`,
        processedMessage: userMessage
      };
    }

    // 2. 智能参数映射检查
    const parameterMapping = await intelligentParameterMapping(userMessage, currentSimulation);
    
    if (parameterMapping.hasInvalidParameters) {
      return {
        shouldBlock: true,
        warningMessage: `🚨 **参数验证提醒**

${parameterMapping.warningMessage}

如果您需要了解其他主题的参数，请切换到相应的主题页面。`,
        processedMessage: userMessage
      };
    }

    return {
      shouldBlock: false,
      processedMessage: userMessage,
      parameterMapping: parameterMapping.mappedParameters
    };
  }, [currentSimulation]);

  /**
   * 智能参数映射：使用语义理解将用户描述映射到实际参数
   */
  const intelligentParameterMapping = useCallback(async (userMessage: string, simulationType: SimulationType | null): Promise<{
    hasInvalidParameters: boolean;
    warningMessage?: string;
    mappedParameters?: Record<string, string>;
  }> => {
    if (!simulationType) {
      return { hasInvalidParameters: false };
    }

    const { SIMULATION_PARAMETERS } = await import('@/components/ai-tutor/BoundaryCheck');
    const topicInfo = SIMULATION_PARAMETERS[simulationType];
    if (!topicInfo) {
      return { hasInvalidParameters: false };
    }

    // 获取当前主题的所有参数
    const availableParameters: string[] = [];
    const parameterMappings: Record<string, string> = {};
    
    Object.values(topicInfo.categories).forEach(category => {
      Object.entries(category.parameters).forEach(([paramKey, paramInfo]) => {
        availableParameters.push(paramInfo.displayName);
        parameterMappings[paramInfo.displayName] = paramKey;
      });
    });

    // 定义常见的参数同义词映射
    const synonymMappings: Record<string, Record<string, string[]>> = {
      'schelling': {
        'gridSize': ['晶格', '晶格大小', '网格', '网格大小', '网格尺寸', '分辨率', '格子', '格子大小'],
        'similarityThreshold': ['相似性', '阈值', '满意度', '相似度', '相似性阈值'],
        'density': ['密度', '人口密度', '填充率', '占用率'],
        'groupRatio': ['比例', '群体比例', '种族比例', '组别比例']
      },
      'game-of-life': {
        'gridSize': ['晶格', '晶格大小', '网格', '网格大小', '网格尺寸', '分辨率'],
        'speed': ['速度', '运行速度', '模拟速度'],
        'wrapEdges': ['边界', '包裹边界', '环形边界']
      },
      'ising-model': {
        'gridSize': ['晶格', '晶格大小', '网格', '网格大小', '网格尺寸', '分辨率'],
        'temperature': ['温度', '热力学温度', '系统温度'],
        'magneticField': ['磁场', '外磁场', '磁场强度']
      }
    };

    // 检查用户消息中是否包含可映射的参数
    const messageWords = userMessage.toLowerCase();
    const synonyms = synonymMappings[simulationType] || {};
    const mappedParams: Record<string, string> = {};

    // 尝试匹配同义词
    Object.entries(synonyms).forEach(([paramKey, synonymList]) => {
      const found = synonymList.some(synonym => {
        if (messageWords.includes(synonym)) {
          mappedParams[synonym] = paramKey;
          return true;
        }
        return false;
      });
    });

    // 如果没有找到任何参数匹配，则认为是正常对话
    if (Object.keys(mappedParams).length === 0) {
      return { hasInvalidParameters: false };
    }

    // 检查映射的参数是否确实存在于当前主题
    const validMappings: Record<string, string> = {};
    const invalidTerms: string[] = [];

    Object.entries(mappedParams).forEach(([userTerm, paramKey]) => {
      // 检查参数是否在任何类别中存在
      let paramExists = false;
      Object.values(topicInfo.categories).forEach(category => {
        if (category.parameters[paramKey]) {
          paramExists = true;
        }
      });

      if (paramExists) {
        validMappings[userTerm] = paramKey;
      } else {
        invalidTerms.push(userTerm);
      }
    });

    // 如果有无效参数，返回警告
    if (invalidTerms.length > 0) {
      return {
        hasInvalidParameters: true,
        warningMessage: `您提到的参数"${invalidTerms.join('、')}"在当前的"${topicInfo.displayName}"主题中不存在。

${topicInfo.displayName}支持的参数包括：
${availableParameters.map(p => `• ${p}`).join('\n')}

请确认您询问的参数是否属于当前主题，或者切换到相应的主题页面。`
      };
    }

    return {
      hasInvalidParameters: false,
      mappedParameters: validMappings
    };
  }, []);

  /**
   * 获取当前主题的详细信息
   */
  const getCurrentTopicInfo = useCallback(() => {
    if (!currentSimulation) {
      return null;
    }

    const topicMappings = getAllTopicMappings();
    return topicMappings[currentSimulation] || null;
  }, [currentSimulation]);

  /**
   * 检查是否在有效的主题页面
   */
  const isValidTopicPage = useCallback((): boolean => {
    return currentSimulation !== null;
  }, [currentSimulation]);

  return {
    // 边界检查功能
    checkMessageBoundary,
    generateBoundaryWarning,
    preprocessUserMessage,
    
    // 系统提示词增强
    getBoundaryEnhancedPrompt,
    
    // 状态查询
    getCurrentTopicInfo,
    isValidTopicPage,
    
    // 便捷属性
    currentSimulation,
    hasValidTopic: currentSimulation !== null
  };
};
