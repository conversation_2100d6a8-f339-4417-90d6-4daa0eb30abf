/**
 * AI导师系统的TypeScript类型定义
 */

// LLM提供商类型
export type LLMProvider = 'openai' | 'claude' | 'xai' | 'custom';

// LLM配置接口
export interface LLMConfig {
  provider: LLMProvider;
  endpoint: string;
  apiKey: string;
  model: string;
  temperature: number;
  maxTokens: number;
  systemPrompt?: string;
  enableFunctionCalling?: boolean;
}

// 消息类型
export interface ChatMessage {
  id: string;
  role: 'user' | 'assistant' | 'system' | 'tool';
  content: string;
  timestamp: number;
  toolCalls?: ToolCall[];
  toolCallId?: string;
  metadata?: {
    tokens?: number;
    duration?: number;
    context?: string;
    controlResults?: any[];
  };
}

// 聊天历史
export interface ChatHistory {
  messages: ChatMessage[];
  sessionId: string;
  createdAt: number;
  updatedAt: number;
}

// 导师模式类型
export type TutorMode = 'introduction' | 'exploration' | 'demonstration' | 'qa';

// 模拟类型
export type SimulationType = 'game-of-life' | 'boids' | 'ising-model' | 'physarum' | 'schelling';

// 当前上下文
export interface SimulationContext {
  type: SimulationType;
  isActive: boolean;
  currentParams?: Record<string, any>;
  userActions?: string[];
}

// AI导师状态
export interface AITutorState {
  isConfigured: boolean;
  isConnected: boolean;
  isLoading: boolean;
  currentMode: TutorMode;
  simulationContext: SimulationContext | null;
  chatHistory: ChatHistory | null;
  config: LLMConfig | null;
  error: string | null;
}

// LLM连接状态
export type ConnectionStatus = 'disconnected' | 'connecting' | 'connected' | 'error';

// API响应类型
export interface LLMResponse {
  content: string;
  tokens?: number;
  duration?: number;
  error?: string;
  toolCalls?: ToolCall[];
}

// 流式响应类型
export interface LLMStreamResponse {
  content: string;
  isComplete: boolean;
  error?: string;
  toolCalls?: ToolCall[];
}

// 测试连接结果
export interface ConnectionTestResult {
  success: boolean;
  latency?: number;
  error?: string;
  modelInfo?: {
    name: string;
    capabilities: string[];
  };
}

// 控件操作接口
export interface ControlAction {
  type: 'set_parameter' | 'trigger_action' | 'load_preset';
  target: string;
  value?: any;
  description: string;
}

// 演示脚本
export interface DemoScript {
  id: string;
  title: string;
  description: string;
  simulationType: SimulationType;
  steps: DemoStep[];
}

export interface DemoStep {
  id: string;
  description: string;
  action: ControlAction;
  explanation: string;
  duration?: number;
}

// 知识库条目
export interface KnowledgeEntry {
  id: string;
  topic: string;
  simulationType: SimulationType;
  level: 'beginner' | 'intermediate' | 'advanced';
  content: string;
  keywords: string[];
  relatedTopics: string[];
}

// 学习进度
export interface LearningProgress {
  userId?: string;
  completedTopics: string[];
  currentLevel: Record<SimulationType, 'beginner' | 'intermediate' | 'advanced'>;
  timeSpent: Record<SimulationType, number>;
  lastActivity: number;
}

// 用户偏好
export interface UserPreferences {
  preferredExplanationStyle: 'concise' | 'detailed' | 'visual';
  learningPace: 'slow' | 'normal' | 'fast';
  enableDemoMode: boolean;
  saveHistory: boolean;
  theme: 'light' | 'dark' | 'auto';
  language: 'zh-CN' | 'en-US';
}

// 错误类型
export interface AITutorError {
  code: string;
  message: string;
  details?: any;
  timestamp: number;
}

// Hook返回类型
export interface UseAITutorReturn {
  state: AITutorState;
  sendMessage: (message: string) => Promise<void>;
  clearHistory: () => void;
  updateConfig: (config: Partial<LLMConfig>) => void;
  testConnection: (config?: LLMConfig) => Promise<ConnectionTestResult>;
  setMode: (mode: TutorMode) => void;
  setSimulationContext: (context: SimulationContext) => void;
  executeControlAction: (action: ControlAction) => Promise<void>;
  clearAllData: () => void;
}

export interface UseLLMConnectionReturn {
  status: ConnectionStatus;
  sendMessage: (message: string, config: LLMConfig, functions?: FunctionDefinition[]) => Promise<LLMResponse>;
  sendMessageWithHistory: (
    messages: Array<{ role: 'user' | 'assistant' | 'system' | 'tool'; content: string; tool_calls?: any[]; tool_call_id?: string }>,
    config: LLMConfig,
    functions?: FunctionDefinition[]
  ) => Promise<LLMResponse>;
  sendStreamMessage: (
    message: string, 
    config: LLMConfig, 
    onChunk: (chunk: LLMStreamResponse) => void,
    functions?: FunctionDefinition[]
  ) => Promise<void>;
  sendStreamMessageWithHistory: (
    messages: Array<{ role: 'user' | 'assistant' | 'system' | 'tool'; content: string; tool_calls?: any[]; tool_call_id?: string }>,
    config: LLMConfig,
    onChunk: (chunk: LLMStreamResponse) => void,
    functions?: FunctionDefinition[]
  ) => Promise<void>;
  testConnection: (config: LLMConfig) => Promise<ConnectionTestResult>;
  cancelRequest: () => void;
}

// Function Calling相关类型
export interface FunctionParameter {
  type: string;
  description: string;
  enum?: string[];
  properties?: Record<string, FunctionParameter>;
  required?: string[];
}

export interface FunctionDefinition {
  name: string;
  description: string;
  parameters: {
    type: 'object';
    properties: Record<string, FunctionParameter>;
    required?: string[];
  };
}

export interface FunctionCall {
  name: string;
  arguments: string;
}

export interface ToolCall {
  id: string;
  type: 'function';
  function: FunctionCall;
}

// 事件类型
export interface AITutorEvent {
  type: 'message_sent' | 'message_received' | 'config_updated' | 'mode_changed' | 'error_occurred';
  payload: any;
  timestamp: number;
}

// 组件Props类型
export interface AITutorButtonProps {
  className?: string;
  position?: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right';
}

export interface AITutorSettingsProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export interface AITutorChatProps {
  className?: string;
}

export interface AITutorProviderProps {
  children: React.ReactNode;
}
