/**
 * 模拟控件操作类型定义 - 严格按照 ai-tutor-rebuild-guide.md 文档要求
 * 这是完整重构后的接口定义，完全符合文档标准
 */

// 通用控制操作结果
export interface ControlActionResult {
  success: boolean;
  currentState: any;
  message?: string;
}

// 通用参数设置结果
export interface ParameterSetResult {
  success: boolean;
  actualValue: number | boolean | string | object;
  message?: string;
}

// 生命游戏控制操作 - 严格按照文档要求
export interface GameOfLifeControlsNew {
  // 基础控制类 - 返回仿真实际运行状态
  startSimulation: (currentState?: any) => ControlActionResult;
  pauseSimulation: (currentState?: any) => ControlActionResult;
  stepwiseSimulation: () => ControlActionResult;
  resetSimulation: () => ControlActionResult;
  
  // 参数设置类 - 返回滑杆实际值
  setSimulationSpeed: (speed: number) => ParameterSetResult;
  setWrapBoundary: (wrap: boolean) => ParameterSetResult;
  setLifeRules: (rules: {
    survivalMin: number;
    survivalMax: number;
    birthMin: number;
    birthMax: number;
  }) => ParameterSetResult;
  
  // 预设图案选择 - 返回实际选项
  loadPresetPattern: (patternName: string) => ParameterSetResult;
  
  // 颜色设置
  setLiveCellColor: (color: string) => ParameterSetResult;
  setDeadCellColor: (color: string) => ParameterSetResult;
  
  // 状态查询类 - 返回实时参数取值
  getCurrentState: () => {
    isRunning: boolean;
    generation: number;
    liveCellCount: number;
    speed: number;
    wrapEdges: boolean;
    rules: any;
    liveCellColor: string;
    deadCellColor: string;
  };
}

// 文档要求验证清单
/*
✅ 生命游戏页面要求对比：

文档要求 vs 当前实现：
- 基础控制类：
  ✅ "仿真的「开始」、「暂停」控制函数" -> startSimulation, pauseSimulation
  ✅ "「单步运行」控制函数" -> stepwiseSimulation  
  ✅ "「重置」仿真函数" -> resetSimulation
  ✅ "返回仿真实际的运行状态" -> ControlActionResult.currentState

- 参数设置类：
  ✅ "「模拟速度」控制函数，返回滑杆的实际值" -> setSimulationSpeed + ParameterSetResult.actualValue
  ✅ "「环形边界」控制函数，返回滑杆的实际值" -> setWrapBoundary + actualValue
  ✅ "「生命规则」四个子选项控制函数，返回滑杆的实际值" -> setLifeRules + actualValue
  ✅ "「预设图案」选择函数，返回下拉菜单的实际选项" -> loadPresetPattern + actualValue

- 状态查询类：
  ✅ "当前运行状态、代数、活细胞数量等必要参数的查询函数" -> getCurrentState()

输入输出结构验证：
✅ 输入：具体控制指令 (startSimulation, pauseSimulation等)
✅ 输入：可选状态参数 (currentState参数)
✅ 输出：返回操作是否生效 (ControlActionResult.success)
✅ 输出：返回当前状态 (simulation_is_running, simulation_is_paused等)
✅ 输出：参数设置返回实际值 (ParameterSetResult.actualValue)
*/
