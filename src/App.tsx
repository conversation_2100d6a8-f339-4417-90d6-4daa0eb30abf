import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import ErrorBoundary from "./components/ErrorBoundary";
import LandingPage from "./components/LandingPage";
import Index from "./pages/Index";
import NotFound from "./pages/NotFound";
import { AITutorProvider } from "./components/ai-tutor/AITutorProvider";
import DebugPanel from "./components/DebugPanel";

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: (failureCount, error) => {
        // 如果是网络错误，重试3次
        if (error instanceof Error && error.message.includes('Cannot read properties of undefined')) {
          return false; // 对于我们修复的错误，不要重试
        }
        return failureCount < 3;
      },
    },
  },
});

const App = () => (
  <ErrorBoundary>
    <QueryClientProvider client={queryClient}>
      <TooltipProvider>
        <AITutorProvider>
          <Toaster />
          <Sonner />
          {/* 开发环境调试面板 */}
          {process.env.NODE_ENV === 'development' && <DebugPanel />}
          <BrowserRouter>
            <Routes>
              <Route path="/" element={<LandingPage />} />
              <Route path="/explore" element={<Index />} />
              {/* ADD ALL CUSTOM ROUTES ABOVE THE CATCH-ALL "*" ROUTE */}
              <Route path="*" element={<NotFound />} />
            </Routes>
          </BrowserRouter>
        </AITutorProvider>
      </TooltipProvider>
    </QueryClientProvider>
  </ErrorBoundary>
);

export default App;
