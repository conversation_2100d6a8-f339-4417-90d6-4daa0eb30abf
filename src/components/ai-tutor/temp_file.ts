/**
 * AI导师知识库
 * 为每种模拟类型提供专业的知识内容
 */

import type { SimulationType, KnowledgeEntry } from '@/types/ai-tutor';

/**
 * 生命游戏知识库
 */
export const GAME_OF_LIFE_KNOWLEDGE: KnowledgeEntry[] = [
  {
    id: 'gol-history',
    topic: '康威生命游戏的诞生传奇',
    simulationType: 'game-of-life',
    level: 'beginner',
    content: `
## 1970年：一个数学天才的奇思妙想

在剑桥大学的一个普通下午，年轻的数学家约翰·康威正在思考一个深刻的哲学问题：**生命的本质究竟是什么？能否用最简单的数学规则来模拟生命的复杂性？**

### 🎭 科学史上的传奇时刻

康威受到了传奇数学家**约翰·冯·诺伊曼**关于"自复制机器"研究的启发。冯·诺伊曼曾经设想：如果机器能够自我复制，那么它们是否也能展现出类似生命的特征？

经过无数个日夜的思考，康威终于在1970年创造出了这个被他称为"零玩家游戏"的数学宇宙。他后来回忆说：**"我想创造的不仅仅是一个游戏，而是一个关于生命、死亡与重生的数学诗篇。"**

### 🌟 四条简单规则，无限复杂宇宙

令人惊叹的是，康威用仅仅四条简单规则就构建了一个完整的"数字生态系统"：

1. **人口过少**：活细胞少于2个邻居时死亡（孤独死）
2. **适度繁荣**：活细胞有2-3个邻居时继续生存
3. **人口过多**：活细胞超过3个邻居时死亡（拥挤死）
4. **生命诞生**：死细胞恰好有3个邻居时复活

### 🔬 意外的科学发现

最初，康威只是想创造一个有趣的数学游戏。但科学家们很快发现，这个"游戏"竟然是**图灵完备**的——这意味着它可以模拟任何计算机程序！这一发现震惊了整个计算机科学界。
    `,
    keywords: ['约翰·康威', '1970年', '剑桥大学', '冯·诺伊曼', '自复制', '图灵完备', '涌现性'],
    relatedTopics: ['gol-rules', 'gol-patterns', 'complexity-science']
  },
  {
    id: 'gol-rules',
    topic: '生命的四大定律',
    simulationType: 'game-of-life',
    level: 'beginner',
    content: `
## 生命游戏：大自然的缩影

康威的天才之处在于，他用四条极其简单的规则，完美地模拟了真实世界中生命的**生存竞争**、**繁衍生息**和**环境适应**。

### 🏝️ 孤独法则
**少于2个邻居 → 死亡**
- 就像孤岛上的生物，缺乏群体支持而无法生存
- 模拟了现实中的社会隔离效应
- 体现了"团结就是力量"的生物学原理

### 🌱 生存法则  
**2-3个邻居 → 继续生存**
- 完美的生存环境：既不孤单，也不拥挤
- 就像适宜的生态密度，保证资源充足
- 反映了自然界中的"生态平衡"概念

### 💀 拥挤法则
**超过3个邻居 → 死亡**
- 模拟过度竞争导致的资源枯竭
- 类似现实中的"人口爆炸"问题
- 体现了环境承载力的限制

### 🆕 诞生法则
**恰好3个邻居 → 新生命诞生**
- 最神奇的规则：死亡中孕育新生
- 需要"恰到好处"的条件才能诞生
- 象征着生命对完美环境的需求

### 🎯 哲学思考
这四条规则揭示了一个深刻的哲学真理：**复杂性可以从简单性中涌现**。整个宇宙的复杂性，或许也源于几个基本的物理定律。
    `,
    keywords: ['生存规则', '诞生规则', '邻居数量', '生态平衡', '复杂性涌现'],
    relatedTopics: ['gol-history', 'gol-patterns', 'emergence-theory']
  },
  {
    id: 'gol-patterns',
    topic: '数字生命的奇妙形态',
    simulationType: 'game-of-life',
    level: 'intermediate',
    content: `
## 生命游戏中的"物种分类学"

就像生物学家对真实生物进行分类一样，数学家们也为生命游戏中的图案建立了完整的"分类体系"。

### 🏛️ 静物家族（Still Lifes）
**永恒不变的"数字化石"**

- **方块（Block）**：最简单的2×2正方形，象征着完美的稳定性
- **蜂巢（Beehive）**：六边形结构，让人联想到真实的蜂巢
- **面包（Loaf）**：不对称的静物，证明美不一定需要对称
- **船（Boat）**：小巧的5细胞静物，形状酷似小船

**科学意义**：静物代表了系统的"平衡态"，在热力学中称为"最低能量状态"。

### ⚡ 振荡器家族（Oscillators）
**有节律的"数字心跳"**

- **闪烁器（Blinker）**：最简单的周期2振荡器，像是宇宙的心跳
- **蟾蜍（Toad）**：周期2的对称振荡器，展示了完美的对称美
- **脉冲星（Pulsar）**：周期15的大型振荡器，如同天体物理中的脉冲星
- **时钟（Clock）**：周期2的精巧装置，体现了时间的周期性

**科学意义**：振荡器类似于物理学中的"简谐振动"，展示了系统的周期性行为。

### 🚀 飞行器家族（Spaceships）
**征服太空的"数字探险家"**

- **滑翔机（Glider）**：最著名的5细胞飞行器，每4步移动一格对角线
- **轻型宇宙飞船（LWSS）**：9细胞的水平飞行器，移动速度更快
- **中型宇宙飞船（MWSS）**：11细胞的飞行器，更加稳定
- **重型宇宙飞船（HWSS）**：13细胞的大型飞行器，展示了规模的力量

**科学意义**：飞行器证明了信息可以在空间中传播，类似于物理学中的"孤立波"。

### 🏭 枪械和炮台（Guns）
**无穷创造力的"生产线"**

- **滑翔机枪（Glider Gun）**：周期性产生滑翔机的装置
- **游轮炮台**：能发射各种飞行器的复杂结构

这些模式的发现让科学家们意识到：**简单规则可以产生无限复杂的行为**！
    `,
    keywords: ['静物', '振荡器', '飞行器', '滑翔机', '脉冲星', '滑翔机枪'],
    relatedTopics: ['gol-rules', 'gol-complexity', 'pattern-analysis']
  },
  {
    id: 'gol-complexity',
    topic: '从简单到复杂：涌现的奇迹',
    simulationType: 'game-of-life',
    level: 'advanced',
    content: `
## 复杂性科学的完美教科书

康威生命游戏被誉为**复杂性科学**的典型范例，它完美诠释了现代科学中最重要的概念之一：**涌现性（Emergence）**。

### 🌊 涌现性：1+1>2的奇迹

**什么是涌现？**
涌现是指系统在宏观层面展现出的性质和行为，这些特征在微观组件中并不存在。就像：
- 单个H₂O分子没有"湿润"的性质，但水却是湿的
- 单个神经元不会思考，但大脑却能产生意识
- 单个细胞不知道规则，但整体却展现出"智能"行为

### 🎭 生命游戏中的涌现现象

**1. 集体行为的涌现**
- 没有任何"中央控制器"，但整体展现出有序行为
- 滑翔机的"飞行"能力不存在于任何单个细胞中
- 复杂模式从简单规则中自发产生

**2. 信息传递的涌现**
- 滑翔机可以作为"信息载体"，在网格中传递信息
- 枪械可以"制造"其他模式，展现出生产能力
- 系统具备了"计算"的能力

**3. 自组织的涌现**
- 随机初始状态经常演化出有序结构
- 系统自动"筛选"出稳定的模式
- 展现出类似"自然选择"的机制

### 🧠 图灵完备性：意外的发现

1982年，研究者们证明了生命游戏是**图灵完备**的，这意味着：

- 它可以模拟任何计算机程序
- 理论上可以构建一台"生命游戏计算机"
- 甚至可以在其中运行生命游戏本身！

这个发现震惊了科学界：一个用来"娱乐"的数学游戏，竟然具备了通用计算的能力！

### 🌍 现实世界的应用

生命游戏的原理被广泛应用于：

- **生态学**：模拟动植物种群动态
- **城市规划**：研究城市发展模式
- **材料科学**：设计自修复材料
- **人工智能**：启发神经网络设计
- **社会学**：理解社会现象的涌现

### 🔮 哲学思考

康威曾说："也许我们的宇宙本身就是某种巨大的细胞自动机，而我们所见的一切复杂性，都源于几个简单的基本规律。"

这个想法并非天方夜谭——现代物理学正在探索宇宙是否真的可以用类似的数字规则来描述！
    `,
    keywords: ['涌现性', '复杂性科学', '图灵完备', '自组织', '计算', '哲学'],
    relatedTopics: ['complexity-theory', 'emergence', 'computation', 'cellular-automata']
  }
];

/**
 * Boids群体行为知识库
 */
export const BOIDS_KNOWLEDGE: KnowledgeEntry[] = [
  {
    id: 'boids-history',
    topic: '群体智能的数字重现',
    simulationType: 'boids',
    level: 'beginner',
    content: `
## 1986年：当计算机学会"飞行"

在硅谷的皮克斯动画工作室，年轻的计算机图形学专家**克雷格·雷诺兹**面临着一个看似不可能的挑战：如何让计算机中的角色像真实的鸟群一样自然地飞行？

### 🐦 大自然的启示

雷诺兹痴迷于观察窗外的鸟群。他发现了一个令人惊叹的现象：
- **没有领头鸟**：鸟群中并没有"指挥官"告诉每只鸟该往哪飞
- **完美协调**：成百上千只鸟却能完美同步，如同一个生命体
- **优雅转向**：整个鸟群能瞬间改变方向，没有碰撞，没有混乱

他想：**如果每只鸟都只遵循简单的本能反应，整个群体如何展现出如此复杂而优雅的行为？**

### 🧠 三条黄金法则的诞生

经过深入观察和思考，雷诺兹提出了著名的"**三条黄金法则**"：

1. **分离定律**：避免与邻居碰撞
2. **对齐定律**：与邻居朝同一方向飞行  
3. **聚合定律**：向邻居群体的中心靠拢

令人震惊的是，就这三条简单规则，竟然完美重现了鸟群的所有复杂行为！

### 🎬 从科学到艺术

雷诺兹的发现不仅是科学突破，更开启了数字艺术的新纪元：
- **《蝙蝠侠归来》(1992)**：首次在好莱坞电影中使用Boids技术
- **《狮子王》(1994)**：牛羚大迁移场景震撼全球
- **《指环王》(2001)**：史诗战争场面中的千军万马

雷诺兹后来感慨道：**"我永远没想到，对鸟群的简单观察，竟然改变了整个电影工业。"**

### 🌍 超越娱乐的深远影响

今天，Boids算法已经远远超出了动画领域：
- **机器人学**：无人机编队飞行
- **交通规划**：车流优化系统
- **生物学**：研究动物集群行为
- **社会学**：理解人群动态

这证明了一个深刻的道理：**自然界的智慧，往往隐藏在最简单的行为模式中。**
    `,
    keywords: ['克雷格·雷诺兹', '1986年', '皮克斯', '群体智能', '好莱坞', '无人机编队'],
    relatedTopics: ['boids-rules', 'swarm-intelligence', 'emergent-behavior']
  },
  {
    id: 'boids-rules',
    topic: '三条黄金法则：群体智能的密码',
    simulationType: 'boids',
    level: 'beginner',
    content: `
## 解码群体智能：从混沌到秩序

雷诺兹的天才发现在于：**复杂的群体行为可以用三条极其简单的个体规则来解释**。这三条法则就像是大自然编写的"程序代码"。

### 🚫 第一法则：分离（Separation）
**"保持个人空间，避免碰撞"**

- **生物学原理**：就像人在拥挤的地铁里本能地避开他人
- **物理意义**：短程排斥力，防止个体重叠
- **参数调节**：
  - 分离半径小 → 群体紧密但容易碰撞
  - 分离半径大 → 群体松散但很安全

**现实类比**：想象你在拥挤的商场里走路，你会本能地避开靠得太近的人。

### ➡️ 第二法则：对齐（Alignment）
**"跟随邻居的方向，保持队形"**

- **生物学原理**：群体中的"从众心理"
- **物理意义**：速度向量的平均化过程
- **参数调节**：
  - 对齐强度低 → 个体各自为政
  - 对齐强度高 → 群体行动一致

**现实类比**：就像高速公路上的车流，大家都朝着相同的方向行驶。

### 🧲 第三法则：聚合（Cohesion）
**"向群体中心靠拢，保持团结"**

- **生物学原理**：群体的"凝聚力"，安全感的来源
- **物理意义**：长程吸引力，维持群体完整性
- **参数调节**：
  - 聚合力弱 → 群体容易分散
  - 聚合力强 → 群体过度集中

**现实类比**：像磁铁吸引铁屑，群体成员被"看不见的力"拉向中心。

### ⚖️ 三力平衡：和谐的艺术

这三条法则的精妙之处在于它们的**相互制衡**：
- 分离力说："别靠太近！"
- 对齐力说："跟上大部队！"
- 聚合力说："别掉队！"

当这三种力达到动态平衡时，就产生了我们看到的优雅群体行为：既不会碰撞，也不会分散，还能保持统一的行动方向。

### 🎯 参数调节的艺术

调节这三个力的强度比例，可以创造出截然不同的群体行为：
- **紧密编队**：分离力小，聚合力大
- **松散游荡**：三力平衡，中等强度
- **快速机动**：对齐力强，反应敏捷

这就是为什么同样的算法既能模拟悠闲的鱼群，也能表现紧急避险的鸟群！
    `,
    keywords: ['分离', '对齐', '聚合', '参数调节', '力的平衡', '群体动力学'],
    relatedTopics: ['boids-history', 'boids-emergence', 'parameter-tuning']
  },
  {
    id: 'boids-emergence',
    topic: '无领导者的完美协调',
    simulationType: 'boids',
    level: 'intermediate',
    content: `
## 涌现：当1+1+1>3时

Boids模型最令人震撼的特性是**涌现行为**：个体遵循简单规则，群体却展现出令人惊叹的复杂行为。

### 🌊 涌现现象大观

**1. 集体转向**
- **个体行为**：只看邻居，调整方向
- **群体现象**：整体像单一生命体般优雅转弯
- **科学原理**：信息在网络中的快速传播

**2. 分流与合并**
- **遇到障碍**：群体自动分成两股，绕过障碍后重新汇合
- **无需规划**：没有任何个体"知道"整体策略
- **智能表现**：展现出类似"集体决策"的行为

**3. 层次结构**
- **子群形成**：大群体自发分割成多个小群体
- **动态重组**：子群之间不断分离、合并
- **自适应性**：根据环境自动调整群体大小

### 🧠 分布式智能

Boids系统展现了**分布式智能**的特征：

- **无中央控制**：没有"大脑"指挥整体行为
- **局部交互**：每个个体只与邻居交流
- **全局智能**：整体表现出智能行为

这种机制在现实世界中无处不在：
- **蚂蚁觅食**：单个蚂蚁很简单，蚁群却能找到最短路径
- **神经网络**：单个神经元不会思考，大脑却产生意识
- **市场经济**：个人追求利益，市场展现"看不见的手"

### 🔄 反馈循环的力量

Boids中存在多层反馈机制：

**正反馈**：
- 对齐行为加强 → 群体更统一 → 对齐效果更明显
- 聚合行为增强 → 密度提高 → 聚合力更强

**负反馈**：
- 密度过高 → 分离力增强 → 密度降低
- 速度过快 → 难以协调 → 自动减速

这些反馈循环使系统具备了**自稳定性**：系统会自动寻找最佳的运行状态。

### 🎮 参数空间的探索

不同的参数组合会产生截然不同的涌现行为：

**低聚合力 + 高分离力**：
- 结果：个体主义，群体容易分散
- 类比：现代都市中的人群

**高聚合力 + 低分离力**：
- 结果：过度拥挤，频繁碰撞
- 类比：恐慌中的人群

**平衡三力**：
- 结果：和谐流动，优雅协调
- 类比：训练有素的团队

### 🌟 复杂性的边界

最有趣的行为往往出现在**"混沌边缘"**：
- 秩序与混沌的临界点
- 参数的微小变化导致行为的剧烈改变
- 系统展现出最大的创造性和适应性

这启发我们思考：也许生命本身就存在于这样的临界状态中，在秩序与混沌之间寻找着完美的平衡。
    `,
    keywords: ['涌现行为', '分布式智能', '反馈循环', '自稳定性', '混沌边缘'],
    relatedTopics: ['boids-rules', 'complexity-science', 'swarm-robotics']
  },
  {
    id: 'boids-applications',
    topic: '从银幕到现实：Boids的无限可能',
    simulationType: 'boids',
    level: 'advanced',
    content: `
## 当虚拟照进现实

从雷诺兹最初的动画需求开始，Boids算法已经发展成为现代科技的重要基石，影响着我们生活的方方面面。

### 🎬 娱乐产业的革命

**好莱坞的新宠**：
- **《侏罗纪公园》**：恐龙群体的逼真行为
- **《指环王》**：万马奔腾的震撼战争场面
- **《海底总动员》**：鱼群的自然游弋
- **《阿凡达》**：外星生物的群体行为

**游戏产业的突破**：
- **RTS游戏**：军队的智能移动
- **MMORPG**：NPC群体的生动表现
- **模拟游戏**：生态系统的真实模拟

### 🤖 机器人学的未来

**无人机编队**：
- **军事应用**：协调侦察、集群攻击
- **民用服务**：搜索救援、货物配送
- **娱乐表演**：灯光秀、空中芭蕾

**地面机器人**：
- **仓储物流**：智能分拣、协调搬运
- **清洁服务**：多机器人协作清洁
- **探索任务**：火星探测器的协同工作

### 🚗 智能交通系统

**自动驾驶**：
- **车队协调**：高速公路上的智能编队
- **交叉路口**：无信号灯的智能通行
- **紧急避让**：集体规避障碍物

**交通流优化**：
- **拥堵预防**：基于Boids的流量控制
- **路径规划**：动态路线优化
- **事故处理**：智能疏散和绕行

### 🏢 建筑与城市规划

**人群疏散**：
- **紧急疏散**：建筑物的最优逃生路径
- **大型活动**：体育场馆的人流管理
- **公共交通**：地铁站的客流优化

**城市发展**：
- **商业区规划**：基于人流模式的商铺布局
- **居住区设计**：社区结构的优化
- **绿地规划**：公园和休闲区的分布

### 🧬 生物学研究

**动物行为学**：
- **迁徙模式**：候鸟、鱼类的迁徙路线研究
- **觅食行为**：群体觅食策略的分析
- **社会结构**：动物社群的组织形式

**生态系统建模**：
- **种群动态**：捕食者与猎物的关系
- **环境适应**：气候变化对群体行为的影响
- **保护策略**：濒危物种的保护措施

### 💰 金融与经济

**市场建模**：
- **股票交易**：交易者行为的群体模拟
- **市场波动**：恐慌和贪婪的传播模式
- **风险管理**：系统性风险的评估

**经济政策**：
- **消费行为**：消费者群体的决策模式
- **创新扩散**：新技术的市场接受过程
- **资源配置**：社会资源的优化分配

### 🌐 社交网络与传播

**信息传播**：
- **病毒营销**：信息在社交网络中的传播
- **舆论形成**：公众观点的演化过程
- **假新闻控制**：错误信息的传播机制

**社会现象**：
- **流行趋势**：时尚和文化的传播
- **集体行为**：群体心理的数字化研究
- **社会治理**：基于群体行为的政策制定

### 🔮 未来展望

随着人工智能和物联网的发展，Boids算法正在向更高层次演化：

- **认知Boids**：具备学习能力的智能个体
- **多层次Boids**：个体-群体-超群体的层次结构
- **适应性Boids**：能够根据环境自动调整规则
- **量子Boids**：利用量子计算的超级群体智能

雷诺兹的简单算法，正在成为连接虚拟与现实、个体与群体、简单与复杂的桥梁，引领我们走向一个更加智能、协调的未来世界。
    `,
    keywords: ['无人机编队', '自动驾驶', '人群疏散', '生态建模', '金融市场', '社交网络'],
    relatedTopics: ['swarm-robotics', 'smart-cities', 'computational-biology', 'AI-applications']
  }
];

/**
 * Ising模型知识库
 */
