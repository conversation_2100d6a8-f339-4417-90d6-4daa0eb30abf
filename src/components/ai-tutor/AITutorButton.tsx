import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Check, AlertCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import type { AITutorButtonProps } from '@/types/ai-tutor';
import AITutorSettings from './AITutorSettings';
import AITutorChat from './AITutorChat';
import DraggableChatWindow from './DraggableChatWindow';
import { useAITutorState } from './AITutorProvider';

const AITutorButton: React.FC<AITutorButtonProps> = ({ 
  className, 
  position = 'top-left' 
}) => {
  console.log('AITutorButton 渲染');
  
  const [showSettings, setShowSettings] = useState(false);
  const [showChat, setShowChat] = useState(false);
  
  let state;
  try {
    state = useAITutorState();
    console.log('AI状态:', state);
  } catch (error) {
    console.error('AI状态获取失败:', error);
    state = { isConfigured: false, isConnected: false, isLoading: false };
  }

  const positionClasses = {
    'top-left': 'top-4 left-4',
    'top-right': 'top-4 right-4',
    'bottom-left': 'bottom-4 left-4',
    'bottom-right': 'bottom-4 right-4',
  };

  const StatusIcon = () => {
    if (state?.error) {
      return <AlertCircle className="w-3 h-3 text-red-500" />;
    }
    if (state?.isConfigured && state?.isConnected) {
      return <Check className="w-3 h-3 text-green-500" />;
    }
    return <Settings className="w-3 h-3 text-yellow-500" />;
  };

  const handleButtonClick = () => {
    console.log('按钮点击, 状态:', state);
    
    if (!state) {
      alert('AI状态为空!');
      return;
    }
    
    if (!state.isConfigured) {
      console.log('打开设置');
      setShowSettings(true);
    } else {
      console.log('打开聊天');
      setShowChat(true);
    }
  };

  return (
    <>
      <div className={cn(
        "fixed z-50 group",
        positionClasses[position],
        className
      )}>
        <Button
          onClick={handleButtonClick}
          size="lg"
          className={cn(
            "relative rounded-full w-16 h-16 shadow-lg",
            "bg-gradient-to-r from-red-500 to-pink-500",
            "hover:from-red-600 hover:to-pink-600",
            "transition-all duration-300 ease-in-out",
            "hover:scale-110 hover:shadow-xl",
            "border-4 border-yellow-400"
          )}
        >
          <Bot className="w-6 h-6 text-white" />
          <span className="text-xs text-white ml-1">AI</span>
          
          <div className="absolute -top-1 -right-1 w-5 h-5 bg-white rounded-full flex items-center justify-center">
            <StatusIcon />
          </div>
          
          {state?.isLoading && (
            <div className="absolute inset-0 rounded-full border-2 border-white/30 border-t-white animate-spin" />
          )}
        </Button>
      </div>

      <AITutorSettings 
        open={showSettings} 
        onOpenChange={setShowSettings} 
      />

      <DraggableChatWindow
        isOpen={showChat}
        onClose={() => setShowChat(false)}
        onOpenSettings={() => {
          setShowChat(false);
          setShowSettings(true);
        }}
        title="AI导师"
      >
        <AITutorChat />
      </DraggableChatWindow>
    </>
  );
};

export default AITutorButton;
