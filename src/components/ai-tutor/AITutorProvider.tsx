import React, { createContext, useContext, useReducer, useEffect, ReactNode } from 'react';
import type { 
  AITutorState, 
  LLMConfig, 
  ChatMessage, 
  TutorMode, 
  SimulationContext,
  AITutorEvent
} from '@/types/ai-tutor';
import { APIKeyManager, ChatHistoryManager } from '@/utils/encryption';
import { DEFAULT_SYSTEM_PROMPT } from '@/utils/llm-client';

/**
 * AI导师全局状态管理
 * 提供跨组件的状态共享和持久化
 */

// Action类型定义
type AITutorAction = 
  | { type: 'SET_CONFIG'; payload: LLMConfig }
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_CONNECTED'; payload: boolean }
  | { type: 'SET_ERROR'; payload: string | null }
  | { type: 'SET_MODE'; payload: TutorMode }
  | { type: 'SET_SIMULATION_CONTEXT'; payload: SimulationContext | null }
  | { type: 'ADD_MESSAGE'; payload: ChatMessage }
  | { type: 'SET_MESSAGES'; payload: ChatMessage[] }
  | { type: 'CLEAR_HISTORY' }
  | { type: 'CLEAR_ALL_DATA' }
  | { type: 'LOAD_INITIAL_STATE'; payload: Partial<AITutorState> };

// 初始状态
const initialState: AITutorState = {
  isConfigured: false,
  isConnected: false,
  isLoading: false,
  currentMode: 'introduction',
  simulationContext: null,
  chatHistory: null,
  config: null,
  error: null,
};

// Reducer函数
function aiTutorReducer(state: AITutorState, action: AITutorAction): AITutorState {
  switch (action.type) {
    case 'SET_CONFIG':
      return {
        ...state,
        config: action.payload,
        isConfigured: true,
        error: null
      };

    case 'SET_LOADING':
      return {
        ...state,
        isLoading: action.payload
      };

    case 'SET_CONNECTED':
      return {
        ...state,
        isConnected: action.payload,
        error: action.payload ? null : state.error
      };

    case 'SET_ERROR':
      return {
        ...state,
        error: action.payload,
        isLoading: false
      };

    case 'SET_MODE':
      return {
        ...state,
        currentMode: action.payload
      };

    case 'SET_SIMULATION_CONTEXT':
      return {
        ...state,
        simulationContext: action.payload
      };

    case 'ADD_MESSAGE':
      const currentMessages = state.chatHistory?.messages || [];
      const newMessages = [...currentMessages, action.payload];
      return {
        ...state,
        chatHistory: {
          messages: newMessages,
          sessionId: state.chatHistory?.sessionId || 'main',
          createdAt: state.chatHistory?.createdAt || Date.now(),
          updatedAt: Date.now()
        }
      };

    case 'SET_MESSAGES':
      return {
        ...state,
        chatHistory: action.payload.length > 0 ? {
          messages: action.payload,
          sessionId: state.chatHistory?.sessionId || 'main',
          createdAt: state.chatHistory?.createdAt || Date.now(),
          updatedAt: Date.now()
        } : null
      };

    case 'CLEAR_HISTORY':
      return {
        ...state,
        chatHistory: null
      };

    case 'CLEAR_ALL_DATA':
      return {
        ...initialState
      };

    case 'LOAD_INITIAL_STATE':
      return {
        ...state,
        ...action.payload
      };

    default:
      return state;
  }
}

// Context类型定义
interface AITutorContextType {
  state: AITutorState;
  actions: {
    setConfig: (config: LLMConfig) => void;
    setLoading: (loading: boolean) => void;
    setConnected: (connected: boolean) => void;
    setError: (error: string | null) => void;
    setMode: (mode: TutorMode) => void;
    setSimulationContext: (context: SimulationContext | null) => void;
    addMessage: (message: ChatMessage) => void;
    setMessages: (messages: ChatMessage[]) => void;
    clearHistory: () => void;
    clearAllData: () => void;
    loadInitialState: () => void;
  };
  events: {
    emit: (event: AITutorEvent) => void;
    subscribe: (callback: (event: AITutorEvent) => void) => () => void;
  };
}

// 创建Context
const AITutorContext = createContext<AITutorContextType | undefined>(undefined);

// Provider组件
interface AITutorProviderProps {
  children: ReactNode;
}

export const AITutorProvider: React.FC<AITutorProviderProps> = ({ children }) => {
  const [state, dispatch] = useReducer(aiTutorReducer, initialState);
  
  // 事件监听器
  const [eventListeners, setEventListeners] = React.useState<Array<(event: AITutorEvent) => void>>([]);

  // 初始化时加载数据
  useEffect(() => {
    const loadInitialData = async () => {
      try {
        // 加载配置
        const storedConfig = APIKeyManager.retrieve();
        if (storedConfig) {
          dispatch({ type: 'SET_CONFIG', payload: storedConfig });
          // 如果有有效配置（包含 API 密钥），假设已连接
          // 实际使用时会在第一次发送消息时验证连接
          if (storedConfig.apiKey) {
            dispatch({ type: 'SET_CONNECTED', payload: true });
          }
        }

        // 加载聊天历史
        const chatHistory = ChatHistoryManager.load();
        if (chatHistory.length > 0) {
          dispatch({ type: 'SET_MESSAGES', payload: chatHistory });
        }

        // 发送初始化事件
        emitEvent({
          type: 'config_updated',
          payload: { initialized: true },
          timestamp: Date.now()
        });
      } catch (error) {
        console.error('初始化数据加载失败:', error);
        dispatch({ type: 'SET_ERROR', payload: '数据加载失败，请重新配置' });
      }
    };

    loadInitialData();
  }, []);

  // 自动保存聊天历史
  useEffect(() => {
    if (state.chatHistory && state.chatHistory.messages.length > 0) {
      try {
        ChatHistoryManager.save(state.chatHistory.messages);
      } catch (error) {
        console.error('聊天历史保存失败:', error);
      }
    }
  }, [state.chatHistory]);

  // 自动保存配置
  useEffect(() => {
    if (state.config) {
      try {
        APIKeyManager.store(state.config);
      } catch (error) {
        console.error('配置保存失败:', error);
        dispatch({ type: 'SET_ERROR', payload: '配置保存失败' });
      }
    }
  }, [state.config]);

  // 事件发射器
  const emitEvent = (event: AITutorEvent) => {
    eventListeners.forEach(listener => {
      try {
        listener(event);
      } catch (error) {
        console.error('事件监听器执行失败:', error);
      }
    });
  };

  // Action创建函数
  const actions = {
    setConfig: (config: LLMConfig) => {
      dispatch({ type: 'SET_CONFIG', payload: config });
      emitEvent({
        type: 'config_updated',
        payload: config,
        timestamp: Date.now()
      });
    },

    setLoading: (loading: boolean) => {
      dispatch({ type: 'SET_LOADING', payload: loading });
    },

    setConnected: (connected: boolean) => {
      dispatch({ type: 'SET_CONNECTED', payload: connected });
    },

    setError: (error: string | null) => {
      dispatch({ type: 'SET_ERROR', payload: error });
      if (error) {
        emitEvent({
          type: 'error_occurred',
          payload: { error },
          timestamp: Date.now()
        });
      }
    },

    setMode: (mode: TutorMode) => {
      dispatch({ type: 'SET_MODE', payload: mode });
      emitEvent({
        type: 'mode_changed',
        payload: { mode },
        timestamp: Date.now()
      });
    },

    setSimulationContext: (context: SimulationContext | null) => {
      dispatch({ type: 'SET_SIMULATION_CONTEXT', payload: context });
    },

    addMessage: (message: ChatMessage) => {
      dispatch({ type: 'ADD_MESSAGE', payload: message });
      emitEvent({
        type: message.role === 'user' ? 'message_sent' : 'message_received',
        payload: message,
        timestamp: Date.now()
      });
    },

    setMessages: (messages: ChatMessage[]) => {
      dispatch({ type: 'SET_MESSAGES', payload: messages });
    },

    clearHistory: () => {
      ChatHistoryManager.clear();
      dispatch({ type: 'CLEAR_HISTORY' });
    },

    clearAllData: () => {
      APIKeyManager.clear();
      ChatHistoryManager.clear();
      dispatch({ type: 'CLEAR_ALL_DATA' });
    },

    loadInitialState: () => {
      try {
        const storedConfig = APIKeyManager.retrieve();
        const chatHistory = ChatHistoryManager.load();
        
        const initialData: Partial<AITutorState> = {};
        
        if (storedConfig) {
          initialData.config = storedConfig;
          initialData.isConfigured = true;
          // 如果有有效配置（包含 API 密钥），假设已连接
          if (storedConfig.apiKey) {
            initialData.isConnected = true;
          }
        }
        
        if (chatHistory.length > 0) {
          initialData.chatHistory = {
            messages: chatHistory,
            sessionId: 'main',
            createdAt: Date.now(),
            updatedAt: Date.now()
          };
        }
        
        dispatch({ type: 'LOAD_INITIAL_STATE', payload: initialData });
      } catch (error) {
        console.error('加载初始状态失败:', error);
        dispatch({ type: 'SET_ERROR', payload: '数据加载失败' });
      }
    }
  };

  // 事件管理
  const events = {
    emit: emitEvent,
    subscribe: (callback: (event: AITutorEvent) => void) => {
      setEventListeners(prev => [...prev, callback]);
      
      // 返回取消订阅函数
      return () => {
        setEventListeners(prev => prev.filter(listener => listener !== callback));
      };
    }
  };

  const contextValue: AITutorContextType = {
    state,
    actions,
    events
  };

  return (
    <AITutorContext.Provider value={contextValue}>
      {children}
    </AITutorContext.Provider>
  );
};

// Hook for using the context
export const useAITutorContext = () => {
  const context = useContext(AITutorContext);
  if (context === undefined) {
    throw new Error('useAITutorContext must be used within an AITutorProvider');
  }
  return context;
};

// 便捷的Hook，只返回状态
export const useAITutorState = () => {
  const { state } = useAITutorContext();
  return state;
};

// 便捷的Hook，只返回actions
export const useAITutorActions = () => {
  const { actions } = useAITutorContext();
  return actions;
};

// 便捷的Hook，只返回events
export const useAITutorEvents = () => {
  const { events } = useAITutorContext();
  return events;
};

// 初始化系统提示词的Hook
export const useSystemPrompt = () => {
  const { state } = useAITutorContext();
  
  const getSystemPrompt = async (): Promise<string> => {
    if (state.config?.systemPrompt) {
      return state.config.systemPrompt;
    }
    
    // 根据当前上下文生成动态系统提示词
    let contextPrompt = DEFAULT_SYSTEM_PROMPT;
    
    if (state.simulationContext) {
      const simType = state.simulationContext.type;
      
      // 使用分立知识库获取上下文
      try {
        const { getKnowledgeBySimulation } = await import('./knowledge-bases');
        const relevantKnowledge = getKnowledgeBySimulation(simType);
        
        if (relevantKnowledge.length > 0) {
          const simName = getSimulationName(simType);
          
          contextPrompt += `\n\n## 当前模拟：${simName}
          
你现在正在为学生讲解${simName}。以下是相关的专业知识点：

${relevantKnowledge.slice(0, 3).map(item => `### ${item.topic}
${item.content.slice(0, 500)}...

`).join('')}

请结合这些知识点来回答学生的问题，并引导他们深入理解模拟的科学原理。`;
        }
      } catch (error) {
        console.error('加载知识库失败:', error);
      }
      
      // 添加边界检查功能
      try {
        const { generateBoundaryCheckPrompt } = await import('./BoundaryCheck');
        const boundaryPrompt = generateBoundaryCheckPrompt(simType);
        contextPrompt += boundaryPrompt;
      } catch (error) {
        console.error('加载边界检查失败:', error);
      }
    }
    
    return contextPrompt;
  };
  
  // 提供同步版本（不包含动态知识库内容）
  const getBasicSystemPrompt = (): string => {
    return state.config?.systemPrompt || DEFAULT_SYSTEM_PROMPT;
  };
  
  return { getSystemPrompt, getBasicSystemPrompt };
};

// 辅助函数：获取模拟类型的中文名称
function getSimulationName(type: string): string {
  const names: Record<string, string> = {
    'game-of-life': '生命游戏（Conway\'s Game of Life）',
    'boids': 'Boids群体行为仿真',
    'ising-model': 'Ising模型',
    'physarum': 'Physarum黏菌算法',
    'schelling': 'Schelling分离模型'
  };
  
  return names[type] || type;
}
