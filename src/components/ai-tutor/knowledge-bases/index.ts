/**
 * AI导师知识库索引
 * 整合所有模拟类型的知识内容
 */

import type { KnowledgeEntry, SimulationType } from './types';
import { GAME_OF_LIFE_KNOWLEDGE } from './game-of-life';
import { BOIDS_KNOWLEDGE } from './boids';
import { ISING_MODEL_KNOWLEDGE } from './ising-model';
import { PHYSARUM_KNOWLEDGE } from './physarum';
import { SCHELLING_KNOWLEDGE } from './schelling';

/**
 * 所有知识库的汇总
 */
export const ALL_KNOWLEDGE: KnowledgeEntry[] = [
  ...GAME_OF_LIFE_KNOWLEDGE,
  ...BOIDS_KNOWLEDGE,
  ...ISING_MODEL_KNOWLEDGE,
  ...PHYSARUM_KNOWLEDGE,
  ...SCHELLING_KNOWLEDGE
];

/**
 * 根据模拟类型获取相关知识
 */
export function getKnowledgeBySimulation(simulationType: SimulationType): KnowledgeEntry[] {
  return ALL_KNOWLEDGE.filter(entry => entry.simulationType === simulationType);
}

/**
 * 根据难度级别获取知识
 */
export function getKnowledgeByLevel(level: 'beginner' | 'intermediate' | 'advanced'): KnowledgeEntry[] {
  return ALL_KNOWLEDGE.filter(entry => entry.level === level);
}

/**
 * 根据关键词搜索知识
 */
export function searchKnowledge(keyword: string): KnowledgeEntry[] {
  const lowerKeyword = keyword.toLowerCase();
  return ALL_KNOWLEDGE.filter(entry => 
    entry.topic.toLowerCase().includes(lowerKeyword) ||
    entry.content.toLowerCase().includes(lowerKeyword) ||
    entry.keywords.some(k => k.toLowerCase().includes(lowerKeyword))
  );
}

/**
 * 根据ID获取知识条目
 */
export function getKnowledgeById(id: string): KnowledgeEntry | undefined {
  return ALL_KNOWLEDGE.find(entry => entry.id === id);
}

/**
 * 获取相关主题的知识
 */
export function getRelatedKnowledge(entryId: string): KnowledgeEntry[] {
  const entry = getKnowledgeById(entryId);
  if (!entry || !entry.relatedTopics) return [];
  
  return ALL_KNOWLEDGE.filter(otherEntry => 
    otherEntry.id !== entryId && 
    entry.relatedTopics!.some(topic => 
      otherEntry.keywords.includes(topic) || 
      otherEntry.topic.includes(topic)
    )
  );
}

// 导出各个模拟类型的知识库
export {
  GAME_OF_LIFE_KNOWLEDGE,
  BOIDS_KNOWLEDGE,
  ISING_MODEL_KNOWLEDGE,
  PHYSARUM_KNOWLEDGE,
  SCHELLING_KNOWLEDGE
};
