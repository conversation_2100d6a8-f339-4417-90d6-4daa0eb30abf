import type { KnowledgeEntry } from './types';

export const SCHELLING_KNOWLEDGE: KnowledgeEntry[] = [
  {
    id: 'schelling-history',
    topic: '谢林分离模型的历史背景',
    simulationType: 'schelling',
    level: 'beginner',
    content: `## 谢林分离模型：从种族隔离到社会动力学

### 模型诞生背景
托马斯·谢林(<PERSON>, 1921-2016)是美国著名经济学家，2005年诺贝尔经济学奖得主。

1971年，他在研究美国城市种族隔离现象时提出了著名的分离模型。

### 核心洞察
即使个体只有轻微的偏好，最终也可能导致完全的种族隔离。

### 基本设置
- 棋盘状的居住网格
- 两种类型的居民
- 基于邻居构成的迁移决策

### 理论意义
开创了基于个体行为解释宏观社会现象的新范式。`,
    keywords: ['托马斯谢林', '种族隔离', '阈值效应', '复杂性科学'],
    relatedTopics: ['博弈论', '城市社会学', '复杂系统']
  },
  {
    id: 'schelling-mechanism',
    topic: '阈值效应与分离机制',
    simulationType: 'schelling',
    level: 'intermediate',
    content: `## 阈值效应：微观偏好如何塑造宏观模式

### 阈值定义
个体愿意接受的最低同类邻居比例。

### 分离动力学
1. 初始扰动
2. 连锁反应  
3. 聚集加速
4. 稳定分离

### 临界现象
存在临界阈值，超过后系统快速转向分离状态。

### 空间模式
- 斑块状分离
- 带状分离
- 核心-边缘结构

### 现实验证
城市住房、学校教育、职场等领域都观察到类似现象。`,
    keywords: ['阈值效应', '连锁反应', '相变', '空间模式'],
    relatedTopics: ['非线性动力学', '空间统计学', '社会物理学']
  },
  {
    id: 'schelling-modern',
    topic: '现代社会中的谢林动态',
    simulationType: 'schelling',
    level: 'intermediate',
    content: `## 数字时代的新分离

### 社交媒体分离
算法推荐创造了新形式的谢林分离：
- 信息茧房效应
- 观点极化
- 数字分离

### 职场性别分离
STEM领域的性别分离现象：
- 角色模型缺乏
- 工作环境适应
- 自我强化机制

### 城市空间分离
远程工作带来新的地理重组：
- 收入驱动迁移
- 数字鸿沟空间化

### 应对策略
- 算法设计责任
- 多样性管理
- 包容性设计`,
    keywords: ['社交媒体', '算法推荐', '数字分离', '职场性别'],
    relatedTopics: ['数字社会学', '算法伦理', '城市社会学']
  },
  {
    id: 'schelling-complexity',
    topic: '复杂性科学中的谢林模型',
    simulationType: 'schelling',
    level: 'advanced',
    content: `## 复杂系统的谢林特征

### 非线性动力学
- 相变现象
- 滞后效应
- 多稳态

### 涌现性质
- 宏观模式从微观规则涌现
- 非预期后果
- 集体行为

### 网络效应
- 社交网络放大
- 信息传播影响
- 级联效应

### 政策启示
- 传统干预局限性
- 复杂系统思维
- 多层干预策略

### 未来方向
人工智能与算法分离带来新挑战。`,
    keywords: ['复杂性科学', '涌现现象', '非线性动力学', '网络效应'],
    relatedTopics: ['复杂系统理论', '网络科学', '计算社会科学']
  }
];

export default SCHELLING_KNOWLEDGE;
