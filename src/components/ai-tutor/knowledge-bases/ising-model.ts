import type { KnowledgeEntry } from './types';

export const ISING_MODEL_KNOWLEDGE: KnowledgeEntry[] = [
  {
    id: 'ising-history',
    topic: '伊辛模型的历史起源',
    simulationType: 'ising-model',
    level: 'beginner',
    content: `## 伊辛模型：磁性的数学密码

### 历史背景
恩斯特·伊辛(<PERSON>, 1900-1998)于1925年在其博士论文中提出了这个模型。

### 基本概念
- 晶格上的自旋系统
- 相邻自旋的相互作用
- 温度对磁化的影响

### 物理意义
描述铁磁材料的相变现象，是统计物理学的经典模型。`,
    keywords: ['恩斯特伊辛', '磁性', '相变', '统计物理'],
    relatedTopics: ['热力学', '相变理论', '统计力学']
  },
  {
    id: 'ising-physics',
    topic: '伊辛模型的物理机制',
    simulationType: 'ising-model',
    level: 'intermediate',
    content: `## 自旋相互作用与相变

### 哈密顿量
描述系统能量的数学表达式。

### 相变现象
- 铁磁相：低温有序态
- 顺磁相：高温无序态
- 临界温度：相变发生点

### 临界现象
接近相变点时的普适行为。`,
    keywords: ['哈密顿量', '铁磁相', '顺磁相', '临界温度'],
    relatedTopics: ['相变理论', '临界现象', '重整化群']
  },
  {
    id: 'ising-applications',
    topic: '伊辛模型的现代应用',
    simulationType: 'ising-model',
    level: 'advanced',
    content: `## 从磁性到复杂系统

### 生物应用
- 神经网络模型
- 蛋白质折叠
- 生态系统动力学

### 社会科学
- 舆论动力学
- 社会网络分析
- 集体行为研究

### 计算应用
- 优化问题
- 机器学习
- 量子计算`,
    keywords: ['神经网络', '舆论动力学', '优化问题', '量子计算'],
    relatedTopics: ['复杂网络', '机器学习', '量子算法']
  }
];

export default ISING_MODEL_KNOWLEDGE;
