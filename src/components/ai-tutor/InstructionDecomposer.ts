import { ToolCall } from '@/lib/llm/types';
import { AIControlResult } from './types';

/**
 * 指令步骤接口
 */
export interface InstructionStep {
  id: string;
  description: string;
  toolCalls: ToolCall[];
  status: 'pending' | 'executing' | 'completed' | 'failed';
  result?: AIControlResult[];
  timestamp?: number;
}

/**
 * 分解后的指令序列
 */
export interface DecomposedInstructions {
  id: string;
  originalMessage: string;
  steps: InstructionStep[];
  currentStepIndex: number;
  isCompleted: boolean;
  createdAt: number;
}

/**
 * 指令分解器 - 将复杂指令分解为多个基本操作步骤
 */
export class InstructionDecomposer {
  private static instance: InstructionDecomposer;
  private currentInstructions: DecomposedInstructions | null = null;

  private constructor() {}

  static getInstance(): InstructionDecomposer {
    if (!InstructionDecomposer.instance) {
      InstructionDecomposer.instance = new InstructionDecomposer();
    }
    return InstructionDecomposer.instance;
  }

  /**
   * 分析用户消息，判断是否需要分解
   */
  analyzeUserMessage(message: string, toolCalls: ToolCall[]): {
    needsDecomposition: boolean;
    reason?: string;
  } {
    console.log('[指令分解器] 分析用户消息:', message);
    console.log('[指令分解器] 函数调用数量:', toolCalls.length);
    console.log('[指令分解器] 函数调用列表:', toolCalls.map(call => call.function.name));



    if (toolCalls.length <= 1) {
      console.log('[指令分解器] 只有一个函数调用，无需分解');
      return { needsDecomposition: false };
    }

    // 检查是否包含参数设置和仿真控制的组合
    const parameterCalls = toolCalls.filter(call =>
      this.isParameterSettingCall(call.function.name)
    );

    const controlCalls = toolCalls.filter(call =>
      this.isSimulationControlCall(call.function.name)
    );

    console.log('[指令分解器] 参数设置调用:', parameterCalls.map(call => call.function.name));
    console.log('[指令分解器] 控制调用:', controlCalls.map(call => call.function.name));

    if (parameterCalls.length > 0 && controlCalls.length > 0) {
      console.log('[指令分解器] 检测到参数设置和仿真控制的组合，需要分解');
      return {
        needsDecomposition: true,
        reason: '检测到参数设置和仿真控制的组合操作，需要分步执行'
      };
    }

    // 检查是否有多个参数设置
    if (parameterCalls.length > 1) {
      console.log('[指令分解器] 检测到多个参数设置，需要分解');
      return {
        needsDecomposition: true,
        reason: '检测到多个参数设置操作，建议分步执行以确保每个参数正确设置'
      };
    }

    console.log('[指令分解器] 无需分解');
    return { needsDecomposition: false };
  }

  /**
   * 将函数调用分解为步骤
   */
  decomposeInstructions(
    userMessage: string, 
    toolCalls: ToolCall[]
  ): DecomposedInstructions {
    const instructionId = `inst_${Date.now()}`;
    const steps: InstructionStep[] = [];

    // 按优先级分组：参数设置 -> 仿真控制
    const parameterCalls = toolCalls.filter(call => 
      this.isParameterSettingCall(call.function.name)
    );
    
    const controlCalls = toolCalls.filter(call =>
      this.isSimulationControlCall(call.function.name)
    );

    const otherCalls = toolCalls.filter(call =>
      !this.isParameterSettingCall(call.function.name) &&
      !this.isSimulationControlCall(call.function.name)
    );

    // 创建参数设置步骤
    if (parameterCalls.length > 0) {
      // 每个参数设置作为一个独立步骤
      parameterCalls.forEach((call, index) => {
        steps.push({
          id: `step_param_${index}`,
          description: this.getStepDescription(call),
          toolCalls: [call],
          status: 'pending'
        });
      });
    }

    // 创建其他操作步骤
    if (otherCalls.length > 0) {
      steps.push({
        id: 'step_other',
        description: '执行其他操作',
        toolCalls: otherCalls,
        status: 'pending'
      });
    }

    // 创建仿真控制步骤
    if (controlCalls.length > 0) {
      steps.push({
        id: 'step_control',
        description: '控制仿真运行',
        toolCalls: controlCalls,
        status: 'pending'
      });
    }

    const decomposed: DecomposedInstructions = {
      id: instructionId,
      originalMessage: userMessage,
      steps,
      currentStepIndex: 0,
      isCompleted: false,
      createdAt: Date.now()
    };

    this.currentInstructions = decomposed;
    return decomposed;
  }

  /**
   * 获取当前需要执行的步骤
   */
  getCurrentStep(): InstructionStep | null {
    if (!this.currentInstructions || this.currentInstructions.isCompleted) {
      return null;
    }

    const currentIndex = this.currentInstructions.currentStepIndex;
    if (currentIndex >= this.currentInstructions.steps.length) {
      return null;
    }

    return this.currentInstructions.steps[currentIndex];
  }

  /**
   * 标记当前步骤完成并移动到下一步
   */
  completeCurrentStep(result: AIControlResult[]): {
    hasNextStep: boolean;
    nextStep?: InstructionStep;
    isAllCompleted: boolean;
  } {
    if (!this.currentInstructions) {
      return { hasNextStep: false, isAllCompleted: true };
    }

    const currentIndex = this.currentInstructions.currentStepIndex;
    const currentStep = this.currentInstructions.steps[currentIndex];
    
    if (currentStep) {
      currentStep.status = 'completed';
      currentStep.result = result;
      currentStep.timestamp = Date.now();
    }

    // 移动到下一步
    this.currentInstructions.currentStepIndex++;
    
    const nextIndex = this.currentInstructions.currentStepIndex;
    const hasNextStep = nextIndex < this.currentInstructions.steps.length;
    
    if (!hasNextStep) {
      this.currentInstructions.isCompleted = true;
    }

    return {
      hasNextStep,
      nextStep: hasNextStep ? this.currentInstructions.steps[nextIndex] : undefined,
      isAllCompleted: !hasNextStep
    };
  }

  /**
   * 获取当前指令的进度信息
   */
  getProgress(): {
    currentStep: number;
    totalSteps: number;
    completedSteps: number;
    isActive: boolean;
  } | null {
    if (!this.currentInstructions) {
      return null;
    }

    const completedSteps = this.currentInstructions.steps.filter(
      step => step.status === 'completed'
    ).length;

    return {
      currentStep: this.currentInstructions.currentStepIndex + 1,
      totalSteps: this.currentInstructions.steps.length,
      completedSteps,
      isActive: !this.currentInstructions.isCompleted
    };
  }

  /**
   * 清除当前指令
   */
  clearCurrentInstructions(): void {
    this.currentInstructions = null;
  }

  /**
   * 判断是否为参数设置调用
   */
  private isParameterSettingCall(functionName: string): boolean {
    const parameterKeywords = [
      // 通用参数设置关键词
      'setGridSize', 'setSimilarityThreshold', 'setPopulationDensity',
      'setGroupRatio', 'setTemperature', 'setSpeed', 'setNumBoids',
      'setSeparation', 'setAlignment', 'setCohesion', 'setMaxSpeed',
      'setNumAgents', 'setSensorAngle', 'setSensorDistance', 'setRotationAngle',

      // 实际函数名中的关键词（下划线格式）
      'set_grid_size', 'set_similarity_threshold', 'set_density', 'set_group_ratio',
      'set_temperature', 'set_speed', 'set_num_boids', 'set_separation',
      'set_alignment', 'set_cohesion', 'set_max_speed', 'set_num_agents',
      'set_sensor_angle', 'set_sensor_distance', 'set_rotation_angle',
      'set_magnetic_field'
    ];

    return parameterKeywords.some(keyword => functionName.includes(keyword));
  }

  /**
   * 判断是否为仿真控制调用
   */
  private isSimulationControlCall(functionName: string): boolean {
    const controlKeywords = [
      // 通用控制关键词
      'startSimulation', 'pauseSimulation', 'stopSimulation',
      'runSimulation', 'play', 'pause', 'stop', 'step', 'reset',

      // 实际函数名中的关键词
      'start', 'pause', 'stop', 'step', 'reset', 'randomize',
      'toggle_play', 'play_pause'
    ];

    return controlKeywords.some(keyword => functionName.includes(keyword));
  }

  /**
   * 获取步骤描述
   */
  private getStepDescription(toolCall: ToolCall): string {
    const functionName = toolCall.function.name;

    // 网格/尺寸设置
    if (functionName.includes('setGridSize') || functionName.includes('set_grid_size')) {
      return '设置网格大小';
    }
    // 相似性阈值
    else if (functionName.includes('setSimilarityThreshold') || functionName.includes('set_similarity_threshold')) {
      return '设置相似性阈值';
    }
    // 人口密度
    else if (functionName.includes('setPopulationDensity') || functionName.includes('set_density')) {
      return '设置人口密度';
    }
    // 群体比例
    else if (functionName.includes('setGroupRatio') || functionName.includes('set_group_ratio')) {
      return '设置群体比例';
    }
    // 温度设置
    else if (functionName.includes('setTemperature') || functionName.includes('set_temperature')) {
      return '设置温度';
    }
    // 启动仿真
    else if (functionName.includes('startSimulation') || functionName.includes('start') || functionName.includes('play')) {
      return '启动仿真';
    }
    // 暂停仿真
    else if (functionName.includes('pauseSimulation') || functionName.includes('pause')) {
      return '暂停仿真';
    }
    // 重置仿真
    else if (functionName.includes('reset')) {
      return '重置仿真';
    }
    // 单步执行
    else if (functionName.includes('step')) {
      return '单步执行';
    }

    return `执行 ${functionName}`;
  }
}

export const instructionDecomposer = InstructionDecomposer.getInstance();
