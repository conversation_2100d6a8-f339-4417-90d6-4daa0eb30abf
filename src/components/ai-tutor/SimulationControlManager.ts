/**
 * AI导师模拟控制管理器
 * 处理AI导师对各种模拟的控制操作
 */

import type { 
  SimulationControls, 
  AIControlCommand, 
  AIControlResult,
  GameOfLifeControls,
  BoidsControls,
  IsingControls,
  PhysarumControls,
  SchellingControls
} from '@/types/simulation-controls';

export class SimulationControlManager {
  private registeredControls: Map<string, SimulationControls> = new Map();
  private currentSimulation: string | null = null;

  /**
   * 注册模拟控制接口
   */
  registerControls(simulationType: string, controls: SimulationControls): void {
    this.registeredControls.set(simulationType, controls);
    // 只在开发环境输出日志
    if (process.env.NODE_ENV === 'development') {
      console.log(`[AI导师] 注册了${simulationType}的控制接口`);
    }
  }

  /**
   * 取消注册模拟控制接口
   */
  unregisterControls(simulationType: string): void {
    this.registeredControls.delete(simulationType);
    // 只在开发环境输出日志
    if (process.env.NODE_ENV === 'development') {
      console.log(`[AI导师] 取消注册${simulationType}的控制接口`);
    }
  }

  /**
   * 设置当前活跃的模拟
   */
  setCurrentSimulation(simulationType: string): void {
    this.currentSimulation = simulationType;
    console.log(`[AI导师] 切换到${simulationType}模拟`);
  }

  /**
   * 获取当前模拟的状态
   */
  getCurrentState(): any {
    if (!this.currentSimulation) {
      return null;
    }

    const controls = this.registeredControls.get(this.currentSimulation);
    if (!controls) {
      return null;
    }

    try {
      switch (controls.type) {
        case 'game-of-life':
          return (controls.controls as GameOfLifeControls).getState();
        case 'boids':
          return (controls.controls as BoidsControls).getState();
        case 'ising':
          return (controls.controls as IsingControls).getState();
        case 'physarum':
          return (controls.controls as PhysarumControls).getState();
        case 'schelling':
          return (controls.controls as SchellingControls).getState();
        default:
          return null;
      }
    } catch (error) {
      console.error('[AI导师] 获取状态失败:', error);
      return null;
    }
  }

  /**
   * 执行AI控制命令
   */
  async executeCommand(command: AIControlCommand): Promise<AIControlResult> {
    const { simulationType, action, parameters } = command;

    console.log(`[AI导师控制] 执行命令:`, { simulationType, action, parameters });

    // 检查是否有注册的控制接口
    const controls = this.registeredControls.get(simulationType);
    if (!controls) {
      console.error(`[AI导师控制] 未找到${simulationType}的控制接口，已注册的控制:`, Array.from(this.registeredControls.keys()));
      return {
        success: false,
        message: `未找到${simulationType}的控制接口`,
        error: 'NO_CONTROLS_REGISTERED'
      };
    }

    console.log(`[AI导师控制] 找到${simulationType}的控制接口，类型:`, controls.type);

    try {
      let result: AIControlResult;

      switch (controls.type) {
        case 'game-of-life':
          result = await this.executeGameOfLifeCommand(
            controls.controls as GameOfLifeControls, 
            action, 
            parameters
          );
          break;
        case 'boids':
          result = await this.executeBoidsCommand(
            controls.controls as BoidsControls, 
            action, 
            parameters
          );
          break;
        case 'ising':
          result = await this.executeIsingCommand(
            controls.controls as IsingControls, 
            action, 
            parameters
          );
          break;
        case 'physarum':
          result = await this.executePhysarumCommand(
            controls.controls as PhysarumControls, 
            action, 
            parameters
          );
          break;
        case 'schelling':
          result = await this.executeSchellingCommand(
            controls.controls as SchellingControls, 
            action, 
            parameters
          );
          break;
        default:
          result = {
            success: false,
            message: `不支持的模拟类型: ${(controls as any).type}`,
            error: 'UNSUPPORTED_SIMULATION_TYPE'
          };
      }

      // 获取执行后的状态
      if (result.success) {
        result.newState = this.getCurrentState();
        console.log(`[AI导师控制] 命令执行成功:`, result);
      } else {
        console.error(`[AI导师控制] 命令执行失败:`, result);
      }

      return result;
    } catch (error) {
      console.error('[AI导师] 执行命令失败:', error);
      return {
        success: false,
        message: `执行命令失败: ${error instanceof Error ? error.message : '未知错误'}`,
        error: 'EXECUTION_ERROR'
      };
    }
  }

  /**
   * 执行生命游戏控制命令
   */
  private async executeGameOfLifeCommand(
    controls: GameOfLifeControls, 
    action: string, 
    parameters?: any
  ): Promise<AIControlResult> {
    switch (action) {
      case 'play':
      case 'start':
        // 使用新的startSimulation方法
        const result = controls.startSimulation();
        return { 
          success: result.success, 
          message: '开始运行生命游戏',
          currentState: result.currentState
        };
      
      case 'pause':
      case 'stop':
        // 使用新的pauseSimulation方法
        const pauseResult = controls.pauseSimulation();
        return { 
          success: pauseResult.success, 
          message: '暂停生命游戏',
          currentState: pauseResult.currentState
        };
      
      case 'step':
      case 'stepwiseSimulation':
        // 使用新的stepwiseSimulation方法
        const stepResult = controls.stepwiseSimulation();
        return { 
          success: stepResult.success, 
          message: '执行一步生命游戏',
          currentState: stepResult.currentState
        };
      
      case 'runMultipleSteps':
        if (parameters?.steps !== undefined) {
          const stepsResult = controls.runMultipleSteps(parameters.steps);
          return { 
            success: stepsResult.success, 
            message: stepsResult.success ? `成功执行了${parameters.steps}步` : stepsResult.message || '执行失败',
            currentState: stepsResult.currentState
          };
        }
        return { success: false, message: '缺少步数参数' };
      
      case 'reset':
      case 'resetSimulation':
        // 使用新的resetSimulation方法
        const resetResult = controls.resetSimulation();
        return { 
          success: resetResult.success, 
          message: '重置生命游戏',
          currentState: resetResult.currentState
        };
      
      case 'setSpeed':
      case 'setSimulationSpeed':
        if (parameters?.speed !== undefined) {
          const speedResult = controls.setSimulationSpeed(parameters.speed);
          return { 
            success: speedResult.success, 
            message: `设置速度为 ${speedResult.actualValue}`,
            actualValue: speedResult.actualValue
          };
        }
        return { success: false, message: '缺少速度参数' };
      
      case 'setRules':
      case 'setLifeRules':
        if (parameters?.rules) {
          console.log(`[AI导师控制] 设置生命游戏规则:`, parameters.rules);
          const rulesResult = controls.setLifeRules(parameters.rules);
          return { 
            success: rulesResult.success, 
            message: '更新生命游戏规则',
            actualValue: rulesResult.actualValue
          };
        }
        console.error(`[AI导师控制] 缺少规则参数:`, parameters);
        return { success: false, message: '缺少规则参数' };
      
      case 'loadPattern':
      case 'loadPresetPattern':
        if (parameters?.pattern) {
          const patternResult = controls.loadPresetPattern(parameters.pattern);
          return { 
            success: patternResult.success, 
            message: `加载模式: ${patternResult.actualValue || parameters.pattern}`,
            actualValue: patternResult.actualValue || parameters.pattern
          };
        }
        return { success: false, message: '缺少模式参数' };
      
      case 'setColors':
        let message = '更新颜色设置: ';
        let success = true;
        if (parameters?.liveCellColor) {
          const liveColorResult = controls.setLiveCellColor(parameters.liveCellColor);
          if (!liveColorResult.success) {
            success = false;
            message += '活细胞颜色设置失败 ';
          } else {
            message += `活细胞颜色: ${liveColorResult.actualValue} `;
          }
        }
        if (parameters?.deadCellColor) {
          const deadColorResult = controls.setDeadCellColor(parameters.deadCellColor);
          if (!deadColorResult.success) {
            success = false;
            message += '死细胞颜色设置失败 ';
          } else {
            message += `死细胞颜色: ${deadColorResult.actualValue} `;
          }
        }
        return { success, message: message.trim() };
      
      default:
        return { success: false, message: `不支持的操作: ${action}` };
    }
  }

  /**
   * 执行Boids仿真控制命令
   */
  private async executeBoidsCommand(
    controls: BoidsControls, 
    action: string, 
    parameters?: any
  ): Promise<AIControlResult> {
    switch (action) {
      case 'play':
      case 'start':
        const currentState = controls.getState();
        if (!currentState.isRunning) {
          controls.togglePlay();
        }
        return { success: true, message: '开始Boids仿真' };
      
      case 'pause':
      case 'stop':
        const currentStateForPause = controls.getState();
        if (currentStateForPause.isRunning) {
          controls.togglePlay();
        }
        return { success: true, message: '暂停Boids仿真' };
      
      case 'reset':
        controls.reset();
        return { success: true, message: '重置Boids仿真' };
      
      case 'setNumBoids':
        if (parameters?.count !== undefined) {
          controls.setNumBoids(parameters.count);
          return { success: true, message: `设置鸟群数量为 ${parameters.count}` };
        }
        return { success: false, message: '缺少数量参数' };
      
      case 'setSeparation':
        if (parameters?.value !== undefined) {
          controls.setSeparation(parameters.value);
          return { success: true, message: `设置分离权重为 ${parameters.value}` };
        }
        return { success: false, message: '缺少分离参数' };
      
      case 'setAlignment':
        if (parameters?.value !== undefined) {
          controls.setAlignment(parameters.value);
          return { success: true, message: `设置对齐权重为 ${parameters.value}` };
        }
        return { success: false, message: '缺少对齐参数' };
      
      case 'setCohesion':
        if (parameters?.value !== undefined) {
          controls.setCohesion(parameters.value);
          return { success: true, message: `设置聚合权重为 ${parameters.value}` };
        }
        return { success: false, message: '缺少聚合参数' };
      
      default:
        return { success: false, message: `不支持的操作: ${action}` };
    }
  }

  /**
   * 执行Ising模型控制命令
   */
  private async executeIsingCommand(
    controls: IsingControls, 
    action: string, 
    parameters?: any
  ): Promise<AIControlResult> {
    switch (action) {
      case 'play':
      case 'start':
        // 使用新的startSimulation方法
        const result = controls.startSimulation();
        return { 
          success: result.success, 
          message: '开始Ising模型仿真',
          currentState: result.currentState
        };
      
      case 'pause':
      case 'stop':
        // 使用新的pauseSimulation方法
        const pauseResult = controls.pauseSimulation();
        return { 
          success: pauseResult.success, 
          message: '暂停Ising模型仿真',
          currentState: pauseResult.currentState
        };
      
      case 'step':
        // 使用新的stepwiseSimulation方法
        const stepResult = controls.stepwiseSimulation();
        return { 
          success: stepResult.success, 
          message: stepResult.success ? '执行一步Ising模型' : stepResult.message || '单步执行失败',
          currentState: stepResult.currentState
        };
      
      case 'reset':
        // 使用新的resetSimulation方法
        const resetResult = controls.resetSimulation();
        return { 
          success: resetResult.success, 
          message: resetResult.success ? '重置Ising模型' : resetResult.message || '重置失败',
          currentState: resetResult.currentState
        };
      
      case 'setTemperature':
      case 'setTemperatureValue':
        if (parameters?.temperature !== undefined) {
          const tempResult = controls.setTemperatureValue(parameters.temperature);
          return { 
            success: tempResult.success, 
            message: `设置温度为 ${tempResult.actualValue}`,
            actualValue: tempResult.actualValue
          };
        }
        return { success: false, message: '缺少温度参数' };
      
      case 'setMagneticField':
      case 'setExternalMagneticField':
        if (parameters?.field !== undefined) {
          const fieldResult = controls.setExternalMagneticField(parameters.field);
          return { 
            success: fieldResult.success, 
            message: `设置外磁场为 ${fieldResult.actualValue}`,
            actualValue: fieldResult.actualValue
          };
        }
        return { success: false, message: '缺少磁场参数' };
      
      case 'setSpeed':
      case 'setSimulationSpeed':
        if (parameters?.speed !== undefined) {
          const speedResult = controls.setSimulationSpeed(parameters.speed);
          return { 
            success: speedResult.success, 
            message: `设置仿真速度为 ${speedResult.actualValue}`,
            actualValue: speedResult.actualValue
          };
        }
        return { success: false, message: '缺少速度参数' };
      
      case 'randomize':
      case 'randomizeSpins':
        const randomResult = controls.randomizeSpins();
        return { 
          success: randomResult.success, 
          message: randomResult.success ? '随机化自旋状态' : randomResult.message || '随机化失败',
          actualValue: randomResult.actualValue
        };
      
      case 'setAllUp':
      case 'setAllSpinsUp':
        const upResult = controls.setAllSpinsUp();
        return { 
          success: upResult.success, 
          message: upResult.success ? '设置所有自旋向上' : upResult.message || '设置失败',
          actualValue: upResult.actualValue
        };
      
      case 'setAllDown':
      case 'setAllSpinsDown':
        const downResult = controls.setAllSpinsDown();
        return { 
          success: downResult.success, 
          message: downResult.success ? '设置所有自旋向下' : downResult.message || '设置失败',
          actualValue: downResult.actualValue
        };
      
      case 'setGridSize':
        if (parameters?.size !== undefined) {
          const sizeResult = controls.setGridSize(parameters.size);
          return { 
            success: sizeResult.success, 
            message: `设置网格尺寸为 ${sizeResult.actualValue}`,
            actualValue: sizeResult.actualValue
          };
        }
        return { success: false, message: '缺少尺寸参数' };
      
      case 'setThermalMotion':
        if (parameters?.enabled !== undefined) {
          const thermalResult = controls.setThermalMotion(parameters.enabled);
          return { 
            success: thermalResult.success, 
            message: `设置热运动为 ${thermalResult.actualValue ? '开启' : '关闭'}`,
            actualValue: thermalResult.actualValue
          };
        }
        return { success: false, message: '缺少热运动参数' };
      
      case 'setThermalWeight':
        if (parameters?.weight !== undefined) {
          const weightResult = controls.setThermalWeight(parameters.weight);
          return { 
            success: weightResult.success, 
            message: `设置热运动权重为 ${weightResult.actualValue}`,
            actualValue: weightResult.actualValue
          };
        }
        return { success: false, message: '缺少权重参数' };
      
      case 'setThermalizationSteps':
        if (parameters?.steps !== undefined) {
          const thermalizationResult = controls.setThermalizationSteps(parameters.steps);
          return { 
            success: thermalizationResult.success, 
            message: `设置热化步数为 ${thermalizationResult.actualValue}`,
            actualValue: thermalizationResult.actualValue
          };
        }
        return { success: false, message: '缺少热化步数参数' };
      
      case 'setSamplingSteps':
        if (parameters?.steps !== undefined) {
          const samplingResult = controls.setSamplingSteps(parameters.steps);
          return { 
            success: samplingResult.success, 
            message: `设置采样步数为 ${samplingResult.actualValue}`,
            actualValue: samplingResult.actualValue
          };
        }
        return { success: false, message: '缺少采样步数参数' };
      
      case 'setSpinsUpColor':
        if (parameters?.color !== undefined) {
          const upColorResult = controls.setSpinsUpColor(parameters.color);
          return { 
            success: upColorResult.success, 
            message: `设置自旋向上颜色为 ${upColorResult.actualValue}`,
            actualValue: upColorResult.actualValue
          };
        }
        return { success: false, message: '缺少颜色参数' };
      
      case 'setSpinsDownColor':
        if (parameters?.color !== undefined) {
          const downColorResult = controls.setSpinsDownColor(parameters.color);
          return { 
            success: downColorResult.success, 
            message: `设置自旋向下颜色为 ${downColorResult.actualValue}`,
            actualValue: downColorResult.actualValue
          };
        }
        return { success: false, message: '缺少颜色参数' };
      
      case 'clearPlots':
        const clearResult = controls.clearPlots();
        return { 
          success: clearResult.success, 
          message: clearResult.success ? '清空图表' : clearResult.message || '清空失败',
          actualValue: clearResult.actualValue
        };
      
      default:
        return { success: false, message: `不支持的操作: ${action}` };
    }
  }

  /**
   * 执行Physarum仿真控制命令
   */
  private async executePhysarumCommand(
    controls: PhysarumControls, 
    action: string, 
    parameters?: any
  ): Promise<AIControlResult> {
    switch (action) {
      case 'play':
      case 'start':
        const currentState = controls.getState();
        if (!currentState.isRunning) {
          controls.togglePlay();
        }
        return { success: true, message: '开始Physarum仿真' };
      
      case 'pause':
      case 'stop':
        const currentStateForPause = controls.getState();
        if (currentStateForPause.isRunning) {
          controls.togglePlay();
        }
        return { success: true, message: '暂停Physarum仿真' };
      
      case 'reset':
        controls.reset();
        return { success: true, message: '重置Physarum仿真' };
      
      case 'setNumAgents':
        if (parameters?.count !== undefined) {
          controls.setNumAgents(parameters.count);
          return { success: true, message: `设置代理数量为 ${parameters.count}` };
        }
        return { success: false, message: '缺少数量参数' };
      
      default:
        return { success: false, message: `不支持的操作: ${action}` };
    }
  }

  /**
   * 执行Schelling模型控制命令
   */
  private async executeSchellingCommand(
    controls: SchellingControls, 
    action: string, 
    parameters?: any
  ): Promise<AIControlResult> {
    console.log(`[SimulationControlManager] 执行Schelling命令: ${action}`, parameters);
    
    try {
      switch (action) {
        // 兼容性接口
        case 'play':
        case 'start':
          const startResult = controls.startSimulation?.(parameters);
          return { 
            success: startResult?.success ?? true, 
            message: startResult?.success ? '开始Schelling模型仿真' : '启动失败',
            currentState: startResult?.currentState
          };
        
        case 'pause':
        case 'stop':
          const pauseResult = controls.pauseSimulation?.(parameters);
          return { 
            success: pauseResult?.success ?? true, 
            message: pauseResult?.success ? '暂停Schelling模型仿真' : '暂停失败',
            currentState: pauseResult?.currentState
          };
        
        case 'step':
          const stepResult = controls.stepwiseSimulation?.();
          return { 
            success: stepResult?.success ?? true, 
            message: stepResult?.success ? '执行一步Schelling模型' : '单步执行失败',
            currentState: stepResult?.currentState
          };
        
        case 'reset':
          const resetResult = controls.resetSimulation?.();
          return { 
            success: resetResult?.success ?? true, 
            message: resetResult?.success ? '重置Schelling模型' : '重置失败',
            currentState: resetResult?.currentState
          };
        
        // 新标准化接口
        case 'startSimulation':
          const startSimResult = controls.startSimulation(parameters);
          return { 
            success: startSimResult.success, 
            message: startSimResult.success ? '开始Schelling模型仿真' : startSimResult.message || '启动失败',
            currentState: startSimResult.currentState
          };
        
        case 'pauseSimulation':
          const pauseSimResult = controls.pauseSimulation(parameters);
          return { 
            success: pauseSimResult.success, 
            message: pauseSimResult.success ? '暂停Schelling模型仿真' : pauseSimResult.message || '暂停失败',
            currentState: pauseSimResult.currentState
          };
        
        case 'stepwiseSimulation':
          const stepSimResult = controls.stepwiseSimulation();
          return { 
            success: stepSimResult.success, 
            message: stepSimResult.success ? '执行一步Schelling模型' : stepSimResult.message || '单步执行失败',
            currentState: stepSimResult.currentState
          };
        
        case 'resetSimulation':
          const resetSimResult = controls.resetSimulation();
          return { 
            success: resetSimResult.success, 
            message: resetSimResult.success ? '重置Schelling模型' : resetSimResult.message || '重置失败',
            currentState: resetSimResult.currentState
          };
        
        // 参数设置接口
        case 'setSimilarityThreshold':
        case 'setSimilarityThresholdValue':
          const threshold = parameters?.threshold ?? parameters?.value ?? parameters;
          if (threshold !== undefined) {
            const thresholdResult = controls.setSimilarityThresholdValue?.(threshold);
            return { 
              success: thresholdResult?.success ?? true, 
              message: `设置相似性阈值为 ${thresholdResult?.actualValue ?? threshold}`,
              actualValue: thresholdResult?.actualValue
            };
          }
          return { success: false, message: '缺少阈值参数' };
        
        case 'setPopulationDensity':
          const density = parameters?.density ?? parameters?.value ?? parameters;
          if (density !== undefined) {
            const densityResult = controls.setPopulationDensity?.(density);
            return { 
              success: densityResult?.success ?? true, 
              message: `设置人口密度为 ${densityResult?.actualValue ?? density}`,
              actualValue: densityResult?.actualValue
            };
          }
          return { success: false, message: '缺少密度参数' };
        
        case 'setGroupRatio':
        case 'setGroupRatioValue':
          const ratio = parameters?.ratio ?? parameters?.value ?? parameters;
          if (ratio !== undefined) {
            const ratioResult = controls.setGroupRatioValue?.(ratio);
            return { 
              success: ratioResult?.success ?? true, 
              message: `设置群体比例为 ${ratioResult?.actualValue ?? ratio}`,
              actualValue: ratioResult?.actualValue
            };
          }
          return { success: false, message: '缺少比例参数' };
        
        case 'setGridSize':
          const size = parameters?.size ?? parameters?.value ?? parameters;
          if (size !== undefined) {
            const sizeResult = controls.setGridSize?.(size);
            return { 
              success: sizeResult?.success ?? true, 
              message: `设置网格尺寸为 ${sizeResult?.actualValue ?? size}`,
              actualValue: sizeResult?.actualValue
            };
          }
          return { success: false, message: '缺少尺寸参数' };
        
        case 'setSimulationSpeed':
          const speed = parameters?.speed ?? parameters?.value ?? parameters;
          const speedResult = controls.setSimulationSpeed?.(speed ?? 1);
          return { 
            success: speedResult?.success ?? true, 
            message: `Schelling模型设置速度为固定值 ${speedResult?.actualValue ?? 1}`,
            actualValue: speedResult?.actualValue
          };
        
        // 状态查询接口
        case 'getCurrentState':
          const currentState = controls.getCurrentState?.();
          return { 
            success: true, 
            message: '获取当前状态成功',
            currentState: currentState
          };
        
        case 'getState':
          const state = controls.getState?.();
          return { 
            success: true, 
            message: '获取状态成功',
            currentState: state
          };
        
        default:
          console.warn(`[SimulationControlManager] 未知的Schelling命令: ${action}`);
          return { success: false, message: `不支持的操作: ${action}` };
      }
    } catch (error) {
      console.error(`[SimulationControlManager] Schelling命令执行失败:`, error);
      return { 
        success: false, 
        message: error instanceof Error ? error.message : '执行失败'
      };
    }
  }

  /**
   * 获取可用的控制命令列表
   */
  getAvailableCommands(simulationType: string): string[] {
    switch (simulationType) {
      case 'game-of-life':
        return [
          'play', 'pause', 'step', 'reset', 'setSpeed', 'setRules', 
          'loadPattern', 'setColors'
        ];
      case 'boids':
        return [
          'play', 'pause', 'reset', 'setNumBoids', 'setSeparation', 
          'setAlignment', 'setCohesion', 'setMaxSpeed'
        ];
      case 'ising':
        return [
          'play', 'pause', 'step', 'reset', 'setTemperature', 
          'randomize', 'setAllUp', 'setAllDown'
        ];
      case 'physarum':
        return [
          'play', 'pause', 'reset', 'setNumAgents', 'setSensorAngle', 
          'setSensorDistance', 'setRotationAngle'
        ];
      case 'schelling':
        return [
          'play', 'pause', 'step', 'reset', 'setSimilarityThreshold', 
          'setDensity', 'setGroupRatio', 'setGridSize'
        ];
      default:
        return [];
    }
  }
}

// 单例实例
export const simulationControlManager = new SimulationControlManager();
