/**
 * AI导师Function调用执行器
 * 将LLM的函数调用转换为实际的模拟控制动作
 */

import type { 
  ToolCall, 
  SimulationType
} from '@/types/ai-tutor';
import type { AIControlCommand, AIControlResult } from '@/types/simulation-controls';
import { simulationControlManager } from './SimulationControlManager';
import { parseFunctionName, isValidFunctionForSimulation } from './FunctionDefinitions';

/**
 * Function调用执行器类
 */
export class FunctionCallExecutor {
  private currentSimulation: SimulationType | null = null;

  /**
   * 设置当前活跃的模拟类型
   */
  setCurrentSimulation(simulationType: SimulationType | null): void {
    this.currentSimulation = simulationType;
  }

  /**
   * 执行单个函数调用
   */
  async executeFunctionCall(toolCall: ToolCall): Promise<AIControlResult> {
    const { function: functionCall } = toolCall;
    const { name, arguments: argsStr } = functionCall;

    console.log(`[Function调用执行器] 🚀 开始执行函数调用: ${name}`);
    console.log(`[Function调用执行器] 📋 参数: ${argsStr}`);
    console.log(`[Function调用执行器] 🎯 当前模拟: ${this.currentSimulation}`);

    try {
      // 解析函数名
      const parsed = parseFunctionName(name);
      if (!parsed) {
        return {
          success: false,
          message: `无效的函数名: ${name}`,
          error: 'INVALID_FUNCTION_NAME'
        };
      }

      const { simulationType, action } = parsed;
      console.log(`[Function调用执行器] 解析结果: simulationType=${simulationType}, action=${action}`);

      // 检查函数是否对应当前模拟
      if (this.currentSimulation && !isValidFunctionForSimulation(name, this.currentSimulation)) {
        console.log(`[Function调用执行器] 函数验证失败: ${name} 不适用于 ${this.currentSimulation}`);
        return {
          success: false,
          message: `函数 ${name} 不适用于当前模拟 ${this.currentSimulation}`,
          error: 'INVALID_FUNCTION_FOR_SIMULATION'
        };
      }

      // 解析参数
      let parameters: any = {};
      if (argsStr) {
        try {
          parameters = JSON.parse(argsStr);
        } catch (error) {
          return {
            success: false,
            message: `函数参数解析失败: ${argsStr}`,
            error: 'PARAMETER_PARSE_ERROR'
          };
        }
      }

      // 映射函数调用到控制命令
      const controlCommand = this.mapFunctionToControlCommand(simulationType, action, parameters);
      if (!controlCommand) {
        return {
          success: false,
          message: `无法映射函数调用: ${name}`,
          error: 'FUNCTION_MAPPING_ERROR'
        };
      }

      // 执行控制命令
      console.log(`[Function调用执行器] 🎮 执行控制命令:`, controlCommand);
      const result = await simulationControlManager.executeCommand(controlCommand);
      console.log(`[Function调用执行器] ✅ 控制命令执行结果:`, result);

      return result;

    } catch (error) {
      console.error('[Function调用执行器] 执行函数调用失败:', error);
      return {
        success: false,
        message: `函数调用执行失败: ${error instanceof Error ? error.message : '未知错误'}`,
        error: 'EXECUTION_ERROR'
      };
    }
  }

  /**
   * 执行多个函数调用 - 支持分步执行
   */
  async executeFunctionCalls(toolCalls: ToolCall[], executeAll: boolean = true): Promise<AIControlResult[]> {
    const results: AIControlResult[] = [];

    // 如果不是执行全部，只执行第一个函数调用
    const callsToExecute = executeAll ? toolCalls : toolCalls.slice(0, 1);

    for (const toolCall of callsToExecute) {
      if (toolCall.type === 'function') {
        const result = await this.executeFunctionCall(toolCall);
        results.push(result);
      } else {
        results.push({
          success: false,
          message: `不支持的工具类型: ${toolCall.type}`,
          error: 'UNSUPPORTED_TOOL_TYPE'
        });
      }
    }

    return results;
  }

  /**
   * 检查是否有多个操作需要分步执行
   */
  shouldDecomposeInstructions(toolCalls: ToolCall[]): boolean {
    if (toolCalls.length <= 1) return false;

    // 检查是否包含参数设置和仿真控制的组合
    const hasParameterSetting = toolCalls.some(call =>
      call.function.name.includes('setGridSize') ||
      call.function.name.includes('setSimilarityThreshold') ||
      call.function.name.includes('setPopulationDensity') ||
      call.function.name.includes('setGroupRatio')
    );

    const hasSimulationControl = toolCalls.some(call =>
      call.function.name.includes('startSimulation') ||
      call.function.name.includes('runSimulation') ||
      call.function.name.includes('play')
    );

    return hasParameterSetting && hasSimulationControl;
  }

  /**
   * 获取剩余的函数调用
   */
  getRemainingCalls(toolCalls: ToolCall[], executedCount: number): ToolCall[] {
    return toolCalls.slice(executedCount);
  }

  /**
   * 将函数调用映射到控制命令
   */
  private mapFunctionToControlCommand(
    simulationType: string, 
    action: string, 
    parameters: any
  ): AIControlCommand | null {
    // 标准化模拟类型名称（确保使用连字符格式）
    const normalizedSimulationType = this.normalizeSimulationType(simulationType);
    
    // 处理基础控制动作
    switch (action) {
      case 'start':
        return {
          simulationType: normalizedSimulationType,
          action: 'play',
          description: `开始${normalizedSimulationType}模拟`
        };
      
      case 'pause':
        return {
          simulationType: normalizedSimulationType,
          action: 'pause',
          description: `暂停${normalizedSimulationType}模拟`
        };
      
      case 'reset':
        return {
          simulationType: normalizedSimulationType,
          action: 'reset',
          description: `重置${normalizedSimulationType}模拟`
        };
      
      case 'get_state':
        return {
          simulationType: normalizedSimulationType,
          action: 'getState',
          description: `获取${normalizedSimulationType}模拟状态`
        };
    }

    // 处理特定模拟的动作
    switch (normalizedSimulationType) {
      case 'game-of-life':
        return this.mapGameOfLifeAction(action, parameters);
      
      case 'boids':
        return this.mapBoidsAction(action, parameters);
      
      case 'ising-model':
        return this.mapIsingModelAction(action, parameters);
      
      case 'physarum':
        return this.mapPhysarumAction(action, parameters);
      
      case 'schelling':
        return this.mapSchellingAction(action, parameters);
      
      default:
        return null;
    }
  }

  /**
   * 标准化模拟类型名称（将下划线转换为连字符）
   */
  private normalizeSimulationType(simulationType: string): string {
    switch (simulationType) {
      case 'game_of_life':
        return 'game-of-life';
      case 'ising_model':
      case 'ising-model':
        return 'ising-model';
      default:
        return simulationType;
    }
  }

  /**
   * 映射生命游戏的动作
   */
  private mapGameOfLifeAction(action: string, parameters: any): AIControlCommand | null {
    console.log(`[FunctionCallExecutor] 映射生命游戏动作: ${action}`, parameters);
    
    switch (action) {
      // 基础控制操作
      case 'start':
        return { 
          simulationType: 'game-of-life', 
          action: 'startSimulation', 
          description: '开始生命游戏' 
        };
      
      case 'pause':
      case 'stop':
        return { 
          simulationType: 'game-of-life', 
          action: 'pauseSimulation', 
          description: '暂停生命游戏' 
        };
      
      case 'step':
        return { 
          simulationType: 'game-of-life', 
          action: 'stepwiseSimulation', 
          description: '执行一步生命游戏' 
        };
      
      case 'reset':
        return { 
          simulationType: 'game-of-life', 
          action: 'resetSimulation', 
          description: '重置生命游戏' 
        };
      
      case 'run_steps':
        return { 
          simulationType: 'game-of-life', 
          action: 'runMultipleSteps', 
          parameters: { steps: parameters.steps },
          description: `运行${parameters.steps}步生命游戏` 
        };
      
      case 'set_speed':
        return { 
          simulationType: 'game-of-life', 
          action: 'setSimulationSpeed', 
          parameters: { speed: parameters.speed },
          description: `设置生命游戏速度为 ${parameters.speed}` 
        };
      
      case 'set_rules':
        // 解析规则字符串并转换为数值范围
        const survivalRule = parameters.survivalRules || "23";
        const birthRule = parameters.birthRules || "3";
        
        // 转换字符串规则为范围
        const survivalNumbers = survivalRule.split('').map(Number);
        const birthNumbers = birthRule.split('').map(Number);
        
        return { 
          simulationType: 'game-of-life', 
          action: 'setLifeRules', 
          parameters: { 
            rules: {
              survivalMin: Math.min(...survivalNumbers),
              survivalMax: Math.max(...survivalNumbers),
              birthMin: Math.min(...birthNumbers),
              birthMax: Math.max(...birthNumbers)
            }
          },
          description: '设置生命游戏规则' 
        };
      
      case 'load_pattern':
        return { 
          simulationType: 'game-of-life', 
          action: 'loadPresetPattern', 
          parameters: { pattern: parameters.pattern },
          description: `加载图案: ${parameters.pattern}` 
        };
      
      case 'set_colors':
        return { 
          simulationType: 'game-of-life', 
          action: 'setColors', 
          parameters: {
            liveCellColor: parameters.liveCellColor,
            deadCellColor: parameters.deadCellColor
          },
          description: '设置生命游戏颜色' 
        };
      
      case 'get_state':
        return { 
          simulationType: 'game-of-life', 
          action: 'getCurrentState', 
          description: '获取生命游戏当前状态' 
        };
      
      default:
        console.warn(`[FunctionCallExecutor] 未知的生命游戏动作: ${action}`);
        return null;
    }
  }

  /**
   * 映射Boids的动作
   */
  private mapBoidsAction(action: string, parameters: any): AIControlCommand | null {
    switch (action) {
      case 'set_count':
        return { 
          simulationType: 'boids', 
          action: 'setNumBoids', 
          parameters: { count: parameters.count },
          description: `设置鸟群数量为 ${parameters.count}` 
        };
      
      case 'set_separation':
        return { 
          simulationType: 'boids', 
          action: 'setSeparation', 
          parameters: { value: parameters.weight },
          description: `设置分离权重为 ${parameters.weight}` 
        };
      
      case 'set_alignment':
        return { 
          simulationType: 'boids', 
          action: 'setAlignment', 
          parameters: { value: parameters.weight },
          description: `设置对齐权重为 ${parameters.weight}` 
        };
      
      case 'set_cohesion':
        return { 
          simulationType: 'boids', 
          action: 'setCohesion', 
          parameters: { value: parameters.weight },
          description: `设置聚合权重为 ${parameters.weight}` 
        };
      
      case 'set_max_speed':
        return { 
          simulationType: 'boids', 
          action: 'setMaxSpeed', 
          parameters: { speed: parameters.speed },
          description: `设置最大速度为 ${parameters.speed}` 
        };
      
      case 'set_perception_radius':
        return { 
          simulationType: 'boids', 
          action: 'setPerceptionRadius', 
          parameters: { radius: parameters.radius },
          description: `设置感知半径为 ${parameters.radius}` 
        };
      
      case 'get_state':
        return { 
          simulationType: 'boids', 
          action: 'getCurrentState', 
          description: '获取Boids模拟当前状态' 
        };
      
      default:
        return null;
    }
  }

  /**
   * 映射伊辛模型的动作
   */
  private mapIsingModelAction(action: string, parameters: any): AIControlCommand | null {
    switch (action) {
      case 'step':
        return { simulationType: 'ising-model', action: 'step', description: '执行一步伊辛模型' };
      
      case 'set_temperature':
        return { 
          simulationType: 'ising-model', 
          action: 'setTemperature', 
          parameters: { temperature: parameters.temperature },
          description: `设置温度为 ${parameters.temperature}` 
        };
      
      case 'set_magnetic_field':
        return { 
          simulationType: 'ising-model', 
          action: 'setMagneticField', 
          parameters: { field: parameters.field },
          description: `设置磁场为 ${parameters.field}` 
        };
      
      case 'randomize':
        return { simulationType: 'ising-model', action: 'randomize', description: '随机化自旋状态' };
      
      case 'set_all_up':
        return { simulationType: 'ising-model', action: 'setAllUp', description: '设置所有自旋向上' };
      
      case 'set_all_down':
        return { simulationType: 'ising-model', action: 'setAllDown', description: '设置所有自旋向下' };
      
      case 'set_grid_size':
        return { 
          simulationType: 'ising-model', 
          action: 'setGridSize', 
          parameters: { size: parameters.size },
          description: `设置晶格数量为 ${parameters.size}` 
        };
      
      case 'get_state':
        return { 
          simulationType: 'ising-model', 
          action: 'getCurrentState', 
          description: '获取伊辛模型当前状态' 
        };
      
      default:
        return null;
    }
  }

  /**
   * 映射Physarum的动作
   */
  private mapPhysarumAction(action: string, parameters: any): AIControlCommand | null {
    switch (action) {
      case 'set_agent_count':
        return { 
          simulationType: 'physarum', 
          action: 'setNumAgents', 
          parameters: { count: parameters.count },
          description: `设置代理数量为 ${parameters.count}` 
        };
      
      case 'set_sensor_angle':
        return { 
          simulationType: 'physarum', 
          action: 'setSensorAngle', 
          parameters: { angle: parameters.angle },
          description: `设置传感器角度为 ${parameters.angle}` 
        };
      
      case 'set_sensor_distance':
        return { 
          simulationType: 'physarum', 
          action: 'setSensorDistance', 
          parameters: { distance: parameters.distance },
          description: `设置传感器距离为 ${parameters.distance}` 
        };
      
      case 'set_rotation_angle':
        return { 
          simulationType: 'physarum', 
          action: 'setRotationAngle', 
          parameters: { angle: parameters.angle },
          description: `设置旋转角度为 ${parameters.angle}` 
        };
      
      case 'set_step_size':
        return { 
          simulationType: 'physarum', 
          action: 'setStepSize', 
          parameters: { size: parameters.size },
          description: `设置步长为 ${parameters.size}` 
        };
      
      case 'set_deposition_amount':
        return { 
          simulationType: 'physarum', 
          action: 'setDepositionAmount', 
          parameters: { amount: parameters.amount },
          description: `设置沉积量为 ${parameters.amount}` 
        };
      
      case 'set_decay_rate':
        return { 
          simulationType: 'physarum', 
          action: 'setDecayRate', 
          parameters: { rate: parameters.rate },
          description: `设置衰减率为 ${parameters.rate}` 
        };
      
      case 'get_state':
        return { 
          simulationType: 'physarum', 
          action: 'getCurrentState', 
          description: '获取Physarum模拟当前状态' 
        };
      
      default:
        return null;
    }
  }

  /**
   * 映射Schelling模型的动作
   */
  private mapSchellingAction(action: string, parameters: any): AIControlCommand | null {
    console.log(`[FunctionCallExecutor] 映射Schelling动作: ${action}`, parameters);
    
    switch (action) {
      // 基础控制操作
      case 'start':
        return { 
          simulationType: 'schelling', 
          action: 'startSimulation', 
          description: '开始Schelling模型仿真' 
        };
      
      case 'pause':
      case 'stop':
        return { 
          simulationType: 'schelling', 
          action: 'pauseSimulation', 
          description: '暂停Schelling模型仿真' 
        };
      
      case 'step':
        return { 
          simulationType: 'schelling', 
          action: 'stepwiseSimulation', 
          description: '执行一步Schelling模型' 
        };
      
      case 'reset':
        return { 
          simulationType: 'schelling', 
          action: 'resetSimulation', 
          description: '重置Schelling模型' 
        };
      
      // 参数设置操作
      case 'set_similarity_threshold':
        return { 
          simulationType: 'schelling', 
          action: 'setSimilarityThresholdValue', 
          parameters: { threshold: parameters.threshold },
          description: `设置相似性阈值为 ${parameters.threshold}` 
        };
      
      case 'set_density':
      case 'set_population_density':
        return { 
          simulationType: 'schelling', 
          action: 'setPopulationDensity', 
          parameters: { density: parameters.density },
          description: `设置人口密度为 ${parameters.density}` 
        };
      
      case 'set_group_ratio':
      case 'setGroupRatioValue':
        return { 
          simulationType: 'schelling', 
          action: 'setGroupRatioValue', 
          parameters: { ratio: parameters.ratio },
          description: `设置群体比例为 ${parameters.ratio}` 
        };
      
      case 'setGridSize':
      case 'set_grid_size':
        return { 
          simulationType: 'schelling', 
          action: 'setGridSize', 
          parameters: { size: parameters.size },
          description: `设置网格尺寸为 ${parameters.size}` 
        };
      
      case 'setSimulationSpeed':
      case 'set_speed':
        return { 
          simulationType: 'schelling', 
          action: 'setSimulationSpeed', 
          parameters: { speed: parameters.speed || 1 },
          description: `设置仿真速度为 ${parameters.speed || 1}（固定值）` 
        };
      
      // 状态查询操作
      case 'get_state':
      case 'getCurrentState':
        return { 
          simulationType: 'schelling', 
          action: 'getCurrentState', 
          description: '获取Schelling模型当前状态' 
        };
      
      default:
        console.warn(`[FunctionCallExecutor] 未知的Schelling动作: ${action}`);
        return null;
    }
  }

  /**
   * 生成函数调用结果的反馈信息
   */
  generateFeedback(results: AIControlResult[]): string {
    console.log(`[Function调用执行器] 📊 生成反馈，结果数量: ${results.length}`);
    console.log(`[Function调用执行器] 📊 详细结果:`, results);

    if (results.length === 0) {
      return '';
    }

    const feedback = results.map((result, index) => {
      console.log(`[Function调用执行器] 📊 处理结果 ${index + 1}:`, result);
      if (result.success) {
        return `✅ ${result.message}`;
      } else {
        return `❌ ${result.message}`;
      }
    }).join('\n');

    const finalFeedback = `🔧 执行了 ${results.length} 个控制操作\n🛠️ 函数调用结果： ${feedback}`;
    console.log(`[Function调用执行器] 📊 最终反馈:`, finalFeedback);

    return finalFeedback;
  }
}

// 单例实例
export const functionCallExecutor = new FunctionCallExecutor();
