import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Play, CheckCircle, Clock, ArrowRight } from 'lucide-react';
import { instructionDecomposer, InstructionStep } from './InstructionDecomposer';
import { ToolCall } from '@/lib/llm/types';

/**
 * 指令分解器演示组件
 * 用于展示复杂指令如何被分解为多个步骤
 */
const InstructionDecomposerDemo: React.FC = () => {
  const [currentStep, setCurrentStep] = useState<InstructionStep | null>(null);
  const [isExecuting, setIsExecuting] = useState(false);
  const [executionLog, setExecutionLog] = useState<string[]>([]);

  // 模拟复杂指令的函数调用
  const complexInstructionExample: ToolCall[] = [
    {
      id: 'call_1',
      type: 'function',
      function: {
        name: 'schelling_setGridSize',
        arguments: '{"size": 200}'
      }
    },
    {
      id: 'call_2',
      type: 'function',
      function: {
        name: 'schelling_setSimilarityThreshold',
        arguments: '{"threshold": 0.7}'
      }
    },
    {
      id: 'call_3',
      type: 'function',
      function: {
        name: 'schelling_startSimulation',
        arguments: '{}'
      }
    }
  ];

  const addLog = (message: string) => {
    setExecutionLog(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`]);
  };

  const demonstrateDecomposition = () => {
    addLog('开始演示指令分解...');
    
    const userMessage = "将网格大小设置为200，相似性阈值设置为0.7，然后运行仿真";
    
    // 分析是否需要分解
    const analysis = instructionDecomposer.analyzeUserMessage(userMessage, complexInstructionExample);
    addLog(`分析结果: ${analysis.needsDecomposition ? '需要分解' : '不需要分解'}`);
    
    if (analysis.needsDecomposition) {
      // 分解指令
      const decomposed = instructionDecomposer.decomposeInstructions(userMessage, complexInstructionExample);
      addLog(`指令已分解为 ${decomposed.steps.length} 个步骤`);
      
      // 获取第一步
      const firstStep = instructionDecomposer.getCurrentStep();
      if (firstStep) {
        setCurrentStep(firstStep);
        addLog(`准备执行第一步: ${firstStep.description}`);
      }
    }
  };

  const executeCurrentStep = async () => {
    if (!currentStep) return;
    
    setIsExecuting(true);
    addLog(`开始执行: ${currentStep.description}`);
    
    // 模拟执行延迟
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // 模拟执行结果
    const mockResult = [{ 
      success: true, 
      message: `${currentStep.description} 执行成功` 
    }];
    
    // 完成当前步骤
    const progress = instructionDecomposer.completeCurrentStep(mockResult);
    addLog(`步骤完成: ${currentStep.description}`);
    
    if (progress.hasNextStep && progress.nextStep) {
      setCurrentStep(progress.nextStep);
      addLog(`下一步准备就绪: ${progress.nextStep.description}`);
    } else {
      setCurrentStep(null);
      addLog('所有步骤已完成！');
      instructionDecomposer.clearCurrentInstructions();
    }
    
    setIsExecuting(false);
  };

  const resetDemo = () => {
    setCurrentStep(null);
    setIsExecuting(false);
    setExecutionLog([]);
    instructionDecomposer.clearCurrentInstructions();
    addLog('演示已重置');
  };

  const progressInfo = instructionDecomposer.getProgress();

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Play className="w-5 h-5" />
            指令分解器演示
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="text-sm text-muted-foreground">
            演示复杂指令如何被自动分解为多个基本操作步骤，确保每个操作都能正确执行。
          </div>
          
          <div className="flex gap-2">
            <Button onClick={demonstrateDecomposition} disabled={isExecuting}>
              开始演示分解
            </Button>
            <Button 
              onClick={executeCurrentStep} 
              disabled={!currentStep || isExecuting}
              variant="outline"
            >
              {isExecuting ? '执行中...' : '执行当前步骤'}
            </Button>
            <Button onClick={resetDemo} variant="ghost">
              重置演示
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* 进度显示 */}
      {progressInfo && (
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">执行进度</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-4">
              <Badge variant="outline">
                步骤 {progressInfo.currentStep} / {progressInfo.totalSteps}
              </Badge>
              <Badge variant="secondary">
                已完成 {progressInfo.completedSteps} 步
              </Badge>
              <Badge variant={progressInfo.isActive ? "default" : "secondary"}>
                {progressInfo.isActive ? "进行中" : "已完成"}
              </Badge>
            </div>
          </CardContent>
        </Card>
      )}

      {/* 当前步骤显示 */}
      {currentStep && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Clock className="w-5 h-5" />
              当前步骤
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <Badge variant="outline">{currentStep.id}</Badge>
                <ArrowRight className="w-4 h-4" />
                <span className="font-medium">{currentStep.description}</span>
              </div>
              <div className="text-sm text-muted-foreground">
                函数调用: {currentStep.toolCalls.map(call => call.function.name).join(', ')}
              </div>
              <div className="flex items-center gap-2">
                <Badge variant={
                  currentStep.status === 'completed' ? 'default' :
                  currentStep.status === 'executing' ? 'secondary' : 'outline'
                }>
                  {currentStep.status === 'completed' && <CheckCircle className="w-3 h-3 mr-1" />}
                  {currentStep.status}
                </Badge>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* 执行日志 */}
      <Card>
        <CardHeader>
          <CardTitle>执行日志</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="bg-muted/50 rounded-md p-3 max-h-60 overflow-y-auto">
            {executionLog.length === 0 ? (
              <div className="text-muted-foreground text-sm">暂无日志</div>
            ) : (
              <div className="space-y-1">
                {executionLog.map((log, index) => (
                  <div key={index} className="text-sm font-mono">
                    {log}
                  </div>
                ))}
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* 示例说明 */}
      <Card>
        <CardHeader>
          <CardTitle>工作原理</CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          <div className="text-sm">
            <strong>复杂指令示例：</strong>
            "将网格大小设置为200，相似性阈值设置为0.7，然后运行仿真"
          </div>
          
          <div className="text-sm">
            <strong>自动分解为：</strong>
            <ol className="list-decimal list-inside mt-2 space-y-1 ml-4">
              <li>设置网格大小为200</li>
              <li>设置相似性阈值为0.7</li>
              <li>启动仿真</li>
            </ol>
          </div>
          
          <div className="text-sm text-muted-foreground">
            每个步骤会等待前一步完成后再执行，确保操作的正确性和可控性。
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default InstructionDecomposerDemo;
