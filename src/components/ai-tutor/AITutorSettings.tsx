import React, { useState, useEffect } from 'react';
import { 
  <PERSON><PERSON>, 
  DialogContent, 
  DialogHeader, 
  DialogTitle,
  DialogDescription,
  DialogFooter 
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '@/components/ui/select';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { AlertCircle, CheckCircle, Loader2, Eye, EyeOff, Sparkles } from 'lucide-react';
import { cn } from '@/lib/utils';
import type { AITutorSettingsProps, LLMConfig, ConnectionTestResult } from '@/types/ai-tutor';
import { useAITutorContext } from './AITutorProvider';
import { useLLMConnection } from '@/hooks/useLLMConnection';
import { PROMPT_TEMPLATES, type PromptTemplate } from './PromptTemplates';

/**
 * AI导师设置对话框组件
 * 用于配置LLM连接参数
 */
const AITutorSettings: React.FC<AITutorSettingsProps> = ({ 
  open, 
  onOpenChange 
}) => {
  console.log('AITutorSettings 渲染, open:', open);
  
  const { state, actions } = useAITutorContext();
  const { testConnection } = useLLMConnection();
  const [formData, setFormData] = useState<LLMConfig>({
    provider: 'openai',
    endpoint: 'https://api.openai.com/v1',
    apiKey: '',
    model: 'gpt-3.5-turbo',
    temperature: 0.7,
    maxTokens: 2000,
    systemPrompt: '',
    enableFunctionCalling: true
  });

  const [testResult, setTestResult] = useState<ConnectionTestResult | null>(null);
  const [isTesting, setIsTesting] = useState(false);
  const [showApiKey, setShowApiKey] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [selectedTemplate, setSelectedTemplate] = useState<string>('');
  const [showTemplatePreview, setShowTemplatePreview] = useState(false);

  // 处理模板选择
  const handleTemplateSelect = (templateId: string) => {
    const template = PROMPT_TEMPLATES.find(t => t.id === templateId);
    if (template) {
      setSelectedTemplate(templateId);
      handleFieldChange('systemPrompt', template.prompt);
    }
  };

  // 预设的LLM配置
  const presets = {
    openai: {
      endpoint: 'https://api.openai.com/v1',
      models: ['gpt-3.5-turbo', 'gpt-4', 'gpt-4-turbo-preview']
    },
    claude: {
      endpoint: 'https://api.anthropic.com/v1',
      models: ['claude-3-haiku-20240307', 'claude-3-sonnet-20240229', 'claude-3-opus-20240229']
    },
    xai: {
      endpoint: 'https://api.x.ai/v1',
      models: ['grok-3', 'grok-3-mini']
    },
    custom: {
      endpoint: '',
      models: []
    }
  };

  // 处理表单字段变化
  const handleFieldChange = (field: keyof LLMConfig, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // 清除相关错误
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  // 处理提供商变化
  const handleProviderChange = (provider: string) => {
    const preset = presets[provider as keyof typeof presets];
    setFormData(prev => ({
      ...prev,
      provider: provider as LLMConfig['provider'],
      endpoint: preset.endpoint,
      model: preset.models[0] || ''
    }));
  };

  // 验证表单
  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.endpoint) {
      newErrors.endpoint = 'API端点不能为空';
    } else if (!formData.endpoint.startsWith('http')) {
      newErrors.endpoint = 'API端点必须以http或https开头';
    }

    if (!formData.apiKey) {
      newErrors.apiKey = 'API密钥不能为空';
    }

    if (!formData.model) {
      newErrors.model = '模型名称不能为空';
    }

    if (formData.temperature < 0 || formData.temperature > 2) {
      newErrors.temperature = 'Temperature必须在0-2之间';
    }

    if (formData.maxTokens < 1 || formData.maxTokens > 4000) {
      newErrors.maxTokens = 'Max Tokens必须在1-4000之间';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // 测试连接
  const handleTestConnection = async () => {
    if (!validateForm()) return;

    setIsTesting(true);
    setTestResult(null);

    try {
      const result = await testConnection(formData);
      setTestResult(result);
      
      // 根据测试结果更新连接状态
      if (result.success) {
        // 测试成功时，暂时不更新全局状态，等保存时再更新
        console.log('连接测试成功');
      } else {
        // 测试失败时，清除连接状态
        actions.setConnected(false);
      }
    } catch (error) {
      const errorResult = {
        success: false,
        error: error instanceof Error ? error.message : '测试连接失败'
      };
      setTestResult(errorResult);
      // 测试失败时，清除连接状态
      actions.setConnected(false);
    } finally {
      setIsTesting(false);
    }
  };

  // 保存配置
  const handleSave = () => {
    if (!validateForm()) return;

    // 保存配置
    actions.setConfig(formData);
    
    // 如果测试连接成功，设置为已连接状态
    if (testResult?.success) {
      actions.setConnected(true);
    }
    
    onOpenChange(false);
  };

  // 加载当前配置
  React.useEffect(() => {
    if (state.config && open) {
      setFormData(state.config);
    }
  }, [state.config, open]);

  // 监控 open 状态变化
  useEffect(() => {
    console.log('AITutorSettings open 状态变化:', open);
  }, [open]);

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <div className="w-8 h-8 bg-gradient-to-r from-cyan-500 to-blue-500 rounded-full flex items-center justify-center">
              <span className="text-white text-sm font-bold">AI</span>
            </div>
            AI导师设置
          </DialogTitle>
          <DialogDescription>
            配置您的LLM连接参数，让AI导师为您提供智能学习指导。
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* LLM提供商选择 */}
          <div className="space-y-2">
            <Label htmlFor="provider">LLM提供商</Label>
            <Select 
              value={formData.provider} 
              onValueChange={handleProviderChange}
            >
              <SelectTrigger>
                <SelectValue placeholder="选择LLM提供商" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="openai">OpenAI</SelectItem>
                <SelectItem value="claude">Claude (Anthropic)</SelectItem>
                <SelectItem value="xai">xAI (Grok)</SelectItem>
                <SelectItem value="custom">自定义</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <Separator />

          {/* API配置 */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="endpoint">API端点</Label>
              <Input
                id="endpoint"
                value={formData.endpoint}
                onChange={(e) => handleFieldChange('endpoint', e.target.value)}
                placeholder="https://api.openai.com/v1"
                className={errors.endpoint ? 'border-red-500' : ''}
              />
              {errors.endpoint && (
                <p className="text-sm text-red-500">{errors.endpoint}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="model">模型名称</Label>
              {formData.provider === 'custom' ? (
                <Input
                  id="model"
                  value={formData.model}
                  onChange={(e) => handleFieldChange('model', e.target.value)}
                  placeholder="请输入自定义模型名称，如：gpt-4o-mini, claude-3-5-sonnet-20241022"
                  className={errors.model ? 'border-red-500' : ''}
                />
              ) : (
                <Select 
                  value={formData.model} 
                  onValueChange={(value) => handleFieldChange('model', value)}
                >
                  <SelectTrigger className={errors.model ? 'border-red-500' : ''}>
                    <SelectValue placeholder="选择模型" />
                  </SelectTrigger>
                  <SelectContent>
                    {presets[formData.provider].models.map(model => (
                      <SelectItem key={model} value={model}>{model}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              )}
              {errors.model && (
                <p className="text-sm text-red-500">{errors.model}</p>
              )}
              {formData.provider === 'custom' && (
                <p className="text-xs text-muted-foreground">
                  💡 提示：请输入完整的模型名称，确保与API提供商的模型名称完全一致
                </p>
              )}
            </div>
          </div>

          {/* API密钥 */}
          <div className="space-y-2">
            <Label htmlFor="apiKey">API密钥</Label>
            <div className="relative">
              <Input
                id="apiKey"
                type={showApiKey ? 'text' : 'password'}
                value={formData.apiKey}
                onChange={(e) => handleFieldChange('apiKey', e.target.value)}
                placeholder="请输入您的API密钥"
                className={cn('pr-10', errors.apiKey ? 'border-red-500' : '')}
              />
              <Button
                type="button"
                variant="ghost"
                size="sm"
                className="absolute right-1 top-1/2 -translate-y-1/2 h-8 w-8 p-0"
                onClick={() => setShowApiKey(!showApiKey)}
              >
                {showApiKey ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
              </Button>
            </div>
            {errors.apiKey && (
              <p className="text-sm text-red-500">{errors.apiKey}</p>
            )}
          </div>

          <Separator />

          {/* 高级参数 */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="temperature">Temperature ({formData.temperature})</Label>
              <input
                id="temperature"
                type="range"
                min="0"
                max="2"
                step="0.1"
                value={formData.temperature}
                onChange={(e) => handleFieldChange('temperature', parseFloat(e.target.value))}
                className="w-full"
              />
              <div className="flex justify-between text-xs text-gray-500">
                <span>更保守</span>
                <span>更创造性</span>
              </div>
              {errors.temperature && (
                <p className="text-sm text-red-500">{errors.temperature}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="maxTokens">最大Token数</Label>
              <Input
                id="maxTokens"
                type="number"
                min="1"
                max="4000"
                value={formData.maxTokens}
                onChange={(e) => handleFieldChange('maxTokens', parseInt(e.target.value))}
                className={errors.maxTokens ? 'border-red-500' : ''}
              />
              {errors.maxTokens && (
                <p className="text-sm text-red-500">{errors.maxTokens}</p>
              )}
            </div>
          </div>

          {/* 系统提示词模板选择 */}
          <div className="space-y-3">
            <div className="flex items-center gap-2">
              <Sparkles className="w-4 h-4 text-primary" />
              <Label className="text-sm font-medium">系统提示词设置</Label>
            </div>
            
            {/* 预设模板选择 */}
            <div className="space-y-2">
              <Label htmlFor="promptTemplate" className="text-sm">选择预设模板</Label>
              <Select value={selectedTemplate} onValueChange={handleTemplateSelect}>
                <SelectTrigger>
                  <SelectValue placeholder="选择一个预设模板或自定义..." />
                </SelectTrigger>
                <SelectContent>
                  {PROMPT_TEMPLATES.map((template) => (
                    <SelectItem key={template.id} value={template.id}>
                      <div className="flex flex-col">
                        <span className="font-medium">{template.name}</span>
                        <span className="text-xs text-muted-foreground">{template.description}</span>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              
              {/* 显示模板标签 */}
              {selectedTemplate && (
                <div className="flex flex-wrap gap-1">
                  {PROMPT_TEMPLATES.find(t => t.id === selectedTemplate)?.tags.map((tag) => (
                    <Badge key={tag} variant="secondary" className="text-xs">
                      {tag}
                    </Badge>
                  ))}
                </div>
              )}
            </div>

            {/* 自定义系统提示词 */}
            <div className="space-y-2">
              <Label htmlFor="systemPrompt" className="text-sm">
                系统提示词 {selectedTemplate && '(基于模板修改)'}
              </Label>
              <textarea
                id="systemPrompt"
                value={formData.systemPrompt || ''}
                onChange={(e) => {
                  handleFieldChange('systemPrompt', e.target.value);
                  setSelectedTemplate(''); // 清除模板选择状态
                }}
                placeholder="您可以在此自定义AI导师的行为和回复风格..."
                className="w-full h-32 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 resize-none text-sm"
              />
              <p className="text-xs text-muted-foreground">
                💡 提示：选择预设模板后可以进一步自定义修改
              </p>
            </div>
          </div>

          {/* Function Calling 开关 */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <Label htmlFor="enableFunctionCalling" className="text-sm font-medium">
                启用函数调用 (Function Calling)
              </Label>
              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id="enableFunctionCalling"
                  checked={formData.enableFunctionCalling || false}
                  onChange={(e) => setFormData(prev => ({
                    ...prev,
                    enableFunctionCalling: e.target.checked
                  }))}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
              </div>
            </div>
            <p className="text-xs text-gray-500">
              启用后，AI可以直接控制模拟参数。推荐开启以获得最佳交互体验。
            </p>
          </div>

          {/* 测试连接 */}
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <Label>连接测试</Label>
              <Button
                onClick={handleTestConnection}
                disabled={isTesting}
                variant="outline"
                size="sm"
              >
                {isTesting ? (
                  <>
                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                    测试中...
                  </>
                ) : (
                  '测试连接'
                )}
              </Button>
            </div>

            {testResult && (
              <div className={cn(
                "p-3 rounded-md border",
                testResult.success ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'
              )}>
                <div className="flex items-center gap-2">
                  {testResult.success ? (
                    <CheckCircle className="w-4 h-4 text-green-600" />
                  ) : (
                    <AlertCircle className="w-4 h-4 text-red-600" />
                  )}
                  <span className={cn(
                    "font-medium",
                    testResult.success ? 'text-green-800' : 'text-red-800'
                  )}>
                    {testResult.success ? '连接成功' : '连接失败'}
                  </span>
                  {testResult.latency && (
                    <Badge variant="secondary" className="ml-auto">
                      {testResult.latency}ms
                    </Badge>
                  )}
                </div>
                {testResult.error && (
                  <p className="text-sm text-red-600 mt-1">{testResult.error}</p>
                )}
                {testResult.modelInfo && (
                  <div className="mt-2 text-sm text-green-700">
                    <p>模型: {testResult.modelInfo.name}</p>
                    {testResult.modelInfo.capabilities.length > 0 && (
                      <p>支持功能: {testResult.modelInfo.capabilities.join(', ')}</p>
                    )}
                  </div>
                )}
              </div>
            )}
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            取消
          </Button>
          <Button 
            onClick={handleSave}
            disabled={!testResult?.success}
            className="bg-gradient-to-r from-cyan-500 to-blue-500 hover:from-cyan-600 hover:to-blue-600"
          >
            保存配置
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default AITutorSettings;
