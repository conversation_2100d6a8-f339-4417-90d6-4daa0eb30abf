/**
 * AI导师知识库
 * 为每种模拟类型提供专业的知识内容
 */

import type { SimulationType, KnowledgeEntry } from '@/types/ai-tutor';

/**
 * 生命游戏知识库
 */
export const GAME_OF_LIFE_KNOWLEDGE: KnowledgeEntry[] = [
  {
    id: 'gol-history',
    topic: '康威生命游戏的诞生传奇',
    simulationType: 'game-of-life',
    level: 'beginner',
    content: `
## 1970年：一个数学天才的奇思妙想

在剑桥大学的一个普通下午，年轻的数学家约翰·康威正在思考一个深刻的哲学问题：**生命的本质究竟是什么？能否用最简单的数学规则来模拟生命的复杂性？**

### 🎭 科学史上的传奇时刻

康威受到了传奇数学家**约翰·冯·诺伊曼**关于"自复制机器"研究的启发。冯·诺伊曼曾经设想：如果机器能够自我复制，那么它们是否也能展现出类似生命的特征？

经过无数个日夜的思考，康威终于在1970年创造出了这个被他称为"零玩家游戏"的数学宇宙。他后来回忆说：**"我想创造的不仅仅是一个游戏，而是一个关于生命、死亡与重生的数学诗篇。"**

### 🌟 四条简单规则，无限复杂宇宙

令人惊叹的是，康威用仅仅四条简单规则就构建了一个完整的"数字生态系统"：

1. **人口过少**：活细胞少于2个邻居时死亡（孤独死）
2. **适度繁荣**：活细胞有2-3个邻居时继续生存
3. **人口过多**：活细胞超过3个邻居时死亡（拥挤死）
4. **生命诞生**：死细胞恰好有3个邻居时复活

### 🔬 意外的科学发现

最初，康威只是想创造一个有趣的数学游戏。但科学家们很快发现，这个"游戏"竟然是**图灵完备**的——这意味着它可以模拟任何计算机程序！这一发现震惊了整个计算机科学界。
    `,
    keywords: ['约翰·康威', '1970年', '剑桥大学', '冯·诺伊曼', '自复制', '图灵完备', '涌现性'],
    relatedTopics: ['gol-rules', 'gol-patterns', 'complexity-science']
  },
  {
    id: 'gol-rules',
    topic: '生命的四大定律',
    simulationType: 'game-of-life',
    level: 'beginner',
    content: `
## 生命游戏：大自然的缩影

康威的天才之处在于，他用四条极其简单的规则，完美地模拟了真实世界中生命的**生存竞争**、**繁衍生息**和**环境适应**。

### 🏝️ 孤独法则
**少于2个邻居 → 死亡**
- 就像孤岛上的生物，缺乏群体支持而无法生存
- 模拟了现实中的社会隔离效应
- 体现了"团结就是力量"的生物学原理

### 🌱 生存法则  
**2-3个邻居 → 继续生存**
- 完美的生存环境：既不孤单，也不拥挤
- 就像适宜的生态密度，保证资源充足
- 反映了自然界中的"生态平衡"概念

### 💀 拥挤法则
**超过3个邻居 → 死亡**
- 模拟过度竞争导致的资源枯竭
- 类似现实中的"人口爆炸"问题
- 体现了环境承载力的限制

### 🆕 诞生法则
**恰好3个邻居 → 新生命诞生**
- 最神奇的规则：死亡中孕育新生
- 需要"恰到好处"的条件才能诞生
- 象征着生命对完美环境的需求

### 🎯 哲学思考
这四条规则揭示了一个深刻的哲学真理：**复杂性可以从简单性中涌现**。整个宇宙的复杂性，或许也源于几个基本的物理定律。
    `,
    keywords: ['生存规则', '诞生规则', '邻居数量', '生态平衡', '复杂性涌现'],
    relatedTopics: ['gol-history', 'gol-patterns', 'emergence-theory']
  },
  {
    id: 'gol-patterns',
    topic: '数字生命的奇妙形态',
    simulationType: 'game-of-life',
    level: 'intermediate',
    content: `
## 生命游戏中的"物种分类学"

就像生物学家对真实生物进行分类一样，数学家们也为生命游戏中的图案建立了完整的"分类体系"。

### 🏛️ 静物家族（Still Lifes）
**永恒不变的"数字化石"**

- **方块（Block）**：最简单的2×2正方形，象征着完美的稳定性
- **蜂巢（Beehive）**：六边形结构，让人联想到真实的蜂巢
- **面包（Loaf）**：不对称的静物，证明美不一定需要对称
- **船（Boat）**：小巧的5细胞静物，形状酷似小船

**科学意义**：静物代表了系统的"平衡态"，在热力学中称为"最低能量状态"。

### ⚡ 振荡器家族（Oscillators）
**有节律的"数字心跳"**

- **闪烁器（Blinker）**：最简单的周期2振荡器，像是宇宙的心跳
- **蟾蜍（Toad）**：周期2的对称振荡器，展示了完美的对称美
- **脉冲星（Pulsar）**：周期15的大型振荡器，如同天体物理中的脉冲星
- **时钟（Clock）**：周期2的精巧装置，体现了时间的周期性

**科学意义**：振荡器类似于物理学中的"简谐振动"，展示了系统的周期性行为。

### 🚀 飞行器家族（Spaceships）
**征服太空的"数字探险家"**

- **滑翔机（Glider）**：最著名的5细胞飞行器，每4步移动一格对角线
- **轻型宇宙飞船（LWSS）**：9细胞的水平飞行器，移动速度更快
- **中型宇宙飞船（MWSS）**：11细胞的飞行器，更加稳定
- **重型宇宙飞船（HWSS）**：13细胞的大型飞行器，展示了规模的力量

**科学意义**：飞行器证明了信息可以在空间中传播，类似于物理学中的"孤立波"。

### 🏭 枪械和炮台（Guns）
**无穷创造力的"生产线"**

- **滑翔机枪（Glider Gun）**：周期性产生滑翔机的装置
- **游轮炮台**：能发射各种飞行器的复杂结构

这些模式的发现让科学家们意识到：**简单规则可以产生无限复杂的行为**！
    `,
    keywords: ['静物', '振荡器', '飞行器', '滑翔机', '脉冲星', '滑翔机枪'],
    relatedTopics: ['gol-rules', 'gol-complexity', 'pattern-analysis']
  },
  {
    id: 'gol-complexity',
    topic: '从简单到复杂：涌现的奇迹',
    simulationType: 'game-of-life',
    level: 'advanced',
    content: `
## 复杂性科学的完美教科书

康威生命游戏被誉为**复杂性科学**的典型范例，它完美诠释了现代科学中最重要的概念之一：**涌现性（Emergence）**。

### 🌊 涌现性：1+1>2的奇迹

**什么是涌现？**
涌现是指系统在宏观层面展现出的性质和行为，这些特征在微观组件中并不存在。就像：
- 单个H₂O分子没有"湿润"的性质，但水却是湿的
- 单个神经元不会思考，但大脑却能产生意识
- 单个细胞不知道规则，但整体却展现出"智能"行为

### 🎭 生命游戏中的涌现现象

**1. 集体行为的涌现**
- 没有任何"中央控制器"，但整体展现出有序行为
- 滑翔机的"飞行"能力不存在于任何单个细胞中
- 复杂模式从简单规则中自发产生

**2. 信息传递的涌现**
- 滑翔机可以作为"信息载体"，在网格中传递信息
- 枪械可以"制造"其他模式，展现出生产能力
- 系统具备了"计算"的能力

**3. 自组织的涌现**
- 随机初始状态经常演化出有序结构
- 系统自动"筛选"出稳定的模式
- 展现出类似"自然选择"的机制

### 🧠 图灵完备性：意外的发现

1982年，研究者们证明了生命游戏是**图灵完备**的，这意味着：

- 它可以模拟任何计算机程序
- 理论上可以构建一台"生命游戏计算机"
- 甚至可以在其中运行生命游戏本身！

这个发现震惊了科学界：一个用来"娱乐"的数学游戏，竟然具备了通用计算的能力！

### 🌍 现实世界的应用

生命游戏的原理被广泛应用于：

- **生态学**：模拟动植物种群动态
- **城市规划**：研究城市发展模式
- **材料科学**：设计自修复材料
- **人工智能**：启发神经网络设计
- **社会学**：理解社会现象的涌现

### 🔮 哲学思考

康威曾说："也许我们的宇宙本身就是某种巨大的细胞自动机，而我们所见的一切复杂性，都源于几个简单的基本规律。"

这个想法并非天方夜谭——现代物理学正在探索宇宙是否真的可以用类似的数字规则来描述！
    `,
    keywords: ['涌现性', '复杂性科学', '图灵完备', '自组织', '计算', '哲学'],
    relatedTopics: ['complexity-theory', 'emergence', 'computation', 'cellular-automata']
  }
];

/**
 * Boids群体行为知识库
 */
export const BOIDS_KNOWLEDGE: KnowledgeEntry[] = [
  {
    id: 'boids-history',
    topic: '群体智能的数字重现',
    simulationType: 'boids',
    level: 'beginner',
    content: `
## 1986年：当计算机学会"飞行"

在硅谷的皮克斯动画工作室，年轻的计算机图形学专家**克雷格·雷诺兹**面临着一个看似不可能的挑战：如何让计算机中的角色像真实的鸟群一样自然地飞行？

### 🐦 大自然的启示

雷诺兹痴迷于观察窗外的鸟群。他发现了一个令人惊叹的现象：
- **没有领头鸟**：鸟群中并没有"指挥官"告诉每只鸟该往哪飞
- **完美协调**：成百上千只鸟却能完美同步，如同一个生命体
- **优雅转向**：整个鸟群能瞬间改变方向，没有碰撞，没有混乱

他想：**如果每只鸟都只遵循简单的本能反应，整个群体如何展现出如此复杂而优雅的行为？**

### 🧠 三条黄金法则的诞生

经过深入观察和思考，雷诺兹提出了著名的"**三条黄金法则**"：

1. **分离定律**：避免与邻居碰撞
2. **对齐定律**：与邻居朝同一方向飞行  
3. **聚合定律**：向邻居群体的中心靠拢

令人震惊的是，就这三条简单规则，竟然完美重现了鸟群的所有复杂行为！

### 🎬 从科学到艺术

雷诺兹的发现不仅是科学突破，更开启了数字艺术的新纪元：
- **《蝙蝠侠归来》(1992)**：首次在好莱坞电影中使用Boids技术
- **《狮子王》(1994)**：牛羚大迁移场景震撼全球
- **《指环王》(2001)**：史诗战争场面中的千军万马

雷诺兹后来感慨道：**"我永远没想到，对鸟群的简单观察，竟然改变了整个电影工业。"**

### 🌍 超越娱乐的深远影响

今天，Boids算法已经远远超出了动画领域：
- **机器人学**：无人机编队飞行
- **交通规划**：车流优化系统
- **生物学**：研究动物集群行为
- **社会学**：理解人群动态

这证明了一个深刻的道理：**自然界的智慧，往往隐藏在最简单的行为模式中。**
    `,
    keywords: ['克雷格·雷诺兹', '1986年', '皮克斯', '群体智能', '好莱坞', '无人机编队'],
    relatedTopics: ['boids-rules', 'swarm-intelligence', 'emergent-behavior']
  },
  {
    id: 'boids-rules',
    topic: '三条黄金法则：群体智能的密码',
    simulationType: 'boids',
    level: 'beginner',
    content: `
## 解码群体智能：从混沌到秩序

雷诺兹的天才发现在于：**复杂的群体行为可以用三条极其简单的个体规则来解释**。这三条法则就像是大自然编写的"程序代码"。

### 🚫 第一法则：分离（Separation）
**"保持个人空间，避免碰撞"**

- **生物学原理**：就像人在拥挤的地铁里本能地避开他人
- **物理意义**：短程排斥力，防止个体重叠
- **参数调节**：
  - 分离半径小 → 群体紧密但容易碰撞
  - 分离半径大 → 群体松散但很安全

**现实类比**：想象你在拥挤的商场里走路，你会本能地避开靠得太近的人。

### ➡️ 第二法则：对齐（Alignment）
**"跟随邻居的方向，保持队形"**

- **生物学原理**：群体中的"从众心理"
- **物理意义**：速度向量的平均化过程
- **参数调节**：
  - 对齐强度低 → 个体各自为政
  - 对齐强度高 → 群体行动一致

**现实类比**：就像高速公路上的车流，大家都朝着相同的方向行驶。

### 🧲 第三法则：聚合（Cohesion）
**"向群体中心靠拢，保持团结"**

- **生物学原理**：群体的"凝聚力"，安全感的来源
- **物理意义**：长程吸引力，维持群体完整性
- **参数调节**：
  - 聚合力弱 → 群体容易分散
  - 聚合力强 → 群体过度集中

**现实类比**：像磁铁吸引铁屑，群体成员被"看不见的力"拉向中心。

### ⚖️ 三力平衡：和谐的艺术

这三条法则的精妙之处在于它们的**相互制衡**：
- 分离力说："别靠太近！"
- 对齐力说："跟上大部队！"
- 聚合力说："别掉队！"

当这三种力达到动态平衡时，就产生了我们看到的优雅群体行为：既不会碰撞，也不会分散，还能保持统一的行动方向。

### 🎯 参数调节的艺术

调节这三个力的强度比例，可以创造出截然不同的群体行为：
- **紧密编队**：分离力小，聚合力大
- **松散游荡**：三力平衡，中等强度
- **快速机动**：对齐力强，反应敏捷

这就是为什么同样的算法既能模拟悠闲的鱼群，也能表现紧急避险的鸟群！
    `,
    keywords: ['分离', '对齐', '聚合', '参数调节', '力的平衡', '群体动力学'],
    relatedTopics: ['boids-history', 'boids-emergence', 'parameter-tuning']
  },
  {
    id: 'boids-emergence',
    topic: '无领导者的完美协调',
    simulationType: 'boids',
    level: 'intermediate',
    content: `
## 涌现：当1+1+1>3时

Boids模型最令人震撼的特性是**涌现行为**：个体遵循简单规则，群体却展现出令人惊叹的复杂行为。

### 🌊 涌现现象大观

**1. 集体转向**
- **个体行为**：只看邻居，调整方向
- **群体现象**：整体像单一生命体般优雅转弯
- **科学原理**：信息在网络中的快速传播

**2. 分流与合并**
- **遇到障碍**：群体自动分成两股，绕过障碍后重新汇合
- **无需规划**：没有任何个体"知道"整体策略
- **智能表现**：展现出类似"集体决策"的行为

**3. 层次结构**
- **子群形成**：大群体自发分割成多个小群体
- **动态重组**：子群之间不断分离、合并
- **自适应性**：根据环境自动调整群体大小

### 🧠 分布式智能

Boids系统展现了**分布式智能**的特征：

- **无中央控制**：没有"大脑"指挥整体行为
- **局部交互**：每个个体只与邻居交流
- **全局智能**：整体表现出智能行为

这种机制在现实世界中无处不在：
- **蚂蚁觅食**：单个蚂蚁很简单，蚁群却能找到最短路径
- **神经网络**：单个神经元不会思考，大脑却产生意识
- **市场经济**：个人追求利益，市场展现"看不见的手"

### 🔄 反馈循环的力量

Boids中存在多层反馈机制：

**正反馈**：
- 对齐行为加强 → 群体更统一 → 对齐效果更明显
- 聚合行为增强 → 密度提高 → 聚合力更强

**负反馈**：
- 密度过高 → 分离力增强 → 密度降低
- 速度过快 → 难以协调 → 自动减速

这些反馈循环使系统具备了**自稳定性**：系统会自动寻找最佳的运行状态。

### 🎮 参数空间的探索

不同的参数组合会产生截然不同的涌现行为：

**低聚合力 + 高分离力**：
- 结果：个体主义，群体容易分散
- 类比：现代都市中的人群

**高聚合力 + 低分离力**：
- 结果：过度拥挤，频繁碰撞
- 类比：恐慌中的人群

**平衡三力**：
- 结果：和谐流动，优雅协调
- 类比：训练有素的团队

### 🌟 复杂性的边界

最有趣的行为往往出现在**"混沌边缘"**：
- 秩序与混沌的临界点
- 参数的微小变化导致行为的剧烈改变
- 系统展现出最大的创造性和适应性

这启发我们思考：也许生命本身就存在于这样的临界状态中，在秩序与混沌之间寻找着完美的平衡。
    `,
    keywords: ['涌现行为', '分布式智能', '反馈循环', '自稳定性', '混沌边缘'],
    relatedTopics: ['boids-rules', 'complexity-science', 'swarm-robotics']
  },
  {
    id: 'boids-applications',
    topic: '从银幕到现实：Boids的无限可能',
    simulationType: 'boids',
    level: 'advanced',
    content: `
## 当虚拟照进现实

从雷诺兹最初的动画需求开始，Boids算法已经发展成为现代科技的重要基石，影响着我们生活的方方面面。

### 🎬 娱乐产业的革命

**好莱坞的新宠**：
- **《侏罗纪公园》**：恐龙群体的逼真行为
- **《指环王》**：万马奔腾的震撼战争场面
- **《海底总动员》**：鱼群的自然游弋
- **《阿凡达》**：外星生物的群体行为

**游戏产业的突破**：
- **RTS游戏**：军队的智能移动
- **MMORPG**：NPC群体的生动表现
- **模拟游戏**：生态系统的真实模拟

### 🤖 机器人学的未来

**无人机编队**：
- **军事应用**：协调侦察、集群攻击
- **民用服务**：搜索救援、货物配送
- **娱乐表演**：灯光秀、空中芭蕾

**地面机器人**：
- **仓储物流**：智能分拣、协调搬运
- **清洁服务**：多机器人协作清洁
- **探索任务**：火星探测器的协同工作

### 🚗 智能交通系统

**自动驾驶**：
- **车队协调**：高速公路上的智能编队
- **交叉路口**：无信号灯的智能通行
- **紧急避让**：集体规避障碍物

**交通流优化**：
- **拥堵预防**：基于Boids的流量控制
- **路径规划**：动态路线优化
- **事故处理**：智能疏散和绕行

### 🏢 建筑与城市规划

**人群疏散**：
- **紧急疏散**：建筑物的最优逃生路径
- **大型活动**：体育场馆的人流管理
- **公共交通**：地铁站的客流优化

**城市发展**：
- **商业区规划**：基于人流模式的商铺布局
- **居住区设计**：社区结构的优化
- **绿地规划**：公园和休闲区的分布

### 🧬 生物学研究

**动物行为学**：
- **迁徙模式**：候鸟、鱼类的迁徙路线研究
- **觅食行为**：群体觅食策略的分析
- **社会结构**：动物社群的组织形式

**生态系统建模**：
- **种群动态**：捕食者与猎物的关系
- **环境适应**：气候变化对群体行为的影响
- **保护策略**：濒危物种的保护措施

### 💰 金融与经济

**市场建模**：
- **股票交易**：交易者行为的群体模拟
- **市场波动**：恐慌和贪婪的传播模式
- **风险管理**：系统性风险的评估

**经济政策**：
- **消费行为**：消费者群体的决策模式
- **创新扩散**：新技术的市场接受过程
- **资源配置**：社会资源的优化分配

### 🌐 社交网络与传播

**信息传播**：
- **病毒营销**：信息在社交网络中的传播
- **舆论形成**：公众观点的演化过程
- **假新闻控制**：错误信息的传播机制

**社会现象**：
- **流行趋势**：时尚和文化的传播
- **集体行为**：群体心理的数字化研究
- **社会治理**：基于群体行为的政策制定

### 🔮 未来展望

随着人工智能和物联网的发展，Boids算法正在向更高层次演化：

- **认知Boids**：具备学习能力的智能个体
- **多层次Boids**：个体-群体-超群体的层次结构
- **适应性Boids**：能够根据环境自动调整规则
- **量子Boids**：利用量子计算的超级群体智能

雷诺兹的简单算法，正在成为连接虚拟与现实、个体与群体、简单与复杂的桥梁，引领我们走向一个更加智能、协调的未来世界。
    `,
    keywords: ['无人机编队', '自动驾驶', '人群疏散', '生态建模', '金融市场', '社交网络'],
    relatedTopics: ['swarm-robotics', 'smart-cities', 'computational-biology', 'AI-applications']
  }
];

/**
 * Ising模型知识库
 */
export const ISING_KNOWLEDGE: KnowledgeEntry[] = [
  {
    id: 'ising-history',
    topic: '磁性之谜：一个世纪的科学探索',
    simulationType: 'ising-model',
    level: 'beginner',
    content: `
## 1920年：一个简单问题引发的科学革命

在德国汉堡大学的物理系，教授**威廉·伦茨**向他的学生们提出了一个看似简单的问题：**"如果我们把磁性材料想象成一个个小磁针的网格，它们相互影响，那么在不同温度下会发生什么？"**

这个问题看起来朴素无华，却隐藏着物理学中最深刻的奥秘之一。

### 🎓 青年学者的挑战

1925年，伦茨的博士生**恩斯特·伊辛**接受了这个挑战。这位年仅25岁的年轻人决定用数学来回答这个问题。

伊辛做出了一个大胆的简化：
- 把每个原子看作只能**向上↑**或**向下↓**的小磁针
- 相邻的磁针倾向于指向同一方向
- 温度越高，热运动越剧烈，秩序越难维持

经过艰苦的数学推导，伊辛得出了一个令人沮丧的结论：**在一维情况下，无论温度多低，都不会出现磁性！**

这个结果与现实明显矛盾——我们身边到处都是磁铁啊！伊辛沮丧地认为他的模型是失败的。

### 😔 被误解的天才

伊辛的导师伦茨同样感到困惑。他们不知道的是，**一维和二维、三维之间存在着本质的区别**。伊辛的博士论文几乎被学术界忽视，他本人也离开了理论物理学，转向中学教学。

多年后，伊辛曾感慨地说："**我永远没想到，我在25岁时的那篇默默无闻的博士论文，会成为物理学史上最重要的理论之一。**"

### 🏆 迟来的荣耀

**1944年，转机出现了。**

美国数学物理学家**拉斯·昂萨格**用极其精妙的数学技巧，成功解决了**二维伊辛模型**。他的计算表明：

- **低温时**：磁针整齐排列，材料显示强磁性
- **高温时**：磁针随机指向，磁性消失
- **临界温度**：存在一个神奇的温度点，磁性突然出现或消失

这个发现震惊了物理学界！昂萨格也因此获得了1968年的**诺贝尔化学奖**。

### 🔄 相变：物质世界的魔法

伊辛模型揭示了自然界一个最神奇的现象：**相变**。

就像水在100°C突然沸腾一样，磁性材料在特定温度下会突然获得或失去磁性。这种突变并非渐进的，而是**瞬间的、戏剧性的**。

更令人惊叹的是，这种相变现象在自然界无处不在：
- 💧 水的结冰与融化
- 🌟 恒星的诞生与死亡
- 🧠 神经网络的同步激发
- 📈 金融市场的崩溃与繁荣

### 🌟 伊辛的遗产

今天，伊辛模型已经远远超出了磁性研究的范畴：

- **计算机科学**：机器学习中的玻尔兹曼机
- **社会学**：观点传播和社会共识的形成
- **生物学**：神经网络和蛋白质折叠
- **经济学**：市场行为和金融危机的建模

一个最初被认为"失败"的博士论文，最终成为了现代科学的基石之一。

### 💭 深刻的启示

伊�sin模型的历史告诉我们：
- **简单的规则可以产生复杂的现象**
- **理论的价值可能需要时间来验证**
- **数学是揭示自然奥秘的最强工具**
- **失败往往是成功的开始**

正如爱因斯坦所说："**上帝不掷骰子。**" 而伊辛模型告诉我们：即使在看似随机的世界中，也隐藏着深刻的数学秩序。
    `,
    keywords: ['威廉·伦茨', '恩斯特·伊辛', '1925年', '拉斯·昂萨格', '相变', '磁性', '临界温度'],
    relatedTopics: ['ising-phase-transition', 'statistical-mechanics', 'critical-phenomena']
  },
  {
    id: 'ising-physics',
    topic: '磁性的数学密码',
    simulationType: 'ising-model',
    level: 'beginner',
    content: `
## 解密物质的磁性本质

伊辛模型用令人惊叹的简洁性，揭示了磁性背后的物理学原理。让我们深入这个数学宇宙，理解物质如何在微观层面展现磁性。

### 🧲 磁性的微观世界

想象一个巨大的方格纸，每个方格里都住着一个**小磁针**：

- **自旋向上 ↑**：代表 +1，北极朝上
- **自旋向下 ↓**：代表 -1，南极朝上
- **相互作用**：相邻的磁针"希望"指向同一方向

这种"希望"来自量子力学中的**交换相互作用**——这是自然界最基本的力之一。

### ⚖️ 秩序与混沌的较量

在伊辛模型中，两种力量在永恒地较量：

**🔗 交换能（秩序的力量）**
- **作用**：让相邻自旋指向同一方向
- **结果**：创造秩序，产生磁性
- **数学表达**：E_exchange = -J × ∑(相邻自旋的乘积)
- **物理意义**：J > 0 时，同向排列能量更低

**🌡️ 热能（混沌的力量）**
- **作用**：让自旋随机翻转
- **结果**：破坏秩序，消除磁性
- **数学表达**：E_thermal = kT（玻尔兹曼常数 × 温度）
- **物理意义**：温度越高，混乱程度越大

### ⚔️ 温度：决定胜负的关键

**低温环境（T << Tc）**：
- 秩序战胜混沌
- 自旋整齐排列：↑↑↑↑↑
- 材料显示**铁磁性**
- 磁化强度接近最大值

**高温环境（T >> Tc）**：
- 混沌战胜秩序
- 自旋随机分布：↑↓↑↓↑
- 材料显示**顺磁性**
- 磁化强度接近零

**临界温度（T = Tc）**：
- 秩序与混沌势均力敌
- 发生**相变**：磁性突然出现或消失
- 系统处于最"敏感"的状态

### 🎯 哈密顿量：能量的总账本

伊辛模型的核心是**哈密顿量**（总能量）：

**H = -J∑Si·Sj - h∑Si**

让我们逐项解析：

**第一项：-J∑Si·Sj（交换能）**
- J：交换积分（相互作用强度）
- Si, Sj：相邻格点的自旋
- 求和遍历所有相邻对
- 负号表示同向排列能量更低

**第二项：-h∑Si（塞曼能）**
- h：外磁场强度
- 求和遍历所有格点
- 描述外磁场对自旋的影响

### 📊 磁化强度：秩序的度量

**磁化强度**定义为：
M = (1/N) × ∑Si

这个简单的量包含着深刻的物理意义：
- **M = +1**：所有自旋向上，完全有序
- **M = -1**：所有自旋向下，反向有序  
- **M = 0**：自旋随机分布，完全无序
- **0 < |M| < 1**：部分有序状态

### 🌡️ 温度效应的物理图像

**极低温（T → 0）**：
- 热运动几乎静止
- 交换相互作用占主导
- 系统处于**基态**（最低能量状态）
- 磁化强度 M ≈ ±1

**中等温度（T ≈ Tc）**：
- 热运动与交换作用竞争激烈
- 出现**临界涨落**
- 磁化强度快速变化
- 系统展现**临界行为**

**高温（T >> Tc）**：
- 热运动占绝对优势
- 自旋几乎独立随机
- 磁化强度 M ≈ 0
- 系统处于**顺磁态**

### 🔮 相变的神奇瞬间

在临界温度Tc附近，伊辛模型展现出最神奇的现象：

- **相关长度发散**：影响范围变得无限大
- **涨落增强**：系统变得极其敏感
- **临界慢化**：响应时间急剧增长
- **标度不变性**：系统忘记了自己的"尺度"

这些现象不仅存在于磁性材料中，也出现在：
- 液气相变的临界点
- 金融市场的崩溃边缘
- 生态系统的临界状态
- 社会观点的突然转变

伊辛模型告诉我们：**简单的局部相互作用，可以产生复杂的全局行为**！
    `,
    keywords: ['哈密顿量', '磁化强度', '交换相互作用', '相变', '临界温度', '热涨落'],
    relatedTopics: ['ising-history', 'phase-transitions', 'critical-phenomena', 'statistical-mechanics']
  },
  {
    id: 'ising-phase-transition',
    topic: '相变：物质世界的戏剧性转折',
    simulationType: 'ising-model',
    level: 'intermediate',
    content: `
## 当秩序与混沌交锋：相变的科学艺术

相变是自然界最壮观的现象之一。在伊辛模型中，我们可以亲眼见证**秩序如何从混沌中涌现，又如何回归混沌**。

### 🎭 相变的戏剧性

想象一场宏大的舞台剧：

**第一幕：混沌王国（高温）**
- 舞台上的舞者（自旋）各自为政
- 没有统一的节拍，各自随意摆动
- 整体看起来毫无秩序可言
- 磁化强度接近零

**第二幕：临界时刻（T = Tc）**
- 随着温度降低，舞者开始感受到彼此
- 一种微妙的"共鸣"开始出现
- 系统处于极不稳定的边缘状态
- 最轻微的扰动都能改变全局

**第三幕：秩序帝国（低温）**
- 突然间，所有舞者开始同步
- 整个舞台展现出完美的协调
- 一个全新的"有序"世界诞生
- 强烈的磁性突然出现

### 📈 相变的数学肖像

**1. 磁化强度的突变**

在临界温度附近，磁化强度表现出戏剧性的行为：

M(T) ∝ (Tc - T)^β  （T → Tc-）

其中β ≈ 1/8（二维），这被称为**临界指数**。

**2. 磁化率的发散**

磁化率（系统对外磁场的响应）在临界点发散：

χ(T) ∝ |T - Tc|^(-γ)

这意味着在临界点附近，系统对最微小的外场都极其敏感！

**3. 相关长度的无限延伸**

相关长度（自旋间关联的距离）在临界点趋于无穷：

ξ(T) ∝ |T - Tc|^(-ν)

这表明临界点附近，整个系统"连成一片"。

### 🌊 临界涨落：系统的"神经质"

在临界点附近，伊辛系统表现得极其"神经质"：

**巨大的涨落**：
- 磁化强度不停地大幅摆动
- 大小不一的"畴"不断生成、消失
- 系统无法"决定"自己的状态

**长程关联**：
- 远距离的自旋开始"感知"彼此
- 局部的变化能影响整个系统
- 系统失去了"局部性"

**慢弛豫**：
- 系统对扰动的响应变得极其缓慢
- 从一个状态到另一个状态需要很长时间
- 时间尺度急剧增长

### 🔄 动力学相变：秩序的诞生过程

当温度从高温快速降至低温时，我们可以观察到令人着迷的**动力学过程**：

**1. 成核阶段**：
- 随机出现小的有序区域（"核"）
- 这些核在热涨落中时生时灭
- 只有足够大的核才能存活

**2. 生长阶段**：
- 存活的核开始吞噬周围的无序区域
- 有序区域像"感染"一样传播
- 不同的核之间互相竞争

**3. 粗化阶段**：
- 大的有序区域吞并小的区域
- 边界逐渐减少，系统趋于单一状态
- 最终达到完全有序的基态

### 🎯 有限尺寸效应：现实的约束

在真实的材料中，系统尺寸总是有限的，这带来了有趣的效应：

**圆角化的相变**：
- 原本尖锐的相变变得平滑
- 临界温度略有偏移
- 相变的"锐利"程度降低

**尺寸标度律**：
- 系统性质与尺寸的关系遵循特定的标度律
- 通过有限尺寸分析可以精确确定临界点
- 这是数值模拟的重要技术

### 🌍 相变的普适性

伊辛模型的相变属于**同一个普适类**的所有系统都表现出相同的临界行为：

**同类系统**：
- 液气相变
- 合金的有序-无序转变
- 超导体的转变
- 某些生物系统的相变

**普适性质**：
- 临界指数相同
- 标度函数相同
- 临界行为不依赖于微观细节

这个发现告诉我们：**自然界在最深层次上存在着惊人的统一性**！

### 🔮 现代应用：从磁性到机器学习

伊辛相变的概念已经远远超出了物理学：

**机器学习**：
- 玻尔兹曼机基于伊辛模型
- 相变对应学习过程中的"顿悟"
- 临界状态对应最佳的信息处理能力

**神经科学**：
- 大脑的同步态转变
- 意识状态的切换
- 癫痫发作的临界行为

**社会科学**：
- 观点传播的阈值效应
- 流行文化的突然兴起
- 金融危机的级联效应

伊辛模型教会我们：**世界充满了突变和相变，理解这些转折点，就是理解变化的本质**！`,
    keywords: ['相变', '临界点', '临界指数', '普适性', '重整化群'],
    relatedTopics: ['critical-phenomena', 'phase-diagrams', 'universality']
  },
  {
    id: 'ising-applications',
    topic: '从磁铁到人工智能：伊辛模型的现代传奇',
    simulationType: 'ising-model',
    level: 'advanced',
    content: `## 当物理遇见未来：伊辛模型的华丽转身

从最初研究磁性的简单模型，到今天人工智能的重要基石，伊辛模型的应用已经渗透到科学技术的各个角落。

### 神经网络：大脑的数字镜像

**霍普菲尔德网络（1982）**：
- 物理学家约翰·霍普菲尔德的突破性工作
- 神经元状态对应自旋状态，神经连接权重对应磁性相互作用
- 实现联想记忆和模式识别

**玻尔兹曼机**：
- 引入"温度"概念的神经网络
- 高温时随机探索避免局部最优，低温时确定性收敛到全局最优
- 深度学习的重要先驱

### 量子计算的新前沿

**量子退火**：
- D-Wave量子计算机的核心算法
- 利用量子隧穿效应寻找全局最优，解决组合优化问题
- 伊辛模型是天然的量子问题

### 现代技术中的应用

**图像处理**：图像去噪使用伊辛模型
**网络科学**：社交网络中的信息传播
**金融市场**：市场情绪的相变现象
**社会科学**：观点动力学与文化传播

从1925年伊辛的"失败"模型，到今天的广泛应用，这个故事告诉我们：**真正深刻的科学理论，总是能在最意想不到的地方开花结果！**`,
    keywords: ['神经网络', '量子计算', '社会科学', '应用'],
    relatedTopics: ['machine-learning', 'quantum-computing', 'social-dynamics']
  }
];

/**
 * Physarum知识库
 */
export const PHYSARUM_KNOWLEDGE: KnowledgeEntry[] = [
  {
- 这些核在热涨落中时生时灭
- 只有足够大的核才能存活

**2. 生长阶段**：
- 存活的核开始吞噬周围的无序区域
- 有序区域像"感染"一样传播
- 不同的核之间互相竞争

**3. 粗化阶段**：
- 大的有序区域吞并小的区域
- 边界逐渐减少，系统趋于单一状态
- 最终达到完全有序的基态

### 🎯 有限尺寸效应：现实的约束

在真实的材料中，系统尺寸总是有限的，这带来了有趣的效应：

**圆角化的相变**：
- 原本尖锐的相变变得平滑
- 临界温度略有偏移
- 相变的"锐利"程度降低

**尺寸标度律**：
- 系统性质与尺寸的关系遵循特定的标度律
- 通过有限尺寸分析可以精确确定临界点
- 这是数值模拟的重要技术

### 🌍 相变的普适性

伊辛模型的相变属于**同一个普适类**的所有系统都表现出相同的临界行为：

**同类系统**：
- 液气相变
- 合金的有序-无序转变
- 超导体的转变
- 某些生物系统的相变

**普适性质**：
- 临界指数相同
- 标度函数相同
- 临界行为不依赖于微观细节

这个发现告诉我们：**自然界在最深层次上存在着惊人的统一性**！

### 🔮 现代应用：从磁性到机器学习

伊辛相变的概念已经远远超出了物理学：

**机器学习**：
- 玻尔兹曼机基于伊辛模型
- 相变对应学习过程中的"顿悟"
- 临界状态对应最佳的信息处理能力

**神经科学**：
- 大脑的同步态转变
- 意识状态的切换
- 癫痫发作的临界行为

**社会科学**：
- 观点传播的阈值效应
- 流行文化的突然兴起
- 金融危机的级联效应

伊辛模型教会我们：**世界充满了突变和相变，理解这些转折点，就是理解变化的本质**！
    `,
    keywords: ['相变', '临界点', '临界指数', '普适性', '动力学', '有限尺寸效应'],
    relatedTopics: ['ising-physics', 'critical-phenomena', 'phase-diagrams', 'universality']
  },
  {
    id: 'ising-applications',
    topic: '从磁铁到人工智能：伊辛模型的现代传奇',
    simulationType: 'ising-model',
    level: 'advanced',
    content: `
## 当物理遇见未来：伊辛模型的华丽转身

从最初研究磁性的简单模型，到今天人工智能的重要基石，伊辛模型的应用已经渗透到科学技术的各个角落，展现出惊人的生命力。

### 🧠 神经网络：大脑的数字镜像

**霍普菲尔德网络**：
- 1982年，物理学家约翰·霍普菲尔德将伊辛模型引入神经科学
- 神经元的激活状态对应自旋的上下
- 神经元连接的权重对应磁性相互作用
- 网络能够"记忆"模式，实现联想记忆

**玻尔兹曼机**：
- 引入温度概念的神经网络
- 高温时：随机探索，避免局部最优
- 低温时：确定性收敛，找到全局最优
- 成为深度学习的重要先驱

### 🧬 现代应用的广阔天地

**量子计算**：
- D-Wave量子计算机的核心算法
- 利用量子隧穿寻找全局最优解
- 组合优化问题的量子求解

**金融建模**：
- 市场情绪的相变现象
- 金融危机的级联效应
- 风险管理和预测

**社会科学**：
- 观点传播的阈值效应
- 流行文化的突然兴起
- 社会共识的形成过程

### 🔮 未来展望

从伊辛的最初困惑，到今天的广泛应用，这个简单的自旋模型已经成为理解复杂世界的强大工具。它告诉我们：**最深刻的真理往往隐藏在最简单的模型中，而最广泛的应用往往源于最基础的研究**。

正如伊辛本人晚年所感慨的："**也许每一个看似失败的理论，都只是在等待合适的时代来展现它的价值。**"
    `,
    keywords: ['神经网络', '金融建模', '量子计算', '社会物理学', '复杂网络', '人工智能'],
    relatedTopics: ['machine-learning', 'quantum-computing', 'social-physics', 'complex-systems']
  }
];

/**
 * Physarum知识库
 */
export const PHYSARUM_KNOWLEDGE: KnowledgeEntry[] = [
  {
    id: 'physarum-basic',
    topic: 'Physarum黏菌算法',
    simulationType: 'physarum',
    level: 'beginner',
    content: `
## Physarum多头绒泡菌算法

基于真实黏菌（Physarum polycephalum）的觅食行为设计的算法。

### 生物学背景
黏菌是一种单细胞生物，能够：
- **寻找最短路径**：在觅食时自动优化路径
- **网络形成**：构建高效的传输网络
- **集体智能**：无中央控制的自组织行为

### 算法原理
1. **探索阶段**：随机释放搜索代理
2. **路径加强**：成功路径上留下信息素
3. **路径优化**：信息素浓度决定路径选择概率
4. **网络收敛**：最优路径网络逐渐形成

### 应用领域
- 路径规划和优化
- 网络设计
- 迷宫求解
- 供应链优化
    `,
    keywords: ['黏菌', '路径优化', '信息素', '网络', '生物启发'],
    relatedTopics: ['physarum-pathfinding', 'physarum-networks']
  }
];

/**
 * Schelling分离模型知识库
 */
export const SCHELLING_KNOWLEDGE: KnowledgeEntry[] = [
  {
    id: 'schelling-basic',
    topic: 'Schelling分离模型',
    simulationType: 'schelling',
    level: 'beginner',
    content: `
## Schelling种族分离模型

诺贝尔经济学奖得主托马斯·谢林提出的社会动力学模型。

### 模型假设
- **代理人**：两种不同类型（如不同种族）
- **偏好**：希望邻居中至少有一定比例的同类
- **行为**：不满意时会迁移到满意的位置

### 关键发现
即使个体只有**轻微的同类偏好**，也会导致：
- **高度分离**：整体出现明显的聚集区域
- **意外后果**：个人理性导致集体非理性
- **临界阈值**：存在分离突然加剧的临界点

### 社会学意义
- 解释城市居住分离现象
- 说明个人偏好与社会结果的复杂关系
- 揭示社会政策的潜在后果

### 参数影响
- **容忍度阈值**：越低越容易分离
- **空房比例**：影响迁移的灵活性
    `,
    keywords: ['分离', '种族', '社会学', '谢林', '阈值效应'],
    relatedTopics: ['schelling-tolerance', 'schelling-applications']
  }
];

/**
 * 根据模拟类型获取知识库
 */
export function getKnowledgeBase(simulationType: SimulationType): KnowledgeEntry[] {
  switch (simulationType) {
    case 'game-of-life':
      return GAME_OF_LIFE_KNOWLEDGE;
    case 'boids':
      return BOIDS_KNOWLEDGE;
    case 'ising-model':
      return ISING_KNOWLEDGE;
    case 'physarum':
      return PHYSARUM_KNOWLEDGE;
    case 'schelling':
      return SCHELLING_KNOWLEDGE;
    default:
      return [];
  }
}

/**
 * 搜索知识库条目
 */
export function searchKnowledge(
  simulationType: SimulationType,
  query: string,
  level?: 'beginner' | 'intermediate' | 'advanced'
): KnowledgeEntry[] {
  const knowledge = getKnowledgeBase(simulationType);
  const lowerQuery = query.toLowerCase();
  
  return knowledge.filter(entry => {
    const matchesLevel = !level || entry.level === level;
    const matchesContent = 
      entry.topic.toLowerCase().includes(lowerQuery) ||
      entry.content.toLowerCase().includes(lowerQuery) ||
      entry.keywords.some(keyword => keyword.toLowerCase().includes(lowerQuery));
    
    return matchesLevel && matchesContent;
  });
}

/**
 * 获取推荐的学习路径
 */
export function getRecommendedPath(simulationType: SimulationType): string[] {
  const knowledge = getKnowledgeBase(simulationType);
  
  // 按难度级别排序
  const beginner = knowledge.filter(k => k.level === 'beginner').map(k => k.id);
  const intermediate = knowledge.filter(k => k.level === 'intermediate').map(k => k.id);
  const advanced = knowledge.filter(k => k.level === 'advanced').map(k => k.id);
  
  return [...beginner, ...intermediate, ...advanced];
}

/**
 * 为AI导师生成上下文提示
 */
export function generateContextPrompt(simulationType: SimulationType): string {
  const knowledge = getKnowledgeBase(simulationType);
  const basicTopics = knowledge.filter(k => k.level === 'beginner');
  
  if (basicTopics.length === 0) {
    return `当前用户正在探索 ${simulationType} 模拟。`;
  }
  
  const topics = basicTopics.map(k => `- ${k.topic}: ${k.keywords.join(', ')}`).join('\n');
  
  return `当前用户正在探索 ${simulationType} 模拟。相关知识要点：\n${topics}\n\n请根据这些知识要点来回答用户的问题，并鼓励他们进行实际操作和参数调整。`;
}
