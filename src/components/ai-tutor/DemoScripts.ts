/**
 * AI导师演示脚本
 * 预定义的教学演示序列
 */

import type { AIControlCommand } from '@/types/simulation-controls';

export interface DemoStep {
  id: string;
  title: string;
  description: string;
  commands: AIControlCommand[];
  explanation: string;
  duration?: number; // 持续时间（毫秒）
  waitForUserInput?: boolean;
}

export interface DemoScript {
  id: string;
  title: string;
  description: string;
  simulationType: string;
  estimatedDuration: number; // 分钟
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  steps: DemoStep[];
}

/**
 * 生命游戏演示脚本
 */
export const GAME_OF_LIFE_DEMOS: DemoScript[] = [
  {
    id: 'gol-basic-patterns',
    title: '生命游戏基础图案演示',
    description: '通过经典图案了解生命游戏的基本规则和行为',
    simulationType: 'game-of-life',
    estimatedDuration: 5,
    difficulty: 'beginner',
    steps: [
      {
        id: 'step1',
        title: '重置和准备',
        description: '首先重置模拟并设置合适的速度',
        commands: [
          {
            simulationType: 'game-of-life',
            action: 'reset',
            description: '重置游戏状态'
          },
          {
            simulationType: 'game-of-life',
            action: 'setSpeed',
            parameters: { speed: 200 },
            description: '设置中等速度便于观察'
          }
        ],
        explanation: '我们先重置游戏并设置一个合适的速度，这样您可以清楚地观察到细胞的演化过程。'
      },
      {
        id: 'step2',
        title: '振荡器演示',
        description: '加载一个简单的振荡器图案',
        commands: [
          {
            simulationType: 'game-of-life',
            action: 'loadPattern',
            parameters: { pattern: 'blinker' },
            description: '加载眨眼振荡器'
          }
        ],
        explanation: '这是一个"眨眼器"(Blinker)，最简单的振荡器。它会在两种状态之间不断切换，演示了生命游戏中的周期性行为。',
        duration: 3000
      },
      {
        id: 'step3',
        title: '开始观察',
        description: '启动模拟观察振荡器的行为',
        commands: [
          {
            simulationType: 'game-of-life',
            action: 'play',
            description: '开始模拟'
          }
        ],
        explanation: '现在让我们观察这个振荡器的行为。注意它是如何在垂直和水平状态之间切换的。',
        duration: 5000,
        waitForUserInput: true
      },
      {
        id: 'step4',
        title: '滑翔机演示',
        description: '展示移动的滑翔机图案',
        commands: [
          {
            simulationType: 'game-of-life',
            action: 'reset',
            description: '重置为滑翔机演示做准备'
          },
          {
            simulationType: 'game-of-life',
            action: 'loadPattern',
            parameters: { pattern: 'glider' },
            description: '加载滑翔机图案'
          },
          {
            simulationType: 'game-of-life',
            action: 'play',
            description: '开始观察滑翔机移动'
          }
        ],
        explanation: '这是著名的"滑翔机"(Glider)！它会保持形状并在网格中移动，展示了生命游戏中的移动性和稳定性。',
        duration: 8000
      }
    ]
  },
  {
    id: 'gol-rule-exploration',
    title: '生命游戏规则探索',
    description: '通过修改规则来理解生命游戏的机制',
    simulationType: 'game-of-life',
    estimatedDuration: 8,
    difficulty: 'intermediate',
    steps: [
      {
        id: 'step1',
        title: '标准规则演示',
        description: '使用标准的23/3规则',
        commands: [
          {
            simulationType: 'game-of-life',
            action: 'reset',
            description: '重置游戏'
          },
          {
            simulationType: 'game-of-life',
            action: 'setRules',
            parameters: {
              rules: {
                survivalMin: 2,
                survivalMax: 3,
                birthMin: 3,
                birthMax: 3
              }
            },
            description: '设置标准规则：生存2-3，诞生3'
          },
          {
            simulationType: 'game-of-life',
            action: 'loadPattern',
            parameters: { pattern: 'random' },
            description: '加载随机图案'
          }
        ],
        explanation: '这是康威生命游戏的标准规则：B3/S23。观察在这些规则下图案是如何演化的。'
      },
      {
        id: 'step2',
        title: '修改规则实验',
        description: '尝试不同的规则组合',
        commands: [
          {
            simulationType: 'game-of-life',
            action: 'setRules',
            parameters: {
              rules: {
                survivalMin: 1,
                survivalMax: 4,
                birthMin: 2,
                birthMax: 4
              }
            },
            description: '修改为更宽松的规则'
          },
          {
            simulationType: 'game-of-life',
            action: 'play',
            description: '观察新规则下的行为'
          }
        ],
        explanation: '现在我们使用更宽松的规则。注意生命会变得更加活跃和持久！',
        duration: 6000
      }
    ]
  }
];

/**
 * Boids演示脚本
 */
export const BOIDS_DEMOS: DemoScript[] = [
  {
    id: 'boids-three-rules',
    title: 'Boids三大规则演示',
    description: '分别展示分离、对齐、聚合三个规则的效果',
    simulationType: 'boids',
    estimatedDuration: 10,
    difficulty: 'beginner',
    steps: [
      {
        id: 'step1',
        title: '只有分离规则',
        description: '演示纯分离行为',
        commands: [
          {
            simulationType: 'boids',
            action: 'reset',
            description: '重置仿真'
          },
          {
            simulationType: 'boids',
            action: 'setSeparation',
            parameters: { value: 2.0 },
            description: '设置强分离'
          },
          {
            simulationType: 'boids',
            action: 'setAlignment',
            parameters: { value: 0.0 },
            description: '关闭对齐'
          },
          {
            simulationType: 'boids',
            action: 'setCohesion',
            parameters: { value: 0.0 },
            description: '关闭聚合'
          },
          {
            simulationType: 'boids',
            action: 'play',
            description: '开始观察分离行为'
          }
        ],
        explanation: '现在只有分离规则在起作用。观察鸟群如何避免相互碰撞并分散开来。',
        duration: 5000
      },
      {
        id: 'step2',
        title: '添加对齐规则',
        description: '加入对齐行为',
        commands: [
          {
            simulationType: 'boids',
            action: 'setAlignment',
            parameters: { value: 1.0 },
            description: '启用对齐规则'
          }
        ],
        explanation: '现在加入了对齐规则！观察鸟群开始朝相同方向飞行，形成更协调的群体行为。',
        duration: 5000
      },
      {
        id: 'step3',
        title: '完整的群体行为',
        description: '加入聚合规则形成完整行为',
        commands: [
          {
            simulationType: 'boids',
            action: 'setCohesion',
            parameters: { value: 1.0 },
            description: '启用聚合规则'
          }
        ],
        explanation: '最后加入聚合规则！现在您看到的是完整的群体行为：鸟群既保持距离，又对齐方向，还聚集在一起。',
        duration: 8000,
        waitForUserInput: true
      }
    ]
  }
];

/**
 * Ising模型演示脚本
 */
export const ISING_DEMOS: DemoScript[] = [
  {
    id: 'ising-phase-transition',
    title: 'Ising模型相变演示',
    description: '通过温度变化观察磁性材料的相变现象',
    simulationType: 'ising-model',
    estimatedDuration: 12,
    difficulty: 'intermediate',
    steps: [
      {
        id: 'step1',
        title: '低温有序态',
        description: '在低温下观察有序的磁性状态',
        commands: [
          {
            simulationType: 'ising-model',
            action: 'reset',
            description: '重置模拟'
          },
          {
            simulationType: 'ising-model',
            action: 'setAllUp',
            description: '设置所有自旋向上'
          },
          {
            simulationType: 'ising-model',
            action: 'setTemperature',
            parameters: { temperature: 0.5 },
            description: '设置低温'
          },
          {
            simulationType: 'ising-model',
            action: 'play',
            description: '开始低温演化'
          }
        ],
        explanation: '在低温下，热扰动很小，系统保持高度有序的磁性状态。这就是铁磁性材料！',
        duration: 5000
      },
      {
        id: 'step2',
        title: '升温接近临界点',
        description: '逐步升温观察相变',
        commands: [
          {
            simulationType: 'ising-model',
            action: 'setTemperature',
            parameters: { temperature: 2.0 },
            description: '升温至接近临界温度'
          }
        ],
        explanation: '现在升温接近临界点。观察有序结构开始被热扰动破坏，出现畴结构。',
        duration: 6000
      },
      {
        id: 'step3',
        title: '高温无序态',
        description: '高温下的顺磁性状态',
        commands: [
          {
            simulationType: 'ising-model',
            action: 'setTemperature',
            parameters: { temperature: 4.0 },
            description: '升至高温'
          }
        ],
        explanation: '在高温下，热扰动占主导，磁性完全消失，系统进入无序的顺磁性状态。这就是相变！',
        duration: 6000,
        waitForUserInput: true
      }
    ]
  }
];

/**
 * 获取指定模拟类型的演示脚本
 */
export function getDemosForSimulation(simulationType: string): DemoScript[] {
  switch (simulationType) {
    case 'game-of-life':
      return GAME_OF_LIFE_DEMOS;
    case 'boids':
      return BOIDS_DEMOS;
    case 'ising-model':
      return ISING_DEMOS;
    default:
      return [];
  }
}

/**
 * 获取所有演示脚本
 */
export function getAllDemos(): DemoScript[] {
  return [
    ...GAME_OF_LIFE_DEMOS,
    ...BOIDS_DEMOS,
    ...ISING_DEMOS
  ];
}

/**
 * 根据ID查找演示脚本
 */
export function getDemoById(id: string): DemoScript | undefined {
  return getAllDemos().find(demo => demo.id === id);
}
