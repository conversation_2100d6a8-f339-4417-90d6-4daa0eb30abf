import React, { useState, useRef, useEffect, useCallback } from 'react';
import { createPortal } from 'react-dom';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { X, Settings, Move } from 'lucide-react';

interface DraggableChatWindowProps {
  isOpen: boolean;
  onClose: () => void;
  onOpenSettings?: () => void;
  children: React.ReactNode;
  title?: string;
}

/**
 * 可拖动的悬浮聊天窗口
 */
export const DraggableChatWindow: React.FC<DraggableChatWindowProps> = ({
  isOpen,
  onClose,
  onOpenSettings,
  children,
  title = "AI导师"
}) => {
  const [position, setPosition] = useState({ x: 100, y: 100 });
  const [isDragging, setIsDragging] = useState(false);
  const [dragOffset, setDragOffset] = useState({ x: 0, y: 0 });
  const windowRef = useRef<HTMLDivElement>(null);

  // 处理拖动开始
  const handleMouseDown = useCallback((e: React.MouseEvent) => {
    if (!windowRef.current) return;
    
    const rect = windowRef.current.getBoundingClientRect();
    setDragOffset({
      x: e.clientX - rect.left,
      y: e.clientY - rect.top
    });
    setIsDragging(true);
  }, []);

  // 处理拖动
  const handleMouseMove = useCallback((e: MouseEvent) => {
    if (!isDragging) return;

    const newX = e.clientX - dragOffset.x;
    const newY = e.clientY - dragOffset.y;

    // 防止拖出视窗
    const windowWidth = window.innerWidth;
    const windowHeight = window.innerHeight;
    const elementWidth = 400; // 聊天窗口宽度
    const elementHeight = 600; // 聊天窗口高度

    const clampedX = Math.max(0, Math.min(newX, windowWidth - elementWidth));
    const clampedY = Math.max(0, Math.min(newY, windowHeight - elementHeight));

    setPosition({ x: clampedX, y: clampedY });
  }, [isDragging, dragOffset]);

  // 处理拖动结束
  const handleMouseUp = useCallback(() => {
    setIsDragging(false);
  }, []);

  // 绑定全局事件
  useEffect(() => {
    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
      
      return () => {
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
      };
    }
  }, [isDragging, handleMouseMove, handleMouseUp]);

  // ESC键关闭
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Escape' && isOpen) {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleKeyDown);
      return () => document.removeEventListener('keydown', handleKeyDown);
    }
  }, [isOpen, onClose]);

  if (!isOpen) return null;

  const content = (
    <>
      {/* 背景遮罩 */}
      <div 
        className="fixed inset-0 bg-black/20 z-40"
        onClick={onClose}
      />
      
      {/* 聊天窗口 */}
      <div
        ref={windowRef}
        className={cn(
          "fixed z-50 bg-background border rounded-lg shadow-2xl",
          "w-[400px] h-[600px] flex flex-col",
          "transition-shadow duration-200",
          isDragging ? "shadow-3xl cursor-grabbing" : "shadow-2xl"
        )}
        style={{
          left: position.x,
          top: position.y,
        }}
      >
        {/* 标题栏 - 可拖动区域 */}
        <div 
          className={cn(
            "flex items-center justify-between p-3 border-b",
            "bg-muted/50 rounded-t-lg cursor-grab select-none",
            isDragging && "cursor-grabbing"
          )}
          onMouseDown={handleMouseDown}
        >
          <div className="flex items-center gap-2">
            <Move className="w-4 h-4 text-muted-foreground" />
            <h2 className="font-semibold text-sm">{title}</h2>
          </div>
          
          <div className="flex items-center gap-1">
            {onOpenSettings && (
              <Button
                variant="ghost"
                size="sm"
                onClick={onOpenSettings}
                className="h-7 w-7 p-0"
              >
                <Settings className="w-3.5 h-3.5" />
              </Button>
            )}
            <Button
              variant="ghost"
              size="sm"
              onClick={onClose}
              className="h-7 w-7 p-0"
            >
              <X className="w-3.5 h-3.5" />
            </Button>
          </div>
        </div>
        
        {/* 内容区域 */}
        <div className="flex-1 overflow-hidden">
          {children}
        </div>
      </div>
    </>
  );

  // 使用 Portal 渲染到 body
  return createPortal(content, document.body);
};

export default DraggableChatWindow;
