import React, { useState, useRef, useEffect } from 'react';
import { 
  X, 
  Send, 
  Loader2, 
  Co<PERSON>, 
  RotateCcw, 
  Settings,
  MessageSquare,
  AlertCircle,
  Bot,
  User,
  PlayCircle
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';
import { useAITutorContext, useSystemPrompt } from './AITutorProvider';
import { useLLMConnection } from '@/hooks/useLLMConnection';
import { useAISimulationControl } from '@/hooks/useAISimulationControl';
import { useAITutorBoundaryCheck } from '@/hooks/useAITutorBoundaryCheck';
import { getCurrentSimulationFunctions } from './FunctionDefinitions';
import { FunctionCallExecutor } from './FunctionCallExecutor';
import { instructionDecomposer, InstructionStep } from './InstructionDecomposer';
import type { ChatMessage, ToolCall } from '@/types/ai-tutor';
import ReactMarkdown from 'react-markdown';

// Function Call执行器实例
const functionCallExecutor = new FunctionCallExecutor();

interface AITutorChatProps {
  className?: string;
}

/**
 * AI导师聊天界面组件
 * 支持Function Calling进行模拟控制
 * 注意：此组件已移除外层容器，适配悬浮窗口使用
 */
const AITutorChat: React.FC<AITutorChatProps> = ({ 
  className
}) => {
  const { state, actions } = useAITutorContext();
  const { sendMessageWithHistory, testConnection } = useLLMConnection();
  const { getSystemPrompt } = useSystemPrompt();
  const { 
    hasCurrentSimulation,
    currentSimulation
  } = useAISimulationControl();
  const { 
    preprocessUserMessage, 
    isValidTopicPage,
    getCurrentTopicInfo
  } = useAITutorBoundaryCheck();

  // 本地状态
  const [inputMessage, setInputMessage] = useState('');
  const [isStreaming, setIsStreaming] = useState(false);
  const [currentStep, setCurrentStep] = useState<InstructionStep | null>(null);
  const [isWaitingForNextStep, setIsWaitingForNextStep] = useState(false);
  const inputRef = useRef<HTMLInputElement>(null);
  const scrollAreaRef = useRef<HTMLDivElement>(null);

  // 自动滚动到底部
  useEffect(() => {
    if (scrollAreaRef.current) {
      const scrollContainer = scrollAreaRef.current.querySelector('[data-radix-scroll-area-viewport]');
      if (scrollContainer) {
        scrollContainer.scrollTop = scrollContainer.scrollHeight;
      }
    }
  }, [state.chatHistory?.messages]);

  // 执行下一步操作
  const executeNextStep = async () => {
    if (!currentStep || !isWaitingForNextStep) {
      return;
    }

    setIsStreaming(true);
    setIsWaitingForNextStep(false);

    try {
      // 执行当前步骤
      const stepResults = await functionCallExecutor.executeFunctionCalls(currentStep.toolCalls);
      console.log('[AI导师] 步骤执行结果:', stepResults);

      // 完成当前步骤并检查是否有下一步
      const progress = instructionDecomposer.completeCurrentStep(stepResults);

      // 生成反馈消息
      let feedbackContent = functionCallExecutor.generateFeedback(stepResults);

      const progressInfo = instructionDecomposer.getProgress();
      if (progressInfo) {
        feedbackContent += `\n\n**步骤 ${progressInfo.completedSteps}/${progressInfo.totalSteps} 已完成**\n`;
      }

      if (progress.hasNextStep && progress.nextStep) {
        // 还有下一步
        setCurrentStep(progress.nextStep);
        setIsWaitingForNextStep(true);
        feedbackContent += `下一步：${progress.nextStep.description}\n`;
        feedbackContent += '请确认是否继续执行下一步操作。';
      } else {
        // 所有步骤完成
        feedbackContent += '\n\n**所有操作已完成！**';
        setCurrentStep(null);
        instructionDecomposer.clearCurrentInstructions();
      }

      // 添加反馈消息
      const feedbackMessage: ChatMessage = {
        id: `step-feedback-${Date.now()}`,
        role: 'assistant',
        content: feedbackContent,
        timestamp: Date.now()
      };

      actions.addMessage(feedbackMessage);

    } catch (error) {
      console.error('执行步骤失败:', error);

      const errorMessage: ChatMessage = {
        id: `step-error-${Date.now()}`,
        role: 'assistant',
        content: `执行步骤失败: ${error instanceof Error ? error.message : '未知错误'}`,
        timestamp: Date.now()
      };

      actions.addMessage(errorMessage);

      // 清理状态
      setCurrentStep(null);
      setIsWaitingForNextStep(false);
      instructionDecomposer.clearCurrentInstructions();
    } finally {
      setIsStreaming(false);
    }
  };

  // 发送消息处理（支持Function Calling）
  const handleSendMessage = async () => {
    if (!inputMessage.trim() || isStreaming || !state.config) {
      return;
    }

    const messageText = inputMessage.trim();
    setInputMessage('');
    setIsStreaming(true);

    try {
      // 添加用户消息
      const userMessage: ChatMessage = {
        id: Date.now().toString(),
        role: 'user',
        content: messageText,
        timestamp: Date.now()
      };
      actions.addMessage(userMessage);

      // 🚨 智能边界检查 - 异步处理参数映射
      const boundaryCheck = await preprocessUserMessage(messageText);
      
      if (boundaryCheck.shouldBlock) {
        // 如果违反边界条件，直接返回警告消息
        const warningMessage: ChatMessage = {
          id: `boundary-warning-${Date.now()}`,
          role: 'assistant',
          content: boundaryCheck.warningMessage || '请在正确的主题页面提问相关内容。',
          timestamp: Date.now()
        };
        actions.addMessage(warningMessage);
        setIsStreaming(false);
        return;
      }

      // 准备消息历史
      const messages = state.chatHistory?.messages || [];
      const apiMessages = messages.map(msg => ({
        role: msg.role as any,
        content: msg.content
      }));

      // 添加当前用户消息
      apiMessages.push({
        role: 'user',
        content: messageText
      });

      // 获取系统提示词
      const systemPrompt = await getSystemPrompt();

      // 准备Function定义（如果启用了Function Calling）
      const functions = state.config.enableFunctionCalling && hasCurrentSimulation 
        ? getCurrentSimulationFunctions(currentSimulation)
        : undefined;

      // 设置当前模拟到Function执行器
      if (hasCurrentSimulation && currentSimulation) {
        functionCallExecutor.setCurrentSimulation(currentSimulation);
      }

      // 发送消息（支持Function Calling）
      const response = await sendMessageWithHistory(apiMessages, {
        ...state.config,
        systemPrompt
      }, functions);

      // 处理响应
      let assistantContent = response.content;
      
      // 处理函数调用 - 支持分步执行
      if (response.toolCalls && response.toolCalls.length > 0) {
        console.log('[AI导师] 收到函数调用:', response.toolCalls);



        // 分析是否需要分解指令
        const analysis = instructionDecomposer.analyzeUserMessage(messageText, response.toolCalls);
        console.log('[AI导师] 指令分解分析结果:', analysis);

        if (analysis.needsDecomposition) {
          console.log('[AI导师] 需要分解指令，进入分步执行流程');
          // 分解指令为多个步骤
          const decomposed = instructionDecomposer.decomposeInstructions(messageText, response.toolCalls);
          console.log('[AI导师] 指令已分解:', decomposed);

          // 执行第一步
          const firstStep = instructionDecomposer.getCurrentStep();
          if (firstStep) {
            setCurrentStep(firstStep);
            const stepResults = await functionCallExecutor.executeFunctionCalls(firstStep.toolCalls);
            console.log('[AI导师] 第一步执行结果:', stepResults);

            // 完成第一步并检查是否有下一步
            const progress = instructionDecomposer.completeCurrentStep(stepResults);

            if (progress.hasNextStep) {
              // 有下一步，等待用户确认
              setIsWaitingForNextStep(true);
              setCurrentStep(progress.nextStep || null);

              const stepFeedback = functionCallExecutor.generateFeedback(stepResults);
              assistantContent += '\n\n' + stepFeedback;
              assistantContent += `\n\n**步骤 1/${decomposed.steps.length} 已完成**\n`;
              assistantContent += `下一步：${progress.nextStep?.description}\n`;
              assistantContent += '请确认是否继续执行下一步操作。';
            } else {
              // 所有步骤完成
              const stepFeedback = functionCallExecutor.generateFeedback(stepResults);
              assistantContent += '\n\n' + stepFeedback;
              assistantContent += '\n\n**所有操作已完成！**';
              instructionDecomposer.clearCurrentInstructions();
            }
          }
        } else {
          // 不需要分解，直接执行所有函数调用
          console.log('[AI导师] 不需要分解指令，直接执行所有函数调用');
          const functionResults = await functionCallExecutor.executeFunctionCalls(response.toolCalls);
          console.log('[AI导师] 函数执行结果:', functionResults);

          // 生成函数调用反馈
          const feedback = functionCallExecutor.generateFeedback(functionResults);
          if (feedback) {
            assistantContent += '\n\n' + feedback;
          }
        }
      }

      // 添加助手回复
      const assistantMessage: ChatMessage = {
        id: `assistant-${Date.now()}`,
        role: 'assistant',
        content: assistantContent,
        timestamp: Date.now(),
        toolCalls: response.toolCalls,
        metadata: {
          tokens: response.tokens,
          duration: response.duration
        }
      };
      
      actions.addMessage(assistantMessage);

    } catch (error) {
      console.error('发送消息失败:', error);
      
      const errorMessage: ChatMessage = {
        id: `error-${Date.now()}`,
        role: 'assistant',
        content: `❌ 发送消息失败: ${error instanceof Error ? error.message : '未知错误'}`,
        timestamp: Date.now()
      };
      
      actions.addMessage(errorMessage);
    } finally {
      setIsStreaming(false);
    }
  };

  // 按键处理
  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  // 清空历史记录
  const handleClearHistory = () => {
    actions.clearHistory();
  };

  // 测试连接
  const handleTestConnection = async () => {
    if (state.config) {
      await testConnection(state.config);
    }
  };

  // 渲染消息历史
  const messages = state.chatHistory?.messages || [];

  return (
    <div className={cn("flex flex-col h-full bg-background", className)}>
      {/* Function Calling状态指示和快捷操作 */}
      <div className="px-4 py-2 bg-muted/50 border-b">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            {state.config?.enableFunctionCalling ? (
              <>
                <Bot className="w-4 h-4" />
                Function Calling 已启用
                {hasCurrentSimulation && (
                  <Badge variant="outline" className="text-xs ml-2">
                    {currentSimulation} 控制可用
                  </Badge>
                )}
              </>
            ) : (
              <>
                <Badge variant={state.isConnected ? "default" : "destructive"} className="text-xs">
                  {state.isConnected ? '已连接' : '未连接'}
                </Badge>
              </>
            )}
            
            {/* 边界检查状态指示 */}
            {!isValidTopicPage && (
              <Badge variant="destructive" className="text-xs ml-2">
                ⚠️ 请切换到具体页面
              </Badge>
            )}
          </div>
          
          <div className="flex items-center gap-1">
            <Button
              variant="ghost"
              size="sm"
              onClick={handleTestConnection}
              className="h-7 px-2 text-xs"
            >
              <PlayCircle className="w-3 h-3 mr-1" />
              测试
            </Button>
            
            <Button
              variant="ghost"
              size="sm"
              onClick={handleClearHistory}
              className="h-7 px-2 text-xs"
            >
              <RotateCcw className="w-3 h-3 mr-1" />
              清空
            </Button>
          </div>
        </div>
      </div>

      {/* 消息区域 */}
      <ScrollArea 
        ref={scrollAreaRef}
        className="flex-1 p-4"
      >
        <div className="space-y-4">
          {messages.length === 0 ? (
            <div className="text-center text-muted-foreground py-8">
              <Bot className="w-12 h-12 mx-auto mb-4 opacity-50" />
              <p className="text-lg font-medium mb-2">AI导师已准备就绪</p>
              <p className="text-sm">
                {!isValidTopicPage ? (
                  "请先切换到具体的模拟页面，然后我就能为您提供专业指导！"
                ) : (
                  <>
                    你可以询问关于{getCurrentTopicInfo()?.displayName}的任何问题，
                    {state.config?.enableFunctionCalling && hasCurrentSimulation && (
                      <span className="text-primary"> 或直接请求控制当前模拟</span>
                    )}
                  </>
                )}
              </p>
              
              {/* 边界条件提示 */}
              {isValidTopicPage && (
                <div className="mt-4 p-3 bg-muted/30 rounded-lg text-xs">
                  <p className="font-medium text-foreground mb-1">💡 使用提示：</p>
                  <p>• 我只能回答当前主题的问题</p>
                  <p>• 如需讨论其他主题，请先切换页面</p>
                  <p>• 我会提醒您当前主题不支持的参数</p>
                </div>
              )}
            </div>
          ) : (
            messages.map((message) => (
              <MessageItem 
                key={message.id} 
                message={message}
                onCopy={() => navigator.clipboard.writeText(message.content)}
              />
            ))
          )}
          
          {/* 输入流指示器 */}
          {isStreaming && (
            <div className="flex items-start gap-3">
              <div className="w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center flex-shrink-0">
                <Bot className="w-4 h-4 text-primary" />
              </div>
              <div className="flex-1 bg-muted rounded-lg p-3">
                <div className="flex items-center gap-2 text-muted-foreground">
                  <Loader2 className="w-4 h-4 animate-spin" />
                  <span className="text-sm">正在思考...</span>
                </div>
              </div>
            </div>
          )}
        </div>
      </ScrollArea>

      {/* 错误提示 */}
      {state.error && (
        <div className="p-4 border-t">
          <div className="flex items-center gap-2 text-sm text-destructive">
            <AlertCircle className="w-4 h-4" />
            {state.error}
          </div>
        </div>
      )}

      {/* 输入区域 */}
      <div className="p-4 border-t">
        {/* 当前主题提示 */}
        {isValidTopicPage && (
          <div className="mb-3 p-2 bg-muted/50 rounded-md text-sm">
            <div className="flex items-center gap-2 text-muted-foreground">
              <MessageSquare className="w-4 h-4" />
              <span>当前主题：<strong>{getCurrentTopicInfo()?.displayName}</strong></span>
            </div>
            <p className="text-xs text-muted-foreground mt-1">
              💡 我只能回答当前主题的问题。如需讨论其他主题，请先切换到相应页面。
            </p>
          </div>
        )}

        {/* 分步执行提示 */}
        {isWaitingForNextStep && currentStep && (
          <div className="mb-3 p-3 bg-blue-50 border border-blue-200 rounded-md">
            <div className="flex items-center gap-2 text-blue-700 mb-2">
              <PlayCircle className="w-4 h-4" />
              <span className="font-medium">等待执行下一步</span>
            </div>
            <p className="text-sm text-blue-600 mb-3">
              下一步操作：<strong>{currentStep.description}</strong>
            </p>
            <Button
              onClick={executeNextStep}
              disabled={isStreaming}
              size="sm"
              className="w-full"
            >
              {isStreaming ? (
                <>
                  <Loader2 className="w-4 h-4 animate-spin mr-2" />
                  执行中...
                </>
              ) : (
                <>
                  <PlayCircle className="w-4 h-4 mr-2" />
                  继续执行下一步
                </>
              )}
            </Button>
          </div>
        )}

        <div className="flex gap-2">
          <Input
            ref={inputRef}
            value={inputMessage}
            onChange={(e) => setInputMessage(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder={
              !isValidTopicPage
                ? "请先切换到具体的模拟页面..."
                : state.config?.enableFunctionCalling && hasCurrentSimulation
                  ? `问我关于${getCurrentTopicInfo()?.displayName}的问题或要求控制模拟...`
                  : `问我关于${getCurrentTopicInfo()?.displayName}的问题...`
            }
            disabled={isStreaming || !state.isConnected || isWaitingForNextStep}
            className="flex-1"
          />
          <Button
            onClick={handleSendMessage}
            disabled={!inputMessage.trim() || isStreaming || !state.isConnected || isWaitingForNextStep}
            size="icon"
          >
            {isStreaming ? (
              <Loader2 className="w-4 h-4 animate-spin" />
            ) : (
              <Send className="w-4 h-4" />
            )}
          </Button>
        </div>
      </div>
    </div>
  );
};

/**
 * 消息项组件
 */
interface MessageItemProps {
  message: ChatMessage;
  onCopy: () => void;
}

const MessageItem: React.FC<MessageItemProps> = ({ message, onCopy }) => {
  const isUser = message.role === 'user';
  const isError = message.content.startsWith('❌');

  return (
    <div className={cn(
      "flex items-start gap-3",
      isUser && "flex-row-reverse"
    )}>
      {/* 头像 */}
      <div className={cn(
        "w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0",
        isUser ? "bg-primary text-primary-foreground" : "bg-muted"
      )}>
        {isUser ? (
          <User className="w-4 h-4" />
        ) : (
          <Bot className="w-4 h-4" />
        )}
      </div>

      {/* 消息内容 */}
      <div className={cn(
        "flex-1 max-w-[80%]",
        isUser && "text-right"
      )}>
        <div className={cn(
          "rounded-lg p-3 prose prose-sm max-w-none",
          isUser 
            ? "bg-primary text-primary-foreground ml-auto" 
            : isError
              ? "bg-destructive/10 border border-destructive/20"
              : "bg-muted"
        )}>
          {/* 工具调用指示 */}
          {message.toolCalls && message.toolCalls.length > 0 && (
            <div className="mb-2 text-xs opacity-60">
              🔧 执行了 {message.toolCalls.length} 个控制操作
            </div>
          )}
          
          {/* 消息内容 */}
          <ReactMarkdown>{message.content}</ReactMarkdown>
          
          {/* 元数据 */}
          {message.metadata && (
            <div className="mt-2 pt-2 border-t border-current/10 text-xs opacity-60">
              {message.metadata.tokens && (
                <span>Token: {message.metadata.tokens} </span>
              )}
              {message.metadata.duration && (
                <span>耗时: {message.metadata.duration}ms</span>
              )}
            </div>
          )}
        </div>
        
        {/* 操作按钮 */}
        <div className={cn(
          "flex items-center gap-1 mt-1",
          isUser ? "justify-end" : "justify-start"
        )}>
          <Button
            variant="ghost"
            size="sm"
            onClick={onCopy}
            className="h-6 px-2 text-xs opacity-60 hover:opacity-100"
          >
            <Copy className="w-3 h-3 mr-1" />
            复制
          </Button>
          
          <span className="text-xs opacity-40">
            {new Date(message.timestamp).toLocaleTimeString()}
          </span>
        </div>
      </div>
    </div>
  );
};

export default AITutorChat;
