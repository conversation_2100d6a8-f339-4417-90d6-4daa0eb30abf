/**
 * 测试指令分解器功能
 */

import { instructionDecomposer } from './InstructionDecomposer';
import { ToolCall } from '@/lib/llm/types';

// 模拟的函数调用数据
const mockToolCalls: ToolCall[] = [
  {
    id: 'call_1',
    type: 'function',
    function: {
      name: 'schelling_setGridSize',
      arguments: '{"size": 200}'
    }
  },
  {
    id: 'call_2', 
    type: 'function',
    function: {
      name: 'schelling_startSimulation',
      arguments: '{}'
    }
  }
];

// 测试指令分解
export function testInstructionDecomposition() {
  console.log('=== 测试指令分解器 ===');
  
  const userMessage = "将网格大小设置为200，运行仿真看效果";
  
  // 1. 分析是否需要分解
  const analysis = instructionDecomposer.analyzeUserMessage(userMessage, mockToolCalls);
  console.log('分析结果:', analysis);
  
  if (analysis.needsDecomposition) {
    // 2. 分解指令
    const decomposed = instructionDecomposer.decomposeInstructions(userMessage, mockToolCalls);
    console.log('分解后的指令:', decomposed);
    
    // 3. 获取第一步
    const firstStep = instructionDecomposer.getCurrentStep();
    console.log('第一步:', firstStep);
    
    // 4. 模拟完成第一步
    const mockResult = [{ success: true, message: '网格大小已设置为200' }];
    const progress = instructionDecomposer.completeCurrentStep(mockResult);
    console.log('第一步完成后的进度:', progress);
    
    // 5. 获取下一步
    if (progress.hasNextStep) {
      const nextStep = instructionDecomposer.getCurrentStep();
      console.log('下一步:', nextStep);
    }
    
    // 6. 获取进度信息
    const progressInfo = instructionDecomposer.getProgress();
    console.log('进度信息:', progressInfo);
  }
  
  console.log('=== 测试完成 ===');
}

// 测试单个操作（不需要分解）
export function testSingleOperation() {
  console.log('=== 测试单个操作 ===');
  
  const singleToolCall: ToolCall[] = [
    {
      id: 'call_single',
      type: 'function', 
      function: {
        name: 'schelling_setGridSize',
        arguments: '{"size": 100}'
      }
    }
  ];
  
  const userMessage = "将网格大小设置为100";
  const analysis = instructionDecomposer.analyzeUserMessage(userMessage, singleToolCall);
  console.log('单个操作分析结果:', analysis);
  
  console.log('=== 单个操作测试完成 ===');
}

// 在浏览器控制台中运行测试
if (typeof window !== 'undefined') {
  (window as any).testInstructionDecomposition = testInstructionDecomposition;
  (window as any).testSingleOperation = testSingleOperation;
  console.log('测试函数已添加到 window 对象，可以在控制台中运行：');
  console.log('- testInstructionDecomposition()');
  console.log('- testSingleOperation()');
}
