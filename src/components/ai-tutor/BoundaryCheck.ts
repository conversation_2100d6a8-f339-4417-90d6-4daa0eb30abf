/**
 * AI导师边界检查模块
 * 处理页面检测和参数验证功能
 */

import type { SimulationType } from '@/types/ai-tutor';

/**
 * 每个模拟类型的有效参数定义
 */
export const SIMULATION_PARAMETERS: Record<SimulationType, {
  displayName: string;
  categories: {
    [category: string]: {
      displayName: string;
      parameters: {
        [key: string]: {
          displayName: string;
          description: string;
          type: 'number' | 'string' | 'boolean' | 'enum';
          range?: { min: number; max: number };
          options?: string[];
        };
      };
    };
  };
}> = {
  'game-of-life': {
    displayName: '生命游戏',
    categories: {
      basic: {
        displayName: '基础控制',
        parameters: {
          speed: {
            displayName: '运行速度',
            description: '控制模拟运行的速度',
            type: 'number',
            range: { min: 1, max: 100 }
          },
          steps: {
            displayName: '步数',
            description: '批量运行的步数',
            type: 'number',
            range: { min: 1, max: 1000 }
          }
        }
      },
      rules: {
        displayName: '规则设置',
        parameters: {
          survivalRules: {
            displayName: '生存规则',
            description: '细胞存活的邻居数要求',
            type: 'string'
          },
          birthRules: {
            displayName: '诞生规则',
            description: '新细胞诞生的邻居数要求',
            type: 'string'
          }
        }
      },
      patterns: {
        displayName: '图案设置',
        parameters: {
          pattern: {
            displayName: '预设图案',
            description: '可加载的预设图案',
            type: 'enum',
            options: ['glider', 'beacon', 'toad', 'pulsar', 'gosper_gun', 'random']
          }
        }
      },
      appearance: {
        displayName: '外观设置',
        parameters: {
          liveCellColor: {
            displayName: '活细胞颜色',
            description: '活细胞的显示颜色',
            type: 'string'
          },
          deadCellColor: {
            displayName: '死细胞颜色',
            description: '死细胞的显示颜色',
            type: 'string'
          }
        }
      }
    }
  },
  'boids': {
    displayName: 'Boids鸟群模拟',
    categories: {
      basic: {
        displayName: '基础参数',
        parameters: {
          count: {
            displayName: '鸟群数量',
            description: '鸟群中鸟的数量',
            type: 'number',
            range: { min: 1, max: 500 }
          },
          maxSpeed: {
            displayName: '最大速度',
            description: '鸟类的最大飞行速度',
            type: 'number',
            range: { min: 1, max: 10 }
          },
          perceptionRadius: {
            displayName: '感知半径',
            description: '鸟类对邻居的感知范围',
            type: 'number',
            range: { min: 10, max: 100 }
          }
        }
      },
      behavior: {
        displayName: '行为权重',
        parameters: {
          separation: {
            displayName: '分离权重',
            description: '控制鸟类避免碰撞的倾向',
            type: 'number',
            range: { min: 0, max: 5 }
          },
          alignment: {
            displayName: '对齐权重',
            description: '控制鸟类与邻居方向一致的倾向',
            type: 'number',
            range: { min: 0, max: 5 }
          },
          cohesion: {
            displayName: '聚合权重',
            description: '控制鸟类向群体中心移动的倾向',
            type: 'number',
            range: { min: 0, max: 5 }
          }
        }
      }
    }
  },
  'ising-model': {
    displayName: '伊辛模型',
    categories: {
      physics: {
        displayName: '物理参数',
        parameters: {
          temperature: {
            displayName: '温度',
            description: '影响自旋翻转概率的温度参数',
            type: 'number',
            range: { min: 0.1, max: 5.0 }
          },
          magneticField: {
            displayName: '外磁场',
            description: '外部磁场强度',
            type: 'number',
            range: { min: -2.0, max: 2.0 }
          },
          gridSize: {
            displayName: '晶格数量',
            description: '晶格网格的边长数量（N×N网格中的N值）',
            type: 'number',
            range: { min: 10, max: 500 }
          }
        }
      },
      simulation: {
        displayName: '模拟参数',
        parameters: {
          simulationSpeed: {
            displayName: '模拟速度',
            description: '每帧的蒙特卡洛步数',
            type: 'number',
            range: { min: 1, max: 100 }
          },
          thermalizationSteps: {
            displayName: '热化步数',
            description: '系统达到平衡前的步数',
            type: 'number',
            range: { min: 100, max: 100000 }
          },
          samplingSteps: {
            displayName: '采样步数',
            description: '用于统计分析的采样点数量',
            type: 'number',
            range: { min: 100, max: 100000 }
          }
        }
      },
      state: {
        displayName: '状态设置',
        parameters: {
          spinConfiguration: {
            displayName: '自旋配置',
            description: '自旋状态的初始配置',
            type: 'enum',
            options: ['random', 'all_up', 'all_down']
          },
          thermalMotion: {
            displayName: '热运动',
            description: '是否启用热运动影响',
            type: 'boolean'
          }
        }
      },
      appearance: {
        displayName: '外观设置',
        parameters: {
          spinUpColor: {
            displayName: '自旋向上颜色',
            description: '自旋向上的显示颜色',
            type: 'string'
          },
          spinDownColor: {
            displayName: '自旋向下颜色',
            description: '自旋向下的显示颜色',
            type: 'string'
          }
        }
      }
    }
  },
  'physarum': {
    displayName: 'Physarum黏菌模拟',
    categories: {
      agents: {
        displayName: '代理参数',
        parameters: {
          agentCount: {
            displayName: '代理数量',
            description: '黏菌代理的数量',
            type: 'number',
            range: { min: 100, max: 5000 }
          },
          stepSize: {
            displayName: '步长',
            description: '代理每步移动的距离',
            type: 'number',
            range: { min: 0.5, max: 5.0 }
          }
        }
      },
      sensors: {
        displayName: '传感器设置',
        parameters: {
          sensorAngle: {
            displayName: '传感器角度',
            description: '传感器的感知角度',
            type: 'number',
            range: { min: 15, max: 90 }
          },
          sensorDistance: {
            displayName: '传感器距离',
            description: '传感器的感知距离',
            type: 'number',
            range: { min: 5, max: 50 }
          }
        }
      },
      pheromone: {
        displayName: '信息素系统',
        parameters: {
          depositionAmount: {
            displayName: '沉积量',
            description: '信息素的沉积量',
            type: 'number',
            range: { min: 1, max: 50 }
          },
          decayRate: {
            displayName: '衰减率',
            description: '信息素的衰减速率',
            type: 'number',
            range: { min: 0.01, max: 0.2 }
          }
        }
      }
    }
  },
  'schelling': {
    displayName: 'Schelling分离模型',
    categories: {
      population: {
        displayName: '人口参数',
        parameters: {
          similarityThreshold: {
            displayName: '相似性阈值',
            description: '代理满意度的相似邻居比例要求',
            type: 'number',
            range: { min: 0.0, max: 1.0 }
          },
          density: {
            displayName: '人口密度',
            description: '总人口密度',
            type: 'number',
            range: { min: 0.1, max: 1.0 }
          },
          groupRatio: {
            displayName: '群体比例',
            description: '群体A与群体B的比例',
            type: 'number',
            range: { min: 0.1, max: 0.9 }
          }
        }
      },
      grid: {
        displayName: '网格设置',
        parameters: {
          gridSize: {
            displayName: '网格尺寸',
            description: '模拟网格的大小（分辨率）',
            type: 'number',
            range: { min: 20, max: 400 }
          }
        }
      }
    }
  }
};

/**
 * 主题关键词映射
 * 用于检测用户提问是否涉及其他主题
 */
export const TOPIC_KEYWORDS: Record<SimulationType, string[]> = {
  'game-of-life': [
    '生命游戏', '康威', 'Conway', 'Game of Life', 'cellular automata', '细胞自动机',
    '活细胞', '死细胞', '演进', '世代', '邻居', '生存规则', '诞生规则',
    '滑翔机', '信标', '蟾蜍', '脉冲星', '高斯帕机枪', 'glider', 'beacon', 'toad', 'pulsar'
  ],
  'boids': [
    'Boids', '鸟群', '群体行为', '集群', '涌现', '分离', '对齐', '聚合',
    'separation', 'alignment', 'cohesion', '感知半径', '最大速度', '邻居',
    '群体智能', '自组织', 'flocking', 'swarm'
  ],
  'ising-model': [
    '伊辛模型', 'Ising model', '自旋', '磁性', '相变', '临界温度', '磁化强度',
    '温度', '磁场', '铁磁', '反铁磁', '蒙特卡罗', 'Monte Carlo', '统计物理',
    'spin', 'magnetic', 'ferromagnetic', 'antiferromagnetic'
  ],
  'physarum': [
    'Physarum', '黏菌', '信息素', '路径优化', '代理', '传感器', '沉积',
    '衰减', '觅食', '网络形成', '生物启发', '优化算法', 'slime mold',
    'pheromone', 'trail', 'foraging', 'network'
  ],
  'schelling': [
    'Schelling', '分离模型', '种族隔离', '社会分离', '相似性', '阈值', '满意度',
    '迁移', '群体', '密度', '比例', '社会学', '城市规划', 'segregation',
    'similarity', 'threshold', 'agent', 'migration'
  ]
};

/**
 * 检查用户提问是否涉及其他主题
 */
export function checkTopicBoundary(userMessage: string, currentSimulation: SimulationType | null): {
  isValidTopic: boolean;
  suggestedTopic?: SimulationType;
  warningMessage?: string;
} {
  if (!currentSimulation) {
    return { isValidTopic: true };
  }

  const lowerMessage = userMessage.toLowerCase();
  
  // 检查是否涉及其他主题
  for (const [topicType, keywords] of Object.entries(TOPIC_KEYWORDS)) {
    if (topicType === currentSimulation) continue;
    
    // 检查是否包含该主题的关键词
    const hasTopicKeywords = keywords.some(keyword => 
      lowerMessage.includes(keyword.toLowerCase())
    );
    
    if (hasTopicKeywords) {
      const topicDisplayName = SIMULATION_PARAMETERS[topicType as SimulationType].displayName;
      const currentDisplayName = SIMULATION_PARAMETERS[currentSimulation].displayName;
      
      return {
        isValidTopic: false,
        suggestedTopic: topicType as SimulationType,
        warningMessage: `您的问题涉及"${topicDisplayName}"主题，但当前页面是"${currentDisplayName}"。请先切换到"${topicDisplayName}"页面，然后再提问相关内容。`
      };
    }
  }

  return { isValidTopic: true };
}

/**
 * 检查用户询问的参数是否存在于当前主题中
 */
export function checkParameterValidity(userMessage: string, currentSimulation: SimulationType | null): {
  isValidParameter: boolean;
  invalidParameters: string[];
  warningMessage?: string;
} {
  if (!currentSimulation) {
    return { isValidParameter: true, invalidParameters: [] };
  }

  const lowerMessage = userMessage.toLowerCase();
  const simulationParams = SIMULATION_PARAMETERS[currentSimulation];
  const invalidParameters: string[] = [];

  // 常见的参数相关关键词
  const parameterKeywords = [
    '参数', '设置', '调整', '修改', '改变', '配置', '控制',
    'parameter', 'setting', 'adjust', 'modify', 'change', 'configure', 'control',
    '大小', '数量', '速度', '角度', '距离', '阈值', '权重', '比例', '密度',
    'size', 'count', 'speed', 'angle', 'distance', 'threshold', 'weight', 'ratio', 'density'
  ];

  // 检查是否涉及参数相关的内容
  const hasParameterKeywords = parameterKeywords.some(keyword => 
    lowerMessage.includes(keyword.toLowerCase())
  );

  if (!hasParameterKeywords) {
    return { isValidParameter: true, invalidParameters: [] };
  }

  // 收集所有当前模拟的有效参数
  const validParameters = new Set<string>();
  const parameterDisplayNames = new Map<string, string>();

  for (const category of Object.values(simulationParams.categories)) {
    for (const [paramKey, paramConfig] of Object.entries(category.parameters)) {
      validParameters.add(paramKey.toLowerCase());
      validParameters.add(paramConfig.displayName.toLowerCase());
      parameterDisplayNames.set(paramKey.toLowerCase(), paramConfig.displayName);
      parameterDisplayNames.set(paramConfig.displayName.toLowerCase(), paramConfig.displayName);
    }
  }

  // 定义一些常见的无效参数（其他模拟类型的参数）
  const commonInvalidParameters: { [key: string]: string[] } = {
    'game-of-life': [
      '晶格', '格子', '网格大小', '细胞大小', '温度', '磁场', '自旋', '信息素', '代理',
      '传感器', '分离', '对齐', '聚合', '鸟群', '密度', '相似性', '阈值'
    ],
    'boids': [
      '晶格', '格子', '网格大小', '细胞', '温度', '磁场', '自旋', '信息素',
      '传感器角度', '沉积', '衰减', '相似性', '阈值', '群体比例'
    ],
    'ising-model': [
      '晶格大小', '网格大小', '细胞', '鸟群', '代理', '传感器', '信息素',
      '分离', '对齐', '聚合', '相似性', '群体比例', '密度'
    ],
    'physarum': [
      '晶格', '格子', '网格大小', '细胞', '温度', '磁场', '自旋', '鸟群',
      '分离', '对齐', '聚合', '相似性', '群体比例'
    ],
    'schelling': [
      '晶格', '格子', '网格大小', '细胞', '温度', '磁场', '自旋', '信息素',
      '传感器', '代理数量', '步长', '鸟群', '分离', '对齐', '聚合'
    ]
  };

  // 检查是否提到了无效参数
  const potentialInvalidParams = commonInvalidParameters[currentSimulation] || [];
  
  for (const invalidParam of potentialInvalidParams) {
    if (lowerMessage.includes(invalidParam.toLowerCase())) {
      invalidParameters.push(invalidParam);
    }
  }

  if (invalidParameters.length > 0) {
    const displayName = simulationParams.displayName;
    const validParamsList = Array.from(new Set(Array.from(parameterDisplayNames.values())));
    
    return {
      isValidParameter: false,
      invalidParameters,
      warningMessage: `您提到的参数"${invalidParameters.join('", "')}"在当前的"${displayName}"主题中不存在。

${displayName}支持的参数包括：
${validParamsList.map(param => `• ${param}`).join('\n')}

请确认您询问的参数是否属于当前主题，或者切换到相应的主题页面。`
    };
  }

  return { isValidParameter: true, invalidParameters: [] };
}

/**
 * 生成边界检查的系统提示词
 */
export function generateBoundaryCheckPrompt(currentSimulation: SimulationType | null): string {
  if (!currentSimulation) {
    return '';
  }

  const simulationParams = SIMULATION_PARAMETERS[currentSimulation];
  const displayName = simulationParams.displayName;

  // 生成当前主题的参数列表
  const parametersList: string[] = [];
  for (const [categoryKey, category] of Object.entries(simulationParams.categories)) {
    parametersList.push(`**${category.displayName}：**`);
    for (const [paramKey, paramConfig] of Object.entries(category.parameters)) {
      let description = `  • ${paramConfig.displayName}：${paramConfig.description}`;
      if (paramConfig.range) {
        description += `（范围：${paramConfig.range.min}-${paramConfig.range.max}）`;
      } else if (paramConfig.options) {
        description += `（选项：${paramConfig.options.join(', ')}）`;
      }
      parametersList.push(description);
    }
  }

  return `

## 🚨 重要边界条件

### 1. 主题范围限制
- **当前主题页面**：${displayName}
- **讨论范围**：只能讨论与"${displayName}"相关的内容
- **跨主题提问**：如果用户询问其他主题（如生命游戏、Boids、伊辛模型、Physarum、Schelling等），必须提醒用户先切换到相应的主题页面

### 2. 参数验证要求
- **有效参数**：只能修改和解释当前主题页面中存在的参数
- **参数范围**：${displayName}支持的参数包括：

${parametersList.join('\n')}

### 3. 回应策略
当用户违反边界条件时，你必须：

**对于跨主题提问**：
"您的问题涉及[其他主题名称]，但当前页面是"${displayName}"。请先切换到[其他主题名称]页面，然后再提问相关内容。"

**对于无效参数**：
"您提到的参数"[参数名]"在当前的"${displayName}"主题中不存在。${displayName}支持的参数包括：[列出有效参数]"

### 4. 执行要求
- 在回答任何问题前，先检查是否符合边界条件
- 优先提供边界检查的提醒信息
- 只有在边界条件满足时才提供具体的技术解答
- 保持友好但坚定的边界执行`;
}

/**
 * 获取所有主题的映射信息
 */
export function getAllTopicMappings(): Record<SimulationType, { displayName: string; keywords: string[] }> {
  const mappings: Record<SimulationType, { displayName: string; keywords: string[] }> = {} as any;
  
  for (const [key, value] of Object.entries(SIMULATION_PARAMETERS)) {
    const topicKey = key as SimulationType;
    mappings[topicKey] = {
      displayName: value.displayName,
      keywords: TOPIC_KEYWORDS[topicKey]
    };
  }
  
  return mappings;
}
