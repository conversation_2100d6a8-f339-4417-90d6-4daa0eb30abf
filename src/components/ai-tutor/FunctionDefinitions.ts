/**
 * AI导师Function Calling定义生成器
 * 将现有的控制接口转换为LLM可调用的函数定义
 */

import type { FunctionDefinition, SimulationType } from '@/types/ai-tutor';

/**
 * 生成模拟控制函数的定义
 */
export function generateSimulationControlFunctions(simulationType: SimulationType): FunctionDefinition[] {
  const functions: FunctionDefinition[] = [];
  
  // 将连字符转换为下划线用于函数名
  const functionNamePrefix = simulationType.replace(/-/g, '_');

  // 基础控制函数（所有模拟通用）
  functions.push({
    name: `${functionNamePrefix}_start`,
    description: `开始或恢复${getSimulationDisplayName(simulationType)}模拟`,
    parameters: {
      type: 'object',
      properties: {},
      required: []
    }
  });

  functions.push({
    name: `${functionNamePrefix}_pause`,
    description: `暂停${getSimulationDisplayName(simulationType)}模拟`,
    parameters: {
      type: 'object',
      properties: {},
      required: []
    }
  });

  functions.push({
    name: `${functionNamePrefix}_reset`,
    description: `重置${getSimulationDisplayName(simulationType)}模拟到初始状态`,
    parameters: {
      type: 'object',
      properties: {},
      required: []
    }
  });

  functions.push({
    name: `${functionNamePrefix}_get_state`,
    description: `获取${getSimulationDisplayName(simulationType)}模拟的当前状态和参数`,
    parameters: {
      type: 'object',
      properties: {},
      required: []
    }
  });

  // 根据不同模拟类型添加特定的控制函数
  switch (simulationType) {
    case 'game-of-life':
      functions.push(
        {
          name: 'game_of_life_step',
          description: '执行生命游戏的一步演进',
          parameters: {
            type: 'object',
            properties: {},
            required: []
          }
        },
        {
          name: 'game_of_life_run_steps',
          description: '运行生命游戏指定的步数',
          parameters: {
            type: 'object',
            properties: {
              steps: {
                type: 'number',
                description: '要运行的步数，建议在1-1000之间'
              }
            },
            required: ['steps']
          }
        },
        {
          name: 'game_of_life_set_speed',
          description: '设置生命游戏的运行速度',
          parameters: {
            type: 'object',
            properties: {
              speed: {
                type: 'number',
                description: '运行速度（1-100），数值越大越快'
              }
            },
            required: ['speed']
          }
        },
        {
          name: 'game_of_life_set_rules',
          description: '设置生命游戏的规则',
          parameters: {
            type: 'object',
            properties: {
              survivalRules: {
                type: 'string',
                description: '生存规则，如"23"表示邻居数为2或3时细胞存活'
              },
              birthRules: {
                type: 'string',
                description: '诞生规则，如"3"表示邻居数为3时产生新细胞'
              }
            },
            required: ['survivalRules', 'birthRules']
          }
        },
        {
          name: 'game_of_life_load_pattern',
          description: '加载预设的生命游戏图案',
          parameters: {
            type: 'object',
            properties: {
              pattern: {
                type: 'string',
                description: '图案名称',
                enum: ['glider', 'beacon', 'toad', 'pulsar', 'gosper_gun', 'random']
              }
            },
            required: ['pattern']
          }
        },
        {
          name: 'game_of_life_set_colors',
          description: '设置生命游戏的颜色',
          parameters: {
            type: 'object',
            properties: {
              liveCellColor: {
                type: 'string',
                description: '活细胞颜色（CSS颜色值）'
              },
              deadCellColor: {
                type: 'string',
                description: '死细胞颜色（CSS颜色值）'
              }
            }
          }
        }
      );
      break;

    case 'boids':
      functions.push(
        {
          name: 'boids_set_count',
          description: '设置鸟群中鸟的数量',
          parameters: {
            type: 'object',
            properties: {
              count: {
                type: 'number',
                description: '鸟的数量（1-500）'
              }
            },
            required: ['count']
          }
        },
        {
          name: 'boids_set_separation',
          description: '设置鸟群的分离权重',
          parameters: {
            type: 'object',
            properties: {
              weight: {
                type: 'number',
                description: '分离权重（0-5），控制鸟类避免碰撞的倾向'
              }
            },
            required: ['weight']
          }
        },
        {
          name: 'boids_set_alignment',
          description: '设置鸟群的对齐权重',
          parameters: {
            type: 'object',
            properties: {
              weight: {
                type: 'number',
                description: '对齐权重（0-5），控制鸟类与邻居方向一致的倾向'
              }
            },
            required: ['weight']
          }
        },
        {
          name: 'boids_set_cohesion',
          description: '设置鸟群的聚合权重',
          parameters: {
            type: 'object',
            properties: {
              weight: {
                type: 'number',
                description: '聚合权重（0-5），控制鸟类向群体中心移动的倾向'
              }
            },
            required: ['weight']
          }
        },
        {
          name: 'boids_set_max_speed',
          description: '设置鸟的最大速度',
          parameters: {
            type: 'object',
            properties: {
              speed: {
                type: 'number',
                description: '最大速度（1-10）'
              }
            },
            required: ['speed']
          }
        },
        {
          name: 'boids_set_perception_radius',
          description: '设置鸟的感知半径',
          parameters: {
            type: 'object',
            properties: {
              radius: {
                type: 'number',
                description: '感知半径（10-100），影响鸟类对邻居的感知范围'
              }
            },
            required: ['radius']
          }
        }
      );
      break;

    case 'ising-model':
      functions.push(
        {
          name: 'ising_model_step',
          description: '执行伊辛模型的一步Monte Carlo演进',
          parameters: {
            type: 'object',
            properties: {},
            required: []
          }
        },
        {
          name: 'ising_model_set_temperature',
          description: '设置伊辛模型的温度',
          parameters: {
            type: 'object',
            properties: {
              temperature: {
                type: 'number',
                description: '温度值（0.1-5.0），影响自旋翻转的概率'
              }
            },
            required: ['temperature']
          }
        },
        {
          name: 'ising_model_set_magnetic_field',
          description: '设置外磁场强度',
          parameters: {
            type: 'object',
            properties: {
              field: {
                type: 'number',
                description: '磁场强度（-2.0到2.0）'
              }
            },
            required: ['field']
          }
        },
        {
          name: 'ising_model_randomize',
          description: '随机化所有自旋状态',
          parameters: {
            type: 'object',
            properties: {},
            required: []
          }
        },
        {
          name: 'ising_model_set_all_up',
          description: '将所有自旋设置为向上状态',
          parameters: {
            type: 'object',
            properties: {},
            required: []
          }
        },
        {
          name: 'ising_model_set_all_down',
          description: '将所有自旋设置为向下状态',
          parameters: {
            type: 'object',
            properties: {},
            required: []
          }
        },
        {
          name: 'ising_model_set_grid_size',
          description: '设置伊辛模型的晶格数量',
          parameters: {
            type: 'object',
            properties: {
              size: {
                type: 'number',
                description: '晶格数量（10-500），表示N×N网格中的N值'
              }
            },
            required: ['size']
          }
        }
      );
      break;

    case 'physarum':
      functions.push(
        {
          name: 'physarum_set_agent_count',
          description: '设置黏菌代理的数量',
          parameters: {
            type: 'object',
            properties: {
              count: {
                type: 'number',
                description: '代理数量（100-5000）'
              }
            },
            required: ['count']
          }
        },
        {
          name: 'physarum_set_sensor_angle',
          description: '设置传感器角度',
          parameters: {
            type: 'object',
            properties: {
              angle: {
                type: 'number',
                description: '传感器角度（15-90度）'
              }
            },
            required: ['angle']
          }
        },
        {
          name: 'physarum_set_sensor_distance',
          description: '设置传感器距离',
          parameters: {
            type: 'object',
            properties: {
              distance: {
                type: 'number',
                description: '传感器距离（5-50像素）'
              }
            },
            required: ['distance']
          }
        },
        {
          name: 'physarum_set_rotation_angle',
          description: '设置旋转角度',
          parameters: {
            type: 'object',
            properties: {
              angle: {
                type: 'number',
                description: '旋转角度（15-90度）'
              }
            },
            required: ['angle']
          }
        },
        {
          name: 'physarum_set_step_size',
          description: '设置步长',
          parameters: {
            type: 'object',
            properties: {
              size: {
                type: 'number',
                description: '步长（0.5-5.0）'
              }
            },
            required: ['size']
          }
        },
        {
          name: 'physarum_set_deposition_amount',
          description: '设置信息素沉积量',
          parameters: {
            type: 'object',
            properties: {
              amount: {
                type: 'number',
                description: '沉积量（1-50）'
              }
            },
            required: ['amount']
          }
        },
        {
          name: 'physarum_set_decay_rate',
          description: '设置信息素衰减率',
          parameters: {
            type: 'object',
            properties: {
              rate: {
                type: 'number',
                description: '衰减率（0.01-0.2）'
              }
            },
            required: ['rate']
          }
        }
      );
      break;

    case 'schelling':
      functions.push(
        {
          name: 'schelling_step',
          description: '执行Schelling分离模型的一步演进',
          parameters: {
            type: 'object',
            properties: {},
            required: []
          }
        },
        {
          name: 'schelling_set_similarity_threshold',
          description: '设置相似性阈值',
          parameters: {
            type: 'object',
            properties: {
              threshold: {
                type: 'number',
                description: '相似性阈值（0.0-1.0），决定代理满意度的相似邻居比例'
              }
            },
            required: ['threshold']
          }
        },
        {
          name: 'schelling_set_density',
          description: '设置人口密度',
          parameters: {
            type: 'object',
            properties: {
              density: {
                type: 'number',
                description: '人口密度（0.1-1.0）'
              }
            },
            required: ['density']
          }
        },
        {
          name: 'schelling_set_group_ratio',
          description: '设置群体比例',
          parameters: {
            type: 'object',
            properties: {
              ratio: {
                type: 'number',
                description: '群体A与群体B的比例（0.1-0.9）'
              }
            },
            required: ['ratio']
          }
        },
        {
          name: 'schelling_set_grid_size',
          description: '设置网格尺寸（分辨率）',
          parameters: {
            type: 'object',
            properties: {
              size: {
                type: 'number',
                description: '网格尺寸（20-400），决定模拟的分辨率'
              }
            },
            required: ['size']
          }
        }
      );
      break;
  }

  return functions;
}

/**
 * 获取模拟类型的显示名称
 */
function getSimulationDisplayName(simulationType: SimulationType): string {
  const displayNames: Record<SimulationType, string> = {
    'game-of-life': '生命游戏',
    'boids': 'Boids鸟群',
    'ising-model': '伊辛模型',
    'physarum': 'Physarum黏菌',
    'schelling': 'Schelling分离模型'
  };
  
  return displayNames[simulationType] || simulationType;
}

/**
 * 根据当前模拟上下文生成可用的函数定义
 */
export function getCurrentSimulationFunctions(currentSimulation: SimulationType | null): FunctionDefinition[] {
  if (!currentSimulation) {
    return [];
  }
  
  return generateSimulationControlFunctions(currentSimulation);
}

/**
 * 检查函数名是否对应当前活跃的模拟
 */
export function isValidFunctionForSimulation(functionName: string, simulationType: SimulationType): boolean {
  // 将模拟类型的连字符转换为下划线来匹配函数名格式
  const normalizedSimulationType = simulationType.replace(/-/g, '_');
  
  // 只检查下划线格式，因为函数名都是用下划线格式生成的
  return functionName.startsWith(normalizedSimulationType + '_');
}

/**
 * 从函数名解析出模拟类型和动作
 */
export function parseFunctionName(functionName: string): { simulationType: string; action: string } | null {
  const parts = functionName.split('_');
  if (parts.length < 2) {
    return null;
  }
  
  // 处理所有的模拟类型（包括带连字符的）
  let simulationType = '';
  let actionStart = 1;
  
  // 匹配各种模拟类型
  if (parts[0] === 'game' && parts.length > 2 && parts[1] === 'of' && parts[2] === 'life') {
    simulationType = 'game-of-life';
    actionStart = 3;
  } else if (parts[0] === 'ising' && parts.length > 1 && parts[1] === 'model') {
    simulationType = 'ising-model';
    actionStart = 2;
  } else if (parts[0] === 'boids') {
    simulationType = 'boids';
    actionStart = 1;
  } else if (parts[0] === 'physarum') {
    simulationType = 'physarum';
    actionStart = 1;
  } else if (parts[0] === 'schelling') {
    simulationType = 'schelling';
    actionStart = 1;
  } else {
    // 默认情况，假设第一部分是模拟类型
    simulationType = parts[0];
    actionStart = 1;
  }
  
  if (actionStart >= parts.length) {
    return null;
  }
  
  const action = parts.slice(actionStart).join('_');
  
  return { simulationType, action };
}
