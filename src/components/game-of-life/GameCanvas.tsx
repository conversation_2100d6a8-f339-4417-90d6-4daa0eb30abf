import React, { useRef, useEffect, useState } from 'react';

interface GameCanvasProps {
  grid: number[][];
  width: number;
  height: number;
  setCellState: (row: number, col: number, state: number) => void;
  liveCellColor: string;
  deadCellColor: string;
  gridLineColor: string;
}

const GameCanvas: React.FC<GameCanvasProps> = ({
  grid,
  width,
  height,
  setCellState,
  liveCellColor,
  deadCellColor,
  gridLineColor
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [view, setView] = useState({ scale: 1, offsetX: 0, offsetY: 0 });
  const [isPanning, setIsPanning] = useState(false);
  const [lastPanPosition, setLastPanPosition] = useState({ x: 0, y: 0 });
  const [isDrawing, setIsDrawing] = useState(false);
  const lastDrawnCell = useRef<{ row: number, col: number } | null>(null);

  const drawGrid = (ctx: CanvasRenderingContext2D, canvasWidth: number, canvasHeight: number) => {
    const dpr = window.devicePixelRatio || 1;
    
    ctx.clearRect(0, 0, canvasWidth, canvasHeight);
    ctx.save();
    
    // 应用设备像素比缩放
    ctx.scale(dpr, dpr);
    
    // 调整后的canvas尺寸
    const adjustedWidth = canvasWidth / dpr;
    const adjustedHeight = canvasHeight / dpr;
    
    ctx.fillStyle = deadCellColor;
    ctx.fillRect(0, 0, adjustedWidth, adjustedHeight);
    
    // 检查grid是否有效
    if (!grid || !Array.isArray(grid) || grid.length === 0 || 
        !grid[0] || !Array.isArray(grid[0]) || width <= 0 || height <= 0) {
      ctx.restore();
      return;
    }
    
    ctx.translate(view.offsetX, view.offsetY);
    ctx.scale(view.scale, view.scale);

    for (let y = 0; y < height; y++) {
      for (let x = 0; x < width; x++) {
        if (grid[y] && Array.isArray(grid[y]) && grid[y][x] === 1) {
          ctx.fillStyle = liveCellColor;
          ctx.fillRect(x, y, 1, 1);
        }
      }
    }

    if (view.scale > 4) {
      ctx.strokeStyle = gridLineColor;
      ctx.lineWidth = 1 / view.scale;
      ctx.beginPath();
      for (let x = 0; x <= width; x++) {
        ctx.moveTo(x, 0);
        ctx.lineTo(x, height);
      }
      for (let y = 0; y <= height; y++) {
        ctx.moveTo(0, y);
        ctx.lineTo(width, y);
      }
      ctx.stroke();
    }
    
    ctx.restore();
  };

  // Effect to fit grid to canvas and handle resizing
  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const fitToContainer = () => {
      const { width: canvasWidth, height: canvasHeight } = canvas.getBoundingClientRect();
      console.log('fitToContainer called', { canvasWidth, canvasHeight, gridWidth: width, gridHeight: height });
      
      if (canvasWidth === 0 || canvasHeight === 0) {
        console.log('Canvas dimensions are 0, skipping fit');
        return;
      }

      // 处理高DPI显示
      const dpr = window.devicePixelRatio || 1;
      canvas.width = canvasWidth * dpr;
      canvas.height = canvasHeight * dpr;
      canvas.style.width = `${canvasWidth}px`;
      canvas.style.height = `${canvasHeight}px`;
      
      const scaleX = canvasWidth / width;
      const scaleY = canvasHeight / height;
      const scale = Math.min(scaleX, scaleY);
      
      const offsetX = (canvasWidth - width * scale) / 2;
      const offsetY = (canvasHeight - height * scale) / 2;

      console.log('Setting view', { scale, offsetX, offsetY });
      // 设置固定的缩放比例和偏移量
      setView({ scale, offsetX, offsetY });
    };

    const resizeObserver = new ResizeObserver(fitToContainer);
    resizeObserver.observe(canvas);

    // 立即设置，确保初始化
    fitToContainer();
    
    // 额外的延迟确保设置 - 移除这些setTimeout，改为立即设置
    requestAnimationFrame(() => {
      fitToContainer();
    });

    return () => resizeObserver.disconnect();
  }, [width, height]);

  // Effect to draw the grid when it or the view changes
  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas || canvas.width === 0 || canvas.height === 0) return;
    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    drawGrid(ctx, canvas.width, canvas.height);
  }, [grid, view, liveCellColor, deadCellColor, gridLineColor]);

  const getMousePos = (e: React.MouseEvent) => {
    const canvas = canvasRef.current;
    if (!canvas) return { x: 0, y: 0 };
    const rect = canvas.getBoundingClientRect();
    // 不需要考虑DPR，因为我们使用的是CSS坐标
    return { 
      x: e.clientX - rect.left, 
      y: e.clientY - rect.top 
    };
  };

  const handleMouseDown = (e: React.MouseEvent) => {
    console.log('Mouse down event triggered', e.button, 'isPanning:', isPanning, 'isDrawing:', isDrawing);
    console.log('Current view state:', view);
    
    // 阻止默认行为和事件冒泡
    e.preventDefault();
    e.stopPropagation();
    
    if (e.button === 1 || e.ctrlKey) { // Middle mouse button or Ctrl+Click for panning
      setIsPanning(true);
      setLastPanPosition(getMousePos(e));
      console.log('Started panning mode');
    } else if (e.button === 0) { // Left mouse button for drawing
        setIsDrawing(true);
        const pos = getMousePos(e);
        
        // 坐标计算 - 使用更健壮的方法
        let col, row;
        const canvas = canvasRef.current;
        if (!canvas) return;
        
        const rect = canvas.getBoundingClientRect();
        
        // 检查view是否已经正确初始化
        const viewInitialized = view.scale > 0 && (view.offsetX !== 0 || view.offsetY !== 0 || view.scale !== 1);
        
        if (viewInitialized) {
          // 使用正确的view进行坐标转换
          col = Math.floor((pos.x - view.offsetX) / view.scale);
          row = Math.floor((pos.y - view.offsetY) / view.scale);
          console.log('Using view-based coordinate calculation');
        } else {
          // 使用备用坐标计算
          col = Math.floor((pos.x / rect.width) * width);
          row = Math.floor((pos.y / rect.height) * height);
          console.log('Using fallback coordinate calculation');
        }
        
        console.log('Click position:', pos, 'Grid position:', { row, col }, 'View:', view, 'Grid dimensions:', { width, height });
        
        if (row >= 0 && row < height && col >= 0 && col < width && 
            grid[row] && Array.isArray(grid[row]) && typeof grid[row][col] === 'number') {
            const oldState = grid[row][col];
            const newState = oldState === 1 ? 0 : 1;
            console.log('Calling setCellState', { row, col, oldState, newState });
            setCellState(row, col, newState);
            lastDrawnCell.current = { row, col };
        } else {
            console.log('Click outside valid range or invalid grid cell', {
              rowValid: row >= 0 && row < height,
              colValid: col >= 0 && col < width,
              gridRowExists: grid[row] && Array.isArray(grid[row]),
              gridCellValid: grid[row] && typeof grid[row][col] === 'number'
            });
        }
    }
  };

  const handleMouseMove = (e: React.MouseEvent) => {
    const pos = getMousePos(e);
    if (isPanning) {
      const dx = pos.x - lastPanPosition.x;
      const dy = pos.y - lastPanPosition.y;
      setView(v => ({ ...v, offsetX: v.offsetX + dx, offsetY: v.offsetY + dy }));
      setLastPanPosition(pos);
    } else if (isDrawing) {
        // 坐标计算 - 使用更健壮的方法
        let col, row;
        const canvas = canvasRef.current;
        if (!canvas) return;
        
        const rect = canvas.getBoundingClientRect();
        
        // 检查view是否已经正确初始化
        const viewInitialized = view.scale > 0 && (view.offsetX !== 0 || view.offsetY !== 0 || view.scale !== 1);
        
        if (viewInitialized) {
          // 使用正确的view进行坐标转换
          col = Math.floor((pos.x - view.offsetX) / view.scale);
          row = Math.floor((pos.y - view.offsetY) / view.scale);
        } else {
          // 使用备用坐标计算
          col = Math.floor((pos.x / rect.width) * width);
          row = Math.floor((pos.y / rect.height) * height);
        }
        
        if (row >= 0 && row < height && col >= 0 && col < width && 
            grid[row] && Array.isArray(grid[row]) && typeof grid[row][col] === 'number') {
            if (!lastDrawnCell.current || lastDrawnCell.current.row !== row || lastDrawnCell.current.col !== col) {
                const oldState = grid[row][col];
                const newState = oldState === 1 ? 0 : 1;
                console.log('Dragging - setting cell state', { row, col, oldState, newState });
                setCellState(row, col, newState);
                lastDrawnCell.current = { row, col };
            }
        }
    }
  };

  const handleMouseUp = (e: React.MouseEvent) => {
    console.log('Mouse up event triggered', { isPanning, isDrawing });
    e.preventDefault();
    e.stopPropagation();
    setIsPanning(false);
    setIsDrawing(false);
    lastDrawnCell.current = null;
  };
  

  return (
    <canvas
      ref={canvasRef}
      className="w-full h-full cursor-pointer"
      onMouseDown={handleMouseDown}
      onMouseMove={handleMouseMove}
      onMouseUp={handleMouseUp}
      onMouseLeave={handleMouseUp}
      onContextMenu={(e) => e.preventDefault()}
      style={{
        touchAction: 'none',
        userSelect: 'none',
        pointerEvents: 'auto'
      }}
    />
  );
};

export default GameCanvas;
