import React, { useImperative<PERSON>andle, forwardRef } from 'react';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Slider } from "@/components/ui/slider";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Play, Pause, FastForward, RotateCcw } from "lucide-react";
import { patterns } from '@/lib/game-of-life/patterns';
import { GameRules } from '@/lib/game-of-life/core';

interface ControlPanelProps {
  isRunning: boolean;
  togglePlay: () => void;
  step: () => void;
  reset: () => void;
  simulationSpeed: number;
  setSimulationSpeed: (speed: number) => void;
  wrapEdges: boolean;
  setWrapEdges: (wrap: boolean) => void;
  loadPattern: (patternName: string) => void;
  liveCellColor: string;
  setLiveCellColor: (color: string) => void;
  deadCellColor: string;
  setDeadCellColor: (color: string) => void;
  generation: number;
  liveCellCount: number;
  rules: GameRules;
  setRules: React.Dispatch<React.SetStateAction<GameRules>>;
  openAccordion: string | undefined;
  setOpenAccordion: (value: string | undefined) => void;
}

export interface GameOfLifeControlPanelRef {
  setSpeed: (speed: number) => void;
  setWrapBoundary: (wrap: boolean) => void;
  setLifeRules: (rules: GameRules) => void;
  loadPresetPattern: (patternName: string) => void;
  setLiveCellColor: (color: string) => void;
  setDeadCellColor: (color: string) => void;
  clickTogglePlay: () => void;
  clickStep: () => void;
  clickReset: () => void;
}

const ControlPanel = forwardRef<GameOfLifeControlPanelRef, ControlPanelProps>(({
  isRunning, togglePlay, step, reset,
  simulationSpeed, setSimulationSpeed,
  wrapEdges, setWrapEdges,
  loadPattern,
  liveCellColor, setLiveCellColor,
  deadCellColor, setDeadCellColor,
  generation, liveCellCount,
  rules, setRules,
  openAccordion, setOpenAccordion
}, ref) => {
  const handleRuleChange = (ruleName: keyof GameRules, value: number) => {
    setRules(prevRules => ({ ...prevRules, [ruleName]: value }));
  };

  // 暴露给外部调用的方法 - 这些方法会模拟UI控件的操作
  useImperativeHandle(ref, () => ({
    setSpeed: (speed: number) => {
      // 将百分比转换为毫秒间隔：百分比越高，间隔越小（速度越快）
      // 1% = 490ms, 100% = 10ms
      const millisecondsInterval = 500 - (speed * 4.9);
      setSimulationSpeed(millisecondsInterval);
    },
    setWrapBoundary: (wrap: boolean) => {
      setWrapEdges(wrap);
    },
    setLifeRules: (rules: GameRules) => {
      setRules(rules);
    },
    loadPresetPattern: (patternName: string) => {
      loadPattern(patternName);
    },
    setLiveCellColor: (color: string) => {
      setLiveCellColor(color);
    },
    setDeadCellColor: (color: string) => {
      setDeadCellColor(color);
    },
    clickTogglePlay: () => {
      togglePlay();
    },
    clickStep: () => {
      step();
    },
    clickReset: () => {
      reset();
    },
  }));

  return (
    <div className="w-full md:w-80 flex flex-col gap-6 p-4 bg-card border-l h-full overflow-y-auto scrollbar-thin scrollbar-track-transparent scrollbar-thumb-cyan-600/20 hover:scrollbar-thumb-cyan-600/40">
      <h2 className="text-2xl font-bold text-primary">生命游戏 (Game of Life)</h2>
      <p className="text-sm text-muted-foreground">
        通过简单的规则涌现出复杂模式的细胞自动机。点击画布以设置初始状态，或从『预设图案』中选择。
      </p>
      
      <div className="space-y-2">
        <div className="flex items-center justify-between text-sm">
          <span>代数:</span>
          <span className="font-mono text-primary">{generation}</span>
        </div>
        <div className="flex items-center justify-between text-sm">
          <span>存活细胞:</span>
          <span className="font-mono text-primary">{liveCellCount}</span>
        </div>
      </div>

      <div className="grid grid-cols-3 gap-2">
        <Button onClick={togglePlay} className="col-span-1">
          {isRunning ? <Pause className="w-4 h-4" /> : <Play className="w-4 h-4" />}
        </Button>
        <Button onClick={step} disabled={isRunning}><FastForward className="w-4 h-4" /></Button>
        <Button onClick={reset}><RotateCcw className="w-4 h-4" /></Button>
      </div>

      <div className="space-y-2">
        <Label htmlFor="speed">模拟速度</Label>
        <Slider
          id="speed"
          min={10}
          max={500}
          step={10}
          value={[500 - simulationSpeed]}
          onValueChange={(value) => setSimulationSpeed(500 - value[0])}
        />
        <div className="text-xs text-muted-foreground">
          速度: {Math.round((500 - simulationSpeed) / 4.9)}%
        </div>
      </div>

      <div className="space-y-2">
        <Label htmlFor="pattern">预设图案</Label>
        <Select onValueChange={loadPattern}>
          <SelectTrigger id="pattern">
            <SelectValue placeholder="加载图案..." />
          </SelectTrigger>
          <SelectContent>
            {Object.keys(patterns).map(name => (
              <SelectItem key={name} value={name}>{name}</SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      <div className="flex items-center space-x-2">
        <Checkbox id="wrap" checked={wrapEdges} onCheckedChange={(checked) => setWrapEdges(Boolean(checked))} />
        <Label htmlFor="wrap">环形边界</Label>
      </div>

      <div className="space-y-2">
        <Label>生命规则</Label>
        <div className="space-y-4">
          <div className="space-y-2">
            <div className="flex justify-between items-center">
              <Label htmlFor="survivalMin" className="text-sm">存活 (最小邻居)</Label>
              <span className="font-mono text-primary text-sm">{rules.survivalMin}</span>
            </div>
            <Slider
              id="survivalMin"
              min={0} max={8} step={1} value={[rules.survivalMin]}
              onValueChange={(value) => handleRuleChange('survivalMin', value[0])}
            />
          </div>
          <div className="space-y-2">
            <div className="flex justify-between items-center">
              <Label htmlFor="survivalMax" className="text-sm">存活 (最大邻居)</Label>
              <span className="font-mono text-primary text-sm">{rules.survivalMax}</span>
            </div>
            <Slider
              id="survivalMax"
              min={0} max={8} step={1} value={[rules.survivalMax]}
              onValueChange={(value) => handleRuleChange('survivalMax', value[0])}
            />
          </div>
          <div className="space-y-2">
            <div className="flex justify-between items-center">
              <Label htmlFor="birthMin" className="text-sm">诞生 (最小邻居)</Label>
              <span className="font-mono text-primary text-sm">{rules.birthMin}</span>
            </div>
            <Slider
              id="birthMin"
              min={0} max={8} step={1} value={[rules.birthMin]}
              onValueChange={(value) => handleRuleChange('birthMin', value[0])}
            />
          </div>
          <div className="space-y-2">
            <div className="flex justify-between items-center">
              <Label htmlFor="birthMax" className="text-sm">诞生 (最大邻居)</Label>
              <span className="font-mono text-primary text-sm">{rules.birthMax}</span>
            </div>
            <Slider
              id="birthMax"
              min={0} max={8} step={1} value={[rules.birthMax]}
              onValueChange={(value) => handleRuleChange('birthMax', value[0])}
            />
          </div>
        </div>
      </div>

      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="live-color">活细胞颜色</Label>
          <Input 
            id="live-color" 
            type="color" 
            value={liveCellColor} 
            onChange={e => setLiveCellColor(e.target.value)} 
            className="p-1 h-10 w-full"
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="dead-color">死细胞颜色</Label>
          <Input 
            id="dead-color" 
            type="color" 
            value={deadCellColor} 
            onChange={e => setDeadCellColor(e.target.value)} 
            className="p-1 h-10 w-full"
          />
        </div>
      </div>
    </div>
  );
});

ControlPanel.displayName = 'ControlPanel';

export default ControlPanel;
