import React, { useState, useRef, useEffect } from 'react';
import { useGameOfLife } from '@/hooks/useGameOfLife';
import GameCanvas from './GameCanvas';
import ControlPanel, { GameOfLifeControlPanelRef } from './ControlPanel';
import RulesExplanation from './RulesExplanation';
import { patterns } from '@/lib/game-of-life/patterns';
import { GameRules } from '@/lib/game-of-life/core';
import { AITutorButton } from '@/components/ai-tutor';
import { useAITutorActions, useAITutorContext } from '@/components/ai-tutor/AITutorProvider';
import { useAISimulationControl } from '@/hooks/useAISimulationControl';
import type { GameOfLifeControls } from '@/types/simulation-controls';

const initialRules: GameRules = {
  survivalMin: 2,
  survivalMax: 3,
  birthMin: 3,
  birthMax: 3,
};

interface GameOfLifePageProps {
  isActive: boolean;
}

const GameOfLifePage: React.FC<GameOfLifePageProps> = ({ isActive }) => {
  const [config, setConfig] = useState({
    width: 50,    // 默认尺寸，避免0值
    height: 50,   // 默认尺寸，避免0值
    speed: 50,
    wrapEdges: true,
  });
  
  const canvasContainerRef = useRef<HTMLDivElement>(null);
  const controlPanelRef = useRef<GameOfLifeControlPanelRef>(null);

  const [rules, setRules] = useState<GameRules>(initialRules);

  const [colors, setColors] = useState({
    liveCellColor: '#00FFFF', // Cyan
    deadCellColor: '#111827', // Dark gray
    gridLineColor: '#374151', // Lighter gray
  });

  const [openAccordion, setOpenAccordion] = useState<string | undefined>();

  // AI导师相关hooks
  const { setSimulationContext } = useAITutorActions();
  const { registerSimulationControls, unregisterSimulationControls } = useAISimulationControl();

  useEffect(() => {
    const container = canvasContainerRef.current;
    if (!container) return;

    const CELL_SIZE = 10; // Adjust this to change cell density
    const MIN_SIZE = 10;  // 最小网格尺寸

    const updateGridSize = () => {
      const { width, height } = container.getBoundingClientRect();
      console.log('Container size changed:', { width, height });
      
      if (width > 0 && height > 0) {
        const newGridWidth = Math.max(MIN_SIZE, Math.floor(width / CELL_SIZE));
        const newGridHeight = Math.max(MIN_SIZE, Math.floor(height / CELL_SIZE));
        
        console.log('Calculated new grid size:', { newGridWidth, newGridHeight });
        
        setConfig(c => {
          if (c.width !== newGridWidth || c.height !== newGridHeight) {
            console.log('Updating config with new size:', { 
              oldSize: { width: c.width, height: c.height },
              newSize: { width: newGridWidth, height: newGridHeight }
            });
            return { ...c, width: newGridWidth, height: newGridHeight };
          }
          return c;
        });
      }
    };

    const observer = new ResizeObserver(updateGridSize);
    observer.observe(container);
    
    // Set initial size with a small delay to ensure container is rendered
    console.log('Setting initial timeout for grid size update');
    setTimeout(() => {
      updateGridSize();
    }, 100);

    return () => {
      observer.disconnect();
    };
  }, []);

  const game = useGameOfLife({ ...config, rules, isActive });

  // AI控制接口注册
  useEffect(() => {
    if (!isActive) {
      // 如果页面不活跃，取消注册控制接口
      unregisterSimulationControls('game-of-life');
      return;
    }

    // 设置模拟上下文
    setSimulationContext({
      type: 'game-of-life',
      isActive: isActive,
      currentParams: {
        ...config,
        rules,
        colors
      }
    });

    // 注册控制接口 - 通过ref操作UI控件
    const controls: GameOfLifeControls = {
      // 基础控制类 - 通过ref调用控制面板的方法
      startSimulation: (currentState?: any) => {
        console.log('[生命游戏控制] startSimulation被调用');
        if (!game.isRunning) {
          controlPanelRef.current?.clickTogglePlay();
        }
        return {
          success: true,
          currentState: {
            simulation_is_running: game.isRunning,
            simulation_is_paused: !game.isRunning
          }
        };
      },
      
      pauseSimulation: (currentState?: any) => {
        console.log('[生命游戏控制] pauseSimulation被调用');
        if (game.isRunning) {
          controlPanelRef.current?.clickTogglePlay();
        }
        return {
          success: true,
          currentState: {
            simulation_is_running: game.isRunning,
            simulation_is_paused: !game.isRunning
          }
        };
      },
      
      stepwiseSimulation: () => {
        console.log('[生命游戏控制] stepwiseSimulation被调用');
        controlPanelRef.current?.clickStep();
        return {
          success: true,
          currentState: {
            simulation_is_running: game.isRunning,
            generation: game.generation,
            liveCellCount: game.liveCellCount
          }
        };
      },
      
      runMultipleSteps: (steps: number) => {
        console.log(`[生命游戏控制] runMultipleSteps被调用: ${steps}步`);
        const clampedSteps = Math.max(1, Math.min(1000, Math.floor(steps)));
        let successfulSteps = 0;
        
        try {
          for (let i = 0; i < clampedSteps; i++) {
            controlPanelRef.current?.clickStep();
            successfulSteps++;
          }
          return {
            success: true,
            currentState: {
              simulation_is_running: game.isRunning,
              generation: game.generation,
              liveCellCount: game.liveCellCount,
              stepsExecuted: successfulSteps
            }
          };
        } catch (error) {
          console.error('Multiple steps execution failed:', error);
          return {
            success: false,
            message: `执行了${successfulSteps}步后失败`,
            currentState: {
              simulation_is_running: game.isRunning,
              generation: game.generation,
              liveCellCount: game.liveCellCount,
              stepsExecuted: successfulSteps
            }
          };
        }
      },
      
      resetSimulation: () => {
        console.log('[生命游戏控制] resetSimulation被调用');
        controlPanelRef.current?.clickReset();
        return {
          success: true,
          currentState: {
            simulation_is_running: game.isRunning,
            generation: game.generation,
            liveCellCount: game.liveCellCount
          }
        };
      },
      
      // 参数设置类 - 通过ref调用控制面板的方法
      setSimulationSpeed: (speed: number) => {
        console.log(`[生命游戏控制] setSimulationSpeed被调用: ${speed}`);
        controlPanelRef.current?.setSpeed(speed);
        const clampedSpeed = Math.max(1, Math.min(100, speed));
        return {
          success: true,
          actualValue: clampedSpeed
        };
      },
      
      setWrapBoundary: (wrap: boolean) => {
        console.log(`[生命游戏控制] setWrapBoundary被调用: ${wrap}`);
        controlPanelRef.current?.setWrapBoundary(wrap);
        return {
          success: true,
          actualValue: wrap
        };
      },
      
      setLifeRules: (newRules) => {
        console.log(`[生命游戏控制] setLifeRules被调用:`, newRules);
        controlPanelRef.current?.setLifeRules(newRules);
        return {
          success: true,
          actualValue: newRules
        };
      },
      
      // 预设图案选择 - 通过ref调用控制面板的方法
      loadPresetPattern: (patternName: string) => {
        console.log(`[生命游戏控制] loadPresetPattern被调用: ${patternName}`);
        const pattern = patterns[patternName];
        if (pattern) {
          controlPanelRef.current?.loadPresetPattern(patternName);
          return {
            success: true,
            actualValue: patternName
          };
        } else {
          return {
            success: false,
            actualValue: '',
            message: `未找到图案: ${patternName}`
          };
        }
      },
      
      // 颜色设置 - 通过ref调用控制面板的方法
      setLiveCellColor: (color: string) => {
        console.log(`[生命游戏控制] setLiveCellColor被调用: ${color}`);
        controlPanelRef.current?.setLiveCellColor(color);
        return {
          success: true,
          actualValue: color
        };
      },
      
      setDeadCellColor: (color: string) => {
        console.log(`[生命游戏控制] setDeadCellColor被调用: ${color}`);
        controlPanelRef.current?.setDeadCellColor(color);
        return {
          success: true,
          actualValue: color
        };
      },
      
      // 状态查询类
      getCurrentState: () => ({
        isRunning: game.isRunning,
        generation: game.generation,
        liveCellCount: game.liveCellCount,
        speed: Math.round((500 - config.speed) / 4.9), // 转换为1-100的百分比
        speedRaw: config.speed, // 原始毫秒值
        wrapEdges: config.wrapEdges,
        rules: rules,
        liveCellColor: colors.liveCellColor,
        deadCellColor: colors.deadCellColor
      }),
      
      // 兼容性：保持旧接口
      togglePlay: () => {
        controlPanelRef.current?.clickTogglePlay();
      },
      step: () => {
        controlPanelRef.current?.clickStep();
      },
      reset: () => {
        controlPanelRef.current?.clickReset();
      },
      setSpeed: (speed: number) => {
        controlPanelRef.current?.setSpeed(speed);
      },
      setWrapEdges: (wrap: boolean) => {
        controlPanelRef.current?.setWrapBoundary(wrap);
      },
      setRules: (newRules: any) => {
        controlPanelRef.current?.setLifeRules(newRules);
      },
      loadPattern: (patternName: string) => {
        controlPanelRef.current?.loadPresetPattern(patternName);
      },
      getState: () => ({
        isRunning: game.isRunning,
        generation: game.generation,
        liveCellCount: game.liveCellCount,
        speed: Math.round((500 - config.speed) / 4.9),
        wrapEdges: config.wrapEdges,
        rules: rules,
        liveCellColor: colors.liveCellColor,
        deadCellColor: colors.deadCellColor
      })
    };

    registerSimulationControls('game-of-life', {
      type: 'game-of-life',
      controls
    });

    // 清理函数
    return () => {
      unregisterSimulationControls('game-of-life');
    };
  }, [
    isActive, config, rules, colors,
    setSimulationContext, registerSimulationControls, unregisterSimulationControls
  ]);

  // 确保初始化后立即同步grid尺寸
  const [initialSyncDone, setInitialSyncDone] = useState(false);
  
  useEffect(() => {
    if (!initialSyncDone && config.width > 50 && config.height > 50) {
      // 当config从默认值(50x50)更新为实际尺寸时，强制重置以确保同步
      console.log('Forcing initial sync after config update');
      setTimeout(() => {
        game.reset();
        setInitialSyncDone(true);
      }, 50);
    }
  }, [config.width, config.height, initialSyncDone, game]);

  // Debug: 跟踪game状态
  useEffect(() => {
    console.log('Game state changed:', {
      gridSize: game.grid.length > 0 ? `${game.grid[0].length}x${game.grid.length}` : 'empty',
      isRunning: game.isRunning,
      generation: game.generation,
      liveCellCount: game.liveCellCount,
      config: config
    });
  }, [game.grid, game.isRunning, game.generation, game.liveCellCount, config]);

  const handleLoadPattern = (patternName: string) => {
    const pattern = patterns[patternName];
    if (pattern) {
      game.loadPattern(pattern);
    }
  };

  const handleReset = () => {
    game.reset();
    setRules(initialRules);
  };

  return (
    <div className="flex flex-col md:flex-row h-[calc(100vh-150px)] w-full bg-background">
      <div ref={canvasContainerRef} className="flex-grow h-full w-full md:w-auto relative border-2 border-cyan-400/50 rounded-lg overflow-hidden">
        <div className="absolute inset-0 w-full h-full">
          <GameCanvas 
            grid={game.grid}
            width={config.width}
            height={config.height}
            setCellState={game.setCellState}
            liveCellColor={colors.liveCellColor}
            deadCellColor={colors.deadCellColor}
            gridLineColor={colors.gridLineColor}
          />
        </div>
        <div className="absolute bottom-4 left-4 pointer-events-none">
          <div className="pointer-events-auto">
            <RulesExplanation />
          </div>
        </div>
      </div>
      <ControlPanel
        ref={controlPanelRef}
        isRunning={game.isRunning}
        togglePlay={game.togglePlay}
        step={game.step}
        reset={handleReset}
        simulationSpeed={config.speed}
        setSimulationSpeed={(speed) => setConfig(c => ({...c, speed}))}
        wrapEdges={config.wrapEdges}
        setWrapEdges={(wrap) => setConfig(c => ({...c, wrapEdges: wrap}))}
        loadPattern={handleLoadPattern}
        liveCellColor={colors.liveCellColor}
        setLiveCellColor={(color) => setColors(c => ({...c, liveCellColor: color}))}
        deadCellColor={colors.deadCellColor}
        setDeadCellColor={(color) => setColors(c => ({...c, deadCellColor: color}))}
        generation={game.generation}
        liveCellCount={game.liveCellCount}
        rules={rules}
        setRules={setRules}
        openAccordion={openAccordion}
        setOpenAccordion={setOpenAccordion}
      />
      
      {/* AI导师按钮 */}
      <AITutorButton position="top-left" />
    </div>
  );
};

export default GameOfLifePage;
