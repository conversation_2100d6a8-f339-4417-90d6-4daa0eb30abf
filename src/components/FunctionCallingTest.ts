/**
 * Function Calling测试脚本
 * 验证AI导师控制功能是否正常工作
 */

export const testAllSimulations = async () => {
  console.log('🧪 开始全面测试Function Calling功能');
  
  const { simulationControlManager } = await import('./ai-tutor/SimulationControlManager');
  
  // 测试函数定义
  const testGameOfLife = async () => {
    console.log('\n🎮 测试生命游戏控制...');
    
    const tests = [
      {
        name: '开始仿真',
        command: { simulationType: 'game-of-life', action: 'start', description: '开始测试' }
      },
      {
        name: '设置速度',
        command: { 
          simulationType: 'game-of-life', 
          action: 'setSpeed', 
          parameters: { speed: 150 },
          description: '设置速度测试' 
        }
      },
      {
        name: '设置规则',
        command: { 
          simulationType: 'game-of-life', 
          action: 'setRules', 
          parameters: { 
            rules: {
              survivalMin: 2,
              survivalMax: 3,
              birthMin: 3,
              birthMax: 3
            }
          },
          description: '设置规则测试' 
        }
      },
      {
        name: '重置仿真',
        command: { simulationType: 'game-of-life', action: 'reset', description: '重置测试' }
      }
    ];
    
    for (const test of tests) {
      try {
        console.log(`  ▶️ ${test.name}...`);
        const result = await simulationControlManager.executeCommand(test.command);
        console.log(`  ${result.success ? '✅' : '❌'} ${test.name}: ${result.message}`);
        if (!result.success) {
          console.error(`    错误详情:`, result.error);
        }
        await new Promise(resolve => setTimeout(resolve, 500)); // 等待500ms
      } catch (error) {
        console.error(`  ❌ ${test.name} 异常:`, error);
      }
    }
  };
  
  const testBoids = async () => {
    console.log('\n🐦 测试Boids仿真控制...');
    
    const tests = [
      {
        name: '开始仿真',
        command: { simulationType: 'boids', action: 'start', description: '开始测试' }
      },
      {
        name: '设置鸟群数量',
        command: { 
          simulationType: 'boids', 
          action: 'setNumBoids', 
          parameters: { count: 100 },
          description: '设置数量测试' 
        }
      },
      {
        name: '设置分离权重',
        command: { 
          simulationType: 'boids', 
          action: 'setSeparation', 
          parameters: { value: 1.5 },
          description: '设置分离测试' 
        }
      },
      {
        name: '暂停仿真',
        command: { simulationType: 'boids', action: 'pause', description: '暂停测试' }
      }
    ];
    
    for (const test of tests) {
      try {
        console.log(`  ▶️ ${test.name}...`);
        const result = await simulationControlManager.executeCommand(test.command);
        console.log(`  ${result.success ? '✅' : '❌'} ${test.name}: ${result.message}`);
        if (!result.success) {
          console.error(`    错误详情:`, result.error);
        }
        await new Promise(resolve => setTimeout(resolve, 500));
      } catch (error) {
        console.error(`  ❌ ${test.name} 异常:`, error);
      }
    }
  };
  
  const testIsingModel = async () => {
    console.log('\n🧲 测试Ising模型控制...');
    
    const tests = [
      {
        name: '开始仿真',
        command: { simulationType: 'ising-model', action: 'start', description: '开始测试' }
      },
      {
        name: '设置温度',
        command: { 
          simulationType: 'ising-model', 
          action: 'setTemperature', 
          parameters: { temperature: 2.5 },
          description: '设置温度测试' 
        }
      },
      {
        name: '随机化',
        command: { simulationType: 'ising-model', action: 'randomize', description: '随机化测试' }
      },
      {
        name: '重置仿真',
        command: { simulationType: 'ising-model', action: 'reset', description: '重置测试' }
      }
    ];
    
    for (const test of tests) {
      try {
        console.log(`  ▶️ ${test.name}...`);
        const result = await simulationControlManager.executeCommand(test.command);
        console.log(`  ${result.success ? '✅' : '❌'} ${test.name}: ${result.message}`);
        if (!result.success) {
          console.error(`    错误详情:`, result.error);
        }
        await new Promise(resolve => setTimeout(resolve, 500));
      } catch (error) {
        console.error(`  ❌ ${test.name} 异常:`, error);
      }
    }
  };
  
  const showStatus = () => {
    console.log('\n📊 当前注册状态:');
    // @ts-ignore
    const registered = simulationControlManager.registeredControls;
    console.log('已注册的控制类型:', Array.from(registered.keys()));
    
    registered.forEach((controls, type) => {
      console.log(`  📌 ${type}: ${controls.type} (${Object.keys(controls.controls).length} 个控制方法)`);
    });
  };
  
  // 执行所有测试
  showStatus();
  await testGameOfLife();
  await testBoids();
  await testIsingModel();
  
  console.log('\n🎯 Function Calling测试完成！');
  console.log('请检查上述结果，确保所有测试都显示 ✅');
  console.log('如果有 ❌，请检查控制台中的详细错误信息');
};

// 将测试函数绑定到全局对象，便于在浏览器控制台调用
if (typeof window !== 'undefined') {
  (window as any).testAllSimulations = testAllSimulations;
  console.log('✨ Function Calling测试已加载！在控制台中运行 testAllSimulations() 开始测试');
}
