import React, { useRef, useImperativeHandle, forwardRef } from 'react';
import { Button } from '@/components/ui/button';
import { Slider } from '@/components/ui/slider';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Play, Pause, RefreshCw, SkipForward } from 'lucide-react';
import { SchellingParams } from '@/lib/schelling-segregation/core';

interface ControlPanelProps {
  isRunning: boolean;
  togglePlay: () => void;
  step: () => void;
  reset: () => void;
  params: SchellingParams;
  setParams: React.Dispatch<React.SetStateAction<SchellingParams>>;
  stats: {
    iteration: number;
    unhappyAgents: number;
    totalAgents: number;
    segregationIndex: number;
  };
  openAccordion: string | undefined;
  setOpenAccordion: React.Dispatch<React.SetStateAction<string | undefined>>;
}

export interface SchellingControlPanelRef {
  setGridSize: (size: number) => void;
  setSimilarityThreshold: (threshold: number) => void;
  setRatioTypeA: (ratio: number) => void;
  setEmptyCellsPct: (pct: number) => void;
  setNeighborhoodType: (type: string) => void;
  setBoundaryCondition: (condition: string) => void;
  clickTogglePlay: () => void;
  clickStep: () => void;
  clickReset: () => void;
}

const SchellingControlPanel = forwardRef<SchellingControlPanelRef, ControlPanelProps>(({
  isRunning,
  togglePlay,
  step,
  reset,
  params,
  setParams,
  stats,
  openAccordion,
  setOpenAccordion,
}, ref) => {
  const handleParamChange = (key: keyof SchellingParams, value: number | string) => {
    console.log(`[SchellingControlPanel] handleParamChange被调用: ${key} = ${value}`);
    setParams(prev => {
      const newParams = { ...prev, [key]: value };
      console.log(`[SchellingControlPanel] 参数更新:`, newParams);
      return newParams;
    });
  };

  // 暴露给外部调用的方法 - 这些方法会模拟UI控件的操作
  useImperativeHandle(ref, () => ({
    setGridSize: (size: number) => {
      const clampedSize = Math.max(20, Math.min(400, Math.round(size)));
      console.log(`[SchellingControlPanel] setGridSize被调用: ${size} → ${clampedSize}`);
      console.log(`[SchellingControlPanel] 调用前当前gridSize: ${params.gridSize}`);
      
      handleParamChange('gridSize', clampedSize);
      console.log(`[SchellingControlPanel] handleParamChange('gridSize', ${clampedSize}) 已调用`);
      
      // 添加微小延迟确保状态更新完成
      setTimeout(() => {
        console.log(`[SchellingControlPanel] 延迟确认，当前gridSize: ${params.gridSize}`);
      }, 10);
      
      return {
        success: true,
        previousValue: params.gridSize,
        requestedValue: size,
        actualValue: clampedSize
      };
    },
    setSimilarityThreshold: (threshold: number) => {
      const clampedThreshold = Math.max(0, Math.min(1, threshold));
      handleParamChange('similarityThreshold', clampedThreshold);
    },
    setRatioTypeA: (ratio: number) => {
      const clampedRatio = Math.max(0.1, Math.min(0.9, ratio));
      handleParamChange('ratioTypeA', clampedRatio);
    },
    setEmptyCellsPct: (pct: number) => {
      const clampedPct = Math.max(0.05, Math.min(0.3, pct));
      handleParamChange('emptyCellsPct', clampedPct);
    },
    setNeighborhoodType: (type: string) => {
      handleParamChange('neighborhoodType', type);
    },
    setBoundaryCondition: (condition: string) => {
      handleParamChange('boundaryCondition', condition);
    },
    clickTogglePlay: () => {
      togglePlay();
    },
    clickStep: () => {
      step();
    },
    clickReset: () => {
      reset();
    },
  }));

  return (
    <div className="w-full md:w-80 lg:w-96 p-4 bg-background border-l overflow-y-auto flex flex-col gap-4 text-sm scrollbar-thin scrollbar-track-transparent scrollbar-thumb-amber-600/20 hover:scrollbar-thumb-amber-600/40">
      <h2 className="text-xl font-bold text-amber-400">谢林隔离模型（Schelling Model）</h2>
      <p className="text-sm text-amber-400/70">
        展示个体基于相似性偏好而产生的空间隔离现象。即使个体只有轻微的偏好，也会导致显著的隔离。
      </p>

      <div className="grid grid-cols-3 gap-2">
        <Button onClick={togglePlay} variant="outline" size="sm" className="border-amber-500 text-amber-400 hover:bg-amber-500/20">
          {isRunning ? <Pause /> : <Play />}
          <span>{isRunning ? '暂停' : '开始'}</span>
        </Button>
        <Button onClick={step} variant="outline" size="sm" className="border-amber-500 text-amber-400 hover:bg-amber-500/20">
          <SkipForward />
          <span>单步</span>
        </Button>
        <Button onClick={reset} variant="destructive" size="sm" className="bg-amber-600 hover:bg-amber-700">
          <RefreshCw />
          <span>重置</span>
        </Button>
      </div>

      <div className="bg-amber-950/50 p-3 rounded-lg border border-amber-500/30">
        <h3 className="font-semibold text-amber-300 mb-2">模拟统计</h3>
        <div className="grid grid-cols-2 gap-2 text-xs">
          <div className="text-amber-200">
            <span className="text-amber-200/70">迭代:</span> {stats.iteration}
          </div>
          <div className="text-amber-200">
            <span className="text-amber-200/70">不满意:</span> {stats.unhappyAgents}
          </div>
          <div className="text-amber-200">
            <span className="text-amber-200/70">总数:</span> {stats.totalAgents}
          </div>
          <div className="text-amber-200">
            <span className="text-amber-200/70">隔离指数:</span> {stats.segregationIndex.toFixed(3)}
          </div>
        </div>
      </div>

      <div className="space-y-4">
        <h3 className="font-semibold text-amber-300">参数设置</h3>
        
        <div className="space-y-2">
          <Label className="text-amber-200">晶格数量: {params.gridSize}x{params.gridSize}</Label>
          <Slider 
            min={20} 
            max={400} 
            step={5} 
            value={[params.gridSize]} 
            onValueChange={([v]) => handleParamChange('gridSize', v)}
            className="[&>span:first-child]:bg-secondary [&>span>span]:bg-amber-500 [&>span:last-child]:border-amber-500 [&>span:last-child]:bg-background"
          />
        </div>

        <div className="space-y-2">
          <Label className="text-amber-200">相似性阈值: {(params.similarityThreshold * 100).toFixed(0)}%</Label>
          <Slider 
            min={0} 
            max={1} 
            step={0.01} 
            value={[params.similarityThreshold]} 
            onValueChange={([v]) => handleParamChange('similarityThreshold', v)}
            className="[&>span:first-child]:bg-secondary [&>span>span]:bg-amber-500 [&>span:last-child]:border-amber-500 [&>span:last-child]:bg-background"
          />
        </div>

        <div className="space-y-2">
          <Label className="text-amber-200">A类比例: {(params.ratioTypeA * 100).toFixed(0)}%</Label>
          <Slider 
            min={0.1} 
            max={0.9} 
            step={0.05} 
            value={[params.ratioTypeA]} 
            onValueChange={([v]) => handleParamChange('ratioTypeA', v)}
            className="[&>span:first-child]:bg-secondary [&>span>span]:bg-amber-500 [&>span:last-child]:border-amber-500 [&>span:last-child]:bg-background"
          />
        </div>

        <div className="space-y-2">
          <Label className="text-amber-200">空位比例: {(params.emptyCellsPct * 100).toFixed(0)}%</Label>
          <Slider 
            min={0.05} 
            max={0.3} 
            step={0.01} 
            value={[params.emptyCellsPct]} 
            onValueChange={([v]) => handleParamChange('emptyCellsPct', v)}
            className="[&>span:first-child]:bg-secondary [&>span>span]:bg-amber-500 [&>span:last-child]:border-amber-500 [&>span:last-child]:bg-background"
          />
        </div>

        <div className="space-y-2">
          <Label className="text-amber-200">邻域类型</Label>
          <Select onValueChange={(v) => handleParamChange('neighborhoodType', v)} value={params.neighborhoodType}>
            <SelectTrigger className="border-amber-500/50 text-amber-200 focus:border-amber-400">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="Moore">Moore (8邻域)</SelectItem>
              <SelectItem value="VonNeumann">Von Neumann (4邻域)</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-2">
          <Label className="text-amber-200">边界条件</Label>
          <Select onValueChange={(v) => handleParamChange('boundaryCondition', v)} value={params.boundaryCondition}>
            <SelectTrigger className="border-amber-500/50 text-amber-200 focus:border-amber-400">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="Periodic">周期性边界</SelectItem>
              <SelectItem value="Cut-off">固定边界</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>
    </div>
  );
});

SchellingControlPanel.displayName = 'SchellingControlPanel';

export default SchellingControlPanel;