import React, { useState, useEffect, useRef, useCallback } from 'react';
import useSchellingModel from '@/hooks/useSchellingModel';
import SchellingCanvas from './SchellingCanvas';
import SchellingControlPanel, { SchellingControlPanelRef } from './SchellingControlPanel';
import SchellingRulesExplanation from './SchellingRulesExplanation';
import { SchellingParams } from '@/lib/schelling-segregation/core';
import { AITutorButton } from '@/components/ai-tutor';
import { useAITutorActions } from '@/components/ai-tutor/AITutorProvider';
import { useAISimulationControl } from '@/hooks/useAISimulationControl';
import type { SchellingControls } from '@/types/simulation-controls';

const initialParams: SchellingParams = {
  gridSize: 50,
  similarityThreshold: 0.65,
  ratioTypeA: 0.5,
  emptyCellsPct: 0.1,
  neighborhoodType: 'Moore',
  boundaryCondition: 'Periodic',
};

interface SchellingSegregationPageProps {
  isActive: boolean;
}

const SchellingSegregationPage: React.FC<SchellingSegregationPageProps> = ({ isActive }) => {
  const [params, setParams] = useState<SchellingParams>(initialParams);
  const { grid, stats, isRunning, togglePlay, step, reset } = useSchellingModel(params, isActive);
  const controlPanelRef = useRef<SchellingControlPanelRef>(null);

  const [openAccordion, setOpenAccordion] = useState<string | undefined>();

  // AI导师相关hooks
  const { setSimulationContext } = useAITutorActions();
  const { registerSimulationControls, unregisterSimulationControls } = useAISimulationControl();

  // AI控制接口注册 - 上下文相关函数与控制函数分离
  
  // 更新模拟上下文的useEffect，只关注上下文更新
  useEffect(() => {
    setSimulationContext({
      type: 'schelling',
      isActive: isActive,
      currentParams: params
    });
  }, [setSimulationContext, isActive, params]);
  
  // 构建始终获取最新状态的控制函数
  const buildControls = useCallback(() => {
    // 始终获取最新状态的函数，避免闭包陷阱
    const getLatestState = () => ({
      isActive,
      isRunning,
      params,
      stats,
      controlPanel: controlPanelRef.current
    });
    
    return {
      // 兼容性：保持旧接口 - 但修改为始终获取最新状态
      togglePlay: () => {
        const { isActive, controlPanel } = getLatestState();
        if (isActive && controlPanel) {
          controlPanel.clickTogglePlay();
          return true;
        }
        return false;
      },
      
      step: () => {
        const { isActive, controlPanel } = getLatestState();
        if (isActive && controlPanel) {
          controlPanel.clickStep();
          return true;
        }
        return false;
      },
      
      reset: () => {
        const { isActive, controlPanel } = getLatestState();
        if (isActive && controlPanel) {
          controlPanel.clickReset();
          return true;
        }
        return false;
      },
      
      setSpeed: (speed: number) => {
        // Schelling模型没有直接的速度参数
        console.log("[Schelling模型] 不支持速度调整");
        return false;
      },
      
      setSimilarityThreshold: (threshold: number) => {
        const { isActive, controlPanel } = getLatestState();
        if (isActive && controlPanel) {
          controlPanel.setSimilarityThreshold(threshold);
          return true;
        }
        return false;
      },
      
      setDensity: (density: number) => {
        const { isActive, controlPanel } = getLatestState();
        if (isActive && controlPanel) {
          // 将密度转换为空细胞百分比
          const emptyCellsPct = 1 - density;
          controlPanel.setEmptyCellsPct(emptyCellsPct);
          return true;
        }
        return false;
      },
      
      setGroupRatio: (ratio: number) => {
        const { isActive, controlPanel } = getLatestState();
        if (isActive && controlPanel) {
          controlPanel.setRatioTypeA(ratio);
          return true;
        }
        return false;
      },
      
      getState: () => {
        const { isRunning, params, stats } = getLatestState();
        return {
          isRunning,
          speed: 1, // 固定值，因为Schelling模型没有速度概念
          similarityThreshold: params.similarityThreshold,
          density: 1 - params.emptyCellsPct,
          groupRatio: params.ratioTypeA,
          segregationIndex: stats.segregationIndex,
          satisfiedAgents: stats.happyAgentPct
        };
      },

      // 新增：文档要求的接口 - 使用buildControls中的getLatestState来避免闭包问题
      startSimulation: (currentState?: any) => {
        const { isActive, isRunning, controlPanel } = getLatestState();
        
        console.log('[Schelling模型控制] startSimulation被调用');
        console.log(`[Schelling模型控制] 当前状态: isActive=${isActive}, isRunning=${isRunning}`);
        
        // 检查页面是否活跃
        if (!isActive) {
          console.error('[Schelling模型控制] 页面不活跃，无法启动仿真');
          return {
            success: false,
            currentState: {
              simulation_is_running: false,
              simulation_is_paused: true
            },
            message: '页面不活跃，请先切换到Schelling模型页面再启动仿真'
          };
        }
        
        // 检查控制面板引用是否存在
        if (!controlPanel) {
          console.error('[Schelling模型控制] 控制面板引用不存在');
          return {
            success: false,
            currentState: {
              simulation_is_running: false,
              simulation_is_paused: true
            },
            message: '控制面板未初始化，请稍后再试'
          };
        }
        
        const wasRunning = isRunning;
        if (!wasRunning) {
          console.log('[Schelling模型控制] 调用控制面板的clickTogglePlay方法');
          controlPanel.clickTogglePlay();
          
          // 添加短暂延迟以确保状态更新
          setTimeout(() => {
            // 再次获取最新状态验证
            const updatedState = getLatestState();
            console.log(`[Schelling模型控制] 延迟验证: isRunning=${updatedState.isRunning}`);
          }, 100);
        }
        
        return {
          success: true,
          currentState: {
            simulation_is_running: !wasRunning,
            simulation_is_paused: wasRunning
          }
        };
      },
      
      pauseSimulation: (currentState?: any) => {
        const { isActive, isRunning, controlPanel } = getLatestState();
        
        console.log('[Schelling模型控制] pauseSimulation被调用');
        console.log(`[Schelling模型控制] 当前状态: isActive=${isActive}, isRunning=${isRunning}`);
        
        // 检查页面是否活跃
        if (!isActive) {
          console.error('[Schelling模型控制] 页面不活跃，无法暂停仿真');
          return {
            success: false,
            currentState: {
              simulation_is_running: isRunning,
              simulation_is_paused: !isRunning
            },
            message: '页面不活跃，请先切换到Schelling模型页面再操作'
          };
        }
        
        // 检查控制面板引用是否存在
        if (!controlPanel) {
          console.error('[Schelling模型控制] 控制面板引用不存在');
          return {
            success: false,
            currentState: {
              simulation_is_running: isRunning,
              simulation_is_paused: !isRunning
            },
            message: '控制面板未初始化，请稍后再试'
          };
        }
        
        const wasRunning = isRunning;
        if (wasRunning) {
          console.log('[Schelling模型控制] 调用控制面板的clickTogglePlay方法');
          controlPanel.clickTogglePlay();
          
          // 添加短暂延迟以确保状态更新
          setTimeout(() => {
            // 再次获取最新状态验证
            const updatedState = getLatestState();
            console.log(`[Schelling模型控制] 延迟验证: isRunning=${updatedState.isRunning}`);
          }, 100);
        }
        
        return {
          success: true,
          currentState: {
            simulation_is_running: wasRunning ? false : isRunning,
            simulation_is_paused: wasRunning ? true : !isRunning
          }
        };
      },
      
      stepwiseSimulation: () => {
        const { isActive, isRunning, stats, controlPanel } = getLatestState();
        
        console.log('[Schelling模型控制] stepwiseSimulation被调用');
        console.log(`[Schelling模型控制] 当前状态: isActive=${isActive}, isRunning=${isRunning}`);
        
        // 检查页面是否活跃
        if (!isActive) {
          console.error('[Schelling模型控制] 页面不活跃，无法执行单步仿真');
          return {
            success: false,
            currentState: {
              simulation_is_running: isRunning,
              segregationIndex: stats.segregationIndex,
              satisfiedAgents: stats.happyAgentPct
            },
            message: '页面不活跃，请先切换到Schelling模型页面再操作'
          };
        }
        
        // 检查控制面板引用是否存在
        if (!controlPanel) {
          console.error('[Schelling模型控制] 控制面板引用不存在');
          return {
            success: false,
            currentState: {
              simulation_is_running: isRunning,
              segregationIndex: stats.segregationIndex,
              satisfiedAgents: stats.happyAgentPct
            },
            message: '控制面板未初始化，请稍后再试'
          };
        }
        
        if (!isRunning) {
          controlPanel.clickStep();
          
          // 添加短暂延迟以确保状态更新
          setTimeout(() => {
            // 再次获取最新状态验证
            const updatedState = getLatestState();
            console.log(`[Schelling模型控制] 单步执行后，隔离指数: ${updatedState.stats.segregationIndex.toFixed(3)}`);
          }, 100);
          
          return {
            success: true,
            currentState: {
              simulation_is_running: isRunning,
              segregationIndex: stats.segregationIndex,
              satisfiedAgents: stats.happyAgentPct
            }
          };
        } else {
          return {
            success: false,
            currentState: {
              simulation_is_running: isRunning,
              segregationIndex: stats.segregationIndex,
              satisfiedAgents: stats.happyAgentPct
            },
            message: '请先暂停模拟再进行单步执行'
          };
        }
      },
      
      resetSimulation: () => {
        const { isActive, isRunning, stats, controlPanel } = getLatestState();
        
        console.log('[Schelling模型控制] resetSimulation被调用');
        console.log(`[Schelling模型控制] 当前状态: isActive=${isActive}, isRunning=${isRunning}`);
        
        // 检查页面是否活跃
        if (!isActive) {
          console.error('[Schelling模型控制] 页面不活跃，无法重置仿真');
          return {
            success: false,
            currentState: {
              simulation_is_running: isRunning,
              segregationIndex: stats.segregationIndex,
              satisfiedAgents: stats.happyAgentPct
            },
            message: '页面不活跃，请先切换到Schelling模型页面再操作'
          };
        }
        
        // 检查控制面板引用是否存在
        if (!controlPanel) {
          console.error('[Schelling模型控制] 控制面板引用不存在');
          return {
            success: false,
            currentState: {
              simulation_is_running: isRunning,
              segregationIndex: stats.segregationIndex,
              satisfiedAgents: stats.happyAgentPct
            },
            message: '控制面板未初始化，请稍后再试'
          };
        }
        
        try {
          controlPanel.clickReset();
          
          // 添加短暂延迟以确保状态更新
          setTimeout(() => {
            // 再次获取最新状态验证
            const updatedState = getLatestState();
            console.log(`[Schelling模型控制] 重置后状态: isRunning=${updatedState.isRunning}, 隔离指数=${updatedState.stats.segregationIndex}`);
          }, 100);
          
          return {
            success: true,
            currentState: {
              simulation_is_running: false,
              segregationIndex: 0,
              satisfiedAgents: 0
            }
          };
        } catch (error) {
          console.error('Reset simulation failed:', error);
          return {
            success: false,
            currentState: {
              simulation_is_running: isRunning,
              segregationIndex: stats.segregationIndex,
              satisfiedAgents: stats.happyAgentPct
            },
            message: '重置失败'
          };
        }
      },
      
      // 参数设置类 - 返回滑杆实际值，通过UI控件操作
      setSimilarityThresholdValue: (threshold: number) => {
        const { controlPanel, params } = getLatestState();
        
        console.log(`[Schelling模型控制] setSimilarityThresholdValue被调用: ${threshold}`);
        const clampedThreshold = Math.max(0.1, Math.min(1.0, threshold));
        
        if (controlPanel) {
          console.log(`[Schelling模型控制] 调用前当前similarityThreshold: ${params.similarityThreshold}`);
          controlPanel.setSimilarityThreshold(clampedThreshold);
          
          // 添加延迟确保状态更新完成
          setTimeout(() => {
            const updatedState = getLatestState();
            console.log(`[Schelling模型控制] 延迟验证，当前similarityThreshold: ${updatedState.params.similarityThreshold}`);
            console.log(`[Schelling模型控制] 状态更新${updatedState.params.similarityThreshold === clampedThreshold ? '成功' : '失败'}`);
          }, 100);
        }
        
        return {
          success: !!controlPanel,
          actualValue: clampedThreshold
        };
      },
      
      setPopulationDensity: (density: number) => {
        const { controlPanel, params } = getLatestState();
        
        console.log(`[Schelling模型控制] setPopulationDensity被调用: ${density}`);
        const clampedDensity = Math.max(0.1, Math.min(0.9, density));
        // 将密度转换为空细胞百分比
        const emptyCellsPct = 1 - clampedDensity;
        
        if (controlPanel) {
          console.log(`[Schelling模型控制] 调用前当前emptyCellsPct: ${params.emptyCellsPct}`);
          controlPanel.setEmptyCellsPct(emptyCellsPct);
          
          // 添加延迟确保状态更新完成
          setTimeout(() => {
            const updatedState = getLatestState();
            console.log(`[Schelling模型控制] 延迟验证，当前emptyCellsPct: ${updatedState.params.emptyCellsPct}`);
            console.log(`[Schelling模型控制] 状态更新${Math.abs(updatedState.params.emptyCellsPct - emptyCellsPct) < 0.001 ? '成功' : '失败'}`);
          }, 100);
        }
        
        return {
          success: !!controlPanel,
          actualValue: clampedDensity
        };
      },
      
      setGroupRatioValue: (ratio: number) => {
        const { controlPanel, params } = getLatestState();
        
        console.log(`[Schelling模型控制] setGroupRatioValue被调用: ${ratio}`);
        const clampedRatio = Math.max(0.1, Math.min(0.9, ratio));
        
        if (controlPanel) {
          console.log(`[Schelling模型控制] 调用前当前ratioTypeA: ${params.ratioTypeA}`);
          controlPanel.setRatioTypeA(clampedRatio);
          
          // 添加延迟确保状态更新完成
          setTimeout(() => {
            const updatedState = getLatestState();
            console.log(`[Schelling模型控制] 延迟验证，当前ratioTypeA: ${updatedState.params.ratioTypeA}`);
            console.log(`[Schelling模型控制] 状态更新${Math.abs(updatedState.params.ratioTypeA - clampedRatio) < 0.001 ? '成功' : '失败'}`);
          }, 100);
        }
        
        return {
          success: !!controlPanel,
          actualValue: clampedRatio
        };
      },
      
      setGridSize: (size: number) => {
        const { controlPanel, params } = getLatestState();
        
        console.log(`[Schelling模型控制] setGridSize被调用: ${size}`);
        const clampedSize = Math.max(20, Math.min(400, Math.round(size)));
        console.log(`[Schelling模型控制] 限制后的尺寸: ${clampedSize}`);
        
        console.log(`[Schelling模型控制] 调用前当前params.gridSize: ${params.gridSize}`);
        
        // 调用控制面板的setGridSize方法
        if (controlPanel?.setGridSize) {
          const result = controlPanel.setGridSize(clampedSize);
          console.log(`[Schelling模型控制] 控制面板返回结果:`, result);
          
          // 添加延迟确保状态更新完成
          setTimeout(() => {
            const updatedState = getLatestState();
            console.log(`[Schelling模型控制] 延迟验证，当前params.gridSize: ${updatedState.params.gridSize}`);
            console.log(`[Schelling模型控制] 状态更新${updatedState.params.gridSize === clampedSize ? '成功' : '失败'}`);
          }, 100);
          
          return {
            success: true,
            previousValue: params.gridSize,
            requestedValue: size,
            actualValue: clampedSize,
            message: `网格大小从 ${params.gridSize} 调整为 ${clampedSize}`
          };
        } else {
          console.error(`[Schelling模型控制] controlPanel.setGridSize 不存在`);
          return {
            success: false,
            actualValue: params.gridSize,
            message: 'setGridSize方法不存在'
          };
        }
      },
      
      setSimulationSpeed: (speed: number) => {
        console.log(`[Schelling模型控制] setSimulationSpeed被调用: ${speed}`);
        // Schelling模型没有速度概念，返回固定值
        return {
          success: true,
          actualValue: 1
        };
      },
      
      // 状态查询类 - 返回实时参数取值
      getCurrentState: () => {
        const { isRunning, params, stats } = getLatestState();
        
        return {
          isRunning: isRunning,
          step_count: stats.round,
          similarityThreshold: params.similarityThreshold,
          populationDensity: 1 - params.emptyCellsPct,
          groupRatio: params.ratioTypeA,
          segregationIndex: stats.segregationIndex,
          satisfiedAgents: stats.happyAgentPct,
          gridSize: params.gridSize
        };
      }
    };
  }, [controlPanelRef]); // 只依赖于稳定的ref

  // 在单独的useEffect中注册控制函数，避免频繁重建
  useEffect(() => {
    console.log('[Schelling模型控制] 注册控制函数');
    const controls = buildControls();
    
    registerSimulationControls('schelling', {
      type: 'schelling',
      controls
    });

    // 清理函数
    return () => {
      console.log('[Schelling模型控制] 注销控制函数');
      unregisterSimulationControls('schelling');
    };
  }, [buildControls, registerSimulationControls, unregisterSimulationControls]);

  const handleReset = () => {
    reset();
    // 不重置用户设置的参数，只重置模拟状态
    // setParams(initialParams); // 移除这行，保持用户设置的参数
  };

  const handleFullReset = () => {
    reset();
    setParams(initialParams);
  };

  // Convert SchellingStats to the expected format
  const controlPanelStats = {
    iteration: stats.round,
    unhappyAgents: Math.round((1 - stats.happyAgentPct) * 100), // Convert to count approximation
    totalAgents: Math.round(params.gridSize * params.gridSize * (1 - params.emptyCellsPct)),
    segregationIndex: stats.segregationIndex,
  };

  return (
    <div className="flex flex-col md:flex-row h-[calc(100vh-150px)] w-full bg-background relative">
      <AITutorButton position="top-left" />
      <div className="flex-grow h-full w-full md:w-auto relative border-2 border-amber-500/50 rounded-lg overflow-hidden">
        <SchellingCanvas
          grid={grid}
          agentAColor="#f59e0b" // amber-500
          agentBColor="#0ea5e9" // sky-500
          emptyCellColor="#374151" // gray-700
          latticeCount={params.gridSize}
        />
        <SchellingRulesExplanation />
      </div>
      <SchellingControlPanel
        ref={controlPanelRef}
        isRunning={isRunning}
        togglePlay={togglePlay}
        step={step}
        reset={handleReset}
        params={params}
        setParams={setParams}
        stats={controlPanelStats}
        openAccordion={openAccordion}
        setOpenAccordion={setOpenAccordion}
      />
    </div>
  );
};

export default SchellingSegregationPage;
