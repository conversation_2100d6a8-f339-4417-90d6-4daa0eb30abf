
import React, { useRef, useImperativeHandle, forwardRef } from 'react';
import { Button } from '@/components/ui/button';
import { Slider } from '@/components/ui/slider';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Switch } from '@/components/ui/switch';
import { Play, Pause, RefreshCw } from 'lucide-react';
import { IsingParams } from '@/lib/ising-model/core';

interface ControlPanelProps {
  isRunning: boolean;
  togglePlay: () => void;
  reset: () => void;
  params: IsingParams;
  setParams: React.Dispatch<React.SetStateAction<IsingParams>>;
  initialState: 'Random' | 'Ordered';
  setInitialState: (value: 'Random' | 'Ordered') => void;
  clearPlots: () => void;
  stepsPerFrame: number;
  setStepsPerFrame: (value: number) => void;
  spinUpColor: string;
  setSpinUpColor: (color: string) => void;
  spinDownColor: string;
  setSpinDownColor: (color: string) => void;
  thermalizationSteps: number;
  setThermalizationSteps: (value: number) => void;
  samplingSteps: number;
  setSamplingSteps: (value: number) => void;
  openAccordion: string | undefined;
  setOpenAccordion: React.Dispatch<React.SetStateAction<string | undefined>>;
}

export interface IsingModelControlPanelRef {
  setTemperature: (temp: number) => void;
  setMagneticField: (field: number) => void;
  setGridSize: (size: number) => void;
  setSpinsUpColor: (color: string) => void;
  setSpinsDownColor: (color: string) => void;
  setStepsPerFrame: (steps: number) => void;
  setInitialState: (state: 'Random' | 'Ordered') => void;
  setThermalMotion: (enabled: boolean) => void;
  setThermalWeight: (weight: number) => void;
  setThermalizationSteps: (steps: number) => void;
  setSamplingSteps: (steps: number) => void;
  clickTogglePlay: () => void;
  clickReset: () => void;
  clickClearPlots: () => void;
}

const IsingModelControlPanel = forwardRef<IsingModelControlPanelRef, ControlPanelProps>(({
  isRunning,
  togglePlay,
  reset,
  params,
  setParams,
  initialState,
  setInitialState,
  clearPlots,
  stepsPerFrame,
  setStepsPerFrame,
  spinUpColor,
  setSpinUpColor,
  spinDownColor,
  setSpinDownColor,
  thermalizationSteps,
  setThermalizationSteps,
  samplingSteps,
  setSamplingSteps,
  openAccordion,
  setOpenAccordion,
}, ref) => {
  const handleParamChange = (key: keyof IsingParams, value: number) => {
    console.log(`[IsingModelControlPanel] handleParamChange: ${key} = ${value}`);
    setParams(prev => {
      const newParams = { ...prev, [key]: value };
      console.log(`[IsingModelControlPanel] 参数更新:`, newParams);
      return newParams;
    });
  };

  // 暴露给外部调用的方法 - 这些方法会模拟UI控件的操作
  useImperativeHandle(ref, () => ({
    setTemperature: (temp: number) => {
      const clampedTemp = Math.max(0.1, Math.min(5, temp));
      handleParamChange('temperature', clampedTemp);
    },
    setMagneticField: (field: number) => {
      const clampedField = Math.max(-2, Math.min(2, field));
      handleParamChange('externalField', clampedField);
    },
    setGridSize: (size: number) => {
      const clampedSize = Math.max(10, Math.min(500, Math.round(size)));
      console.log(`[IsingModelControlPanel] setGridSize被调用: ${size} → ${clampedSize}`);
      
      // 确保状态立即更新
      handleParamChange('gridSize', clampedSize);
      
      // 添加一个小延迟来确保React状态更新完成
      setTimeout(() => {
        console.log(`[IsingModelControlPanel] 状态更新完成后的params.gridSize:`, clampedSize);
      }, 100);
      
      console.log(`[IsingModelControlPanel] handleParamChange('gridSize', ${clampedSize}) 已调用`);
    },
    setSpinsUpColor: (color: string) => {
      setSpinUpColor(color);
    },
    setSpinsDownColor: (color: string) => {
      setSpinDownColor(color);
    },
    setStepsPerFrame: (steps: number) => {
      const clampedSteps = Math.max(1, Math.min(100, Math.round(steps)));
      setStepsPerFrame(clampedSteps);
    },
    setInitialState: (state: 'Random' | 'Ordered') => {
      setInitialState(state);
    },
    setThermalMotion: (enabled: boolean) => {
      handleParamChange('enableThermalMotion', enabled ? 1 : 0);
    },
    setThermalWeight: (weight: number) => {
      const clampedWeight = Math.max(0.1, Math.min(2.0, weight));
      handleParamChange('thermalWeight', clampedWeight);
    },
    setThermalizationSteps: (steps: number) => {
      const clampedSteps = Math.max(100, Math.min(100000, Math.round(steps)));
      setThermalizationSteps(clampedSteps);
    },
    setSamplingSteps: (steps: number) => {
      const clampedSteps = Math.max(100, Math.min(100000, Math.round(steps)));
      setSamplingSteps(clampedSteps);
    },
    clickTogglePlay: () => {
      togglePlay();
    },
    clickReset: () => {
      reset();
    },
    clickClearPlots: () => {
      clearPlots();
    },
  }));

  return (
    <div className="w-full p-4 bg-background border-l overflow-y-auto flex flex-col gap-4 text-sm scrollbar-thin scrollbar-track-transparent scrollbar-thumb-violet-600/20 hover:scrollbar-thumb-violet-600/40">
      <h2 className="text-xl font-bold text-violet-400">二维伊辛模型（Ising Model）</h2>
      <p className="text-sm text-violet-400/70">
        观察临界温度 (Tc ≈ 2.269) 附近的相变。更大的晶格和充分的采样是观察相变的关键。
      </p>
      
      <div className="grid grid-cols-2 gap-2">
        <Button onClick={togglePlay} variant="outline" size="sm" className="gap-1 border-violet-500 text-violet-400 hover:bg-violet-500/20">
          {isRunning ? <Pause size={16} /> : <Play size={16} />}
          <span>{isRunning ? '暂停' : '开始'}</span>
        </Button>
        <Button onClick={reset} variant="destructive" size="sm" className="gap-1 bg-violet-600 hover:bg-violet-700">
          <RefreshCw size={16} />
          <span>重置模拟</span>
        </Button>
      </div>

      <div className="space-y-3">
        <Label className="text-violet-200">初始状态</Label>
        <Select onValueChange={setInitialState} value={initialState}>
          <SelectTrigger className="border-violet-500/50 focus:border-violet-400">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="Random">随机</SelectItem>
            <SelectItem value="Ordered">有序</SelectItem>
          </SelectContent>
        </Select>
      </div>
      
      <div className="space-y-3">
        <Label htmlFor="gridSize" className="text-violet-200">晶格数量: {params.gridSize}×{params.gridSize}</Label>
        <Input 
          id="gridSize" 
          type="number" 
          value={params.gridSize} 
          onChange={(e) => setParams(p => ({ ...p, gridSize: Number(e.target.value) || 1 }))}
          title="建议使用100×100或更大的晶格观察相变"
          className="border-violet-500/50 focus:border-violet-400"
        />
        <p className="text-xs text-muted-foreground">
          建议使用100×100或更大的晶格观察相变
        </p>
      </div>

      <div className="space-y-3">
        <Label htmlFor="simulationSpeed" className="text-violet-200">模拟速度: {stepsPerFrame.toFixed(1)} MCS/帧</Label>
        <Slider 
          id="simulationSpeed" 
          min={1} 
          max={20} 
          step={1} 
          value={[stepsPerFrame]} 
          onValueChange={([v]) => setStepsPerFrame(v)}
          className="[&>span:first-child]:bg-secondary [&>span>span]:bg-violet-500 [&>span:last-child]:border-violet-500 [&>span:last-child]:bg-background"
        />
      </div>

      <div className="space-y-3">
        <Label htmlFor="temperature" className="text-violet-200">温度 (T): {params.temperature.toFixed(3)}</Label>
        <Slider 
          id="temperature" 
          min={1.5} 
          max={3.0} 
          step={0.01} 
          value={[params.temperature]} 
          onValueChange={([v]) => setParams(p => ({ ...p, temperature: v }))}
          className="[&>span:first-child]:bg-secondary [&>span>span]:bg-violet-500 [&>span:last-child]:border-violet-500 [&>span:last-child]:bg-background"
        />
        <p className="text-xs text-muted-foreground">
          临界温度 Tc ≈ 2.269，建议在2.0-2.5范围细致观察
        </p>
      </div>
      
      <div className="space-y-3">
        <Label htmlFor="externalField" className="text-violet-200">外部磁场 (H): {params.externalField.toFixed(3)}</Label>
        <Slider 
          id="externalField" 
          min={-0.5} 
          max={0.5} 
          step={0.01} 
          value={[params.externalField]} 
          onValueChange={([v]) => setParams(p => ({ ...p, externalField: v }))}
          className="[&>span:first-child]:bg-secondary [&>span>span]:bg-violet-500 [&>span:last-child]:border-violet-500 [&>span:last-child]:bg-background"
        />
      </div>

      <div className="space-y-3 border-t border-violet-500/30 pt-3">
        <div className="flex items-center justify-between">
          <Label htmlFor="thermalMotion" className="text-violet-200">热运动影响</Label>
          <Switch
            id="thermalMotion"
            checked={params.enableThermalMotion}
            onCheckedChange={(checked) => setParams(p => ({ ...p, enableThermalMotion: checked }))}
          />
        </div>
        <p className="text-xs text-muted-foreground">
          关闭热运动可以更清楚地观察相变过程
        </p>
      </div>

      {params.enableThermalMotion && (
        <div className="space-y-3">
          <Label htmlFor="thermalWeight" className="text-violet-200">热运动权重: {params.thermalWeight.toFixed(2)}</Label>
          <Slider 
            id="thermalWeight" 
            min={0.1} 
            max={2.0} 
            step={0.05} 
            value={[params.thermalWeight]} 
            onValueChange={([v]) => setParams(p => ({ ...p, thermalWeight: v }))}
            className="[&>span:first-child]:bg-secondary [&>span>span]:bg-violet-500 [&>span:last-child]:border-violet-500 [&>span:last-child]:bg-background"
          />
        </div>
      )}

      <div className="space-y-3 border-t border-violet-500/30 pt-3">
        <Label htmlFor="thermalizationSteps" className="text-violet-200">热化步数: {thermalizationSteps}</Label>
        <Input 
          id="thermalizationSteps" 
          type="number" 
          value={thermalizationSteps} 
          onChange={(e) => setThermalizationSteps(Number(e.target.value) || 1)}
          title="建议值：5000-50000，系统达到平衡前的步数，不计入统计"
          className="border-violet-500/50 focus:border-violet-400"
        />
        <p className="text-xs text-muted-foreground">
          系统达到平衡前的步数，不计入统计。建议：5000-50000
        </p>
      </div>

      <div className="space-y-3">
        <Label htmlFor="samplingSteps" className="text-violet-200">采样步数: {samplingSteps}</Label>
        <Input 
          id="samplingSteps" 
          type="number" 
          value={samplingSteps} 
          onChange={(e) => setSamplingSteps(Number(e.target.value) || 1)}
          title="建议值：10000-100000，用于统计分析的采样点数量"
          className="border-violet-500/50 focus:border-violet-400"
        />
        <p className="text-xs text-muted-foreground">
          用于统计分析的采样点数量。建议：10000-100000
        </p>
      </div>

      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="spinUpColor" className="text-violet-200">自旋向上 (↑) 颜色</Label>
          <Input 
            id="spinUpColor" 
            type="color" 
            value={spinUpColor} 
            onChange={e => setSpinUpColor(e.target.value)} 
            className="p-1 h-10 border-violet-500/50"
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="spinDownColor" className="text-violet-200">自旋向下 (↓) 颜色</Label>
          <Input 
            id="spinDownColor" 
            type="color" 
            value={spinDownColor} 
            onChange={e => setSpinDownColor(e.target.value)} 
            className="p-1 h-10 border-violet-500/50"
          />
        </div>
      </div>

      <Button onClick={clearPlots} variant="secondary" size="sm" className="gap-1 bg-violet-600/50 hover:bg-violet-600/70 text-violet-100">
        <RefreshCw size={16} />
        <span>清空图表</span>
      </Button>
    </div>
  );
});

export default IsingModelControlPanel;
