import React, { useState, useEffect, useRef, useCallback } from 'react';
import { createIsingGrid, metropolisStep, calculateTotalEnergy, calculateTotalMagnetization, calculateSusceptibility, initialParams, IsingParams } from '@/lib/ising-model/core';
import IsingModelCanvas from './IsingModelCanvas';
import IsingModelControlPanel, { IsingModelControlPanelRef } from './IsingModelControlPanel';
import IsingModelPlots, { PlotDataPoint } from './IsingModelPlots';
import IsingModelExplanation from './IsingModelExplanation';
import IsingModelMagnetizationPanel from './IsingModelMagnetizationPanel';
import { AITutorButton } from '@/components/ai-tutor';
import { useAITutorActions } from '@/components/ai-tutor/AITutorProvider';
import { useAISimulationControl } from '@/hooks/useAISimulationControl';
import type { IsingControls } from '@/types/simulation-controls';

interface IsingModelPageProps {
  isActive: boolean;
}

const IsingModelPage: React.FC<IsingModelPageProps> = ({ isActive }) => {
  const controlPanelRef = useRef<IsingModelControlPanelRef>(null);
  const [grid, setGrid] = useState(() => {
    try {
      return createIsingGrid(initialParams.gridSize, 'Random');
    } catch (error) {
      console.error('Failed to create initial Ising grid:', error);
      return createIsingGrid(10, 'Random'); // fallback to smaller grid
    }
  });
  const [params, setParamsInternal] = useState<IsingParams>(initialParams);
  
  // 包装setParams以添加调试日志
  const setParams = useCallback((updater: React.SetStateAction<IsingParams>) => {
    setParamsInternal(prevParams => {
      const newParams = typeof updater === 'function' ? updater(prevParams) : updater;
      console.log('[IsingModelPage] setParams 被调用:', {
        previous: prevParams,
        new: newParams,
        gridSizeChanged: prevParams.gridSize !== newParams.gridSize
      });
      return newParams;
    });
  }, []);
  const [isRunning, setIsRunning] = useState(false);
  const [initialState, setInitialState] = useState<'Random' | 'Ordered'>('Random');
  const [currentEnergy, setCurrentEnergy] = useState(0);
  const [currentMagnetization, setCurrentMagnetization] = useState(0);
  const [stepsPerFrame, setStepsPerFrame] = useState(5.0);
  const [spinUpColor, setSpinUpColor] = useState('#ff6b6b');
  const [spinDownColor, setSpinDownColor] = useState('#4ecdc4');
  const [plotData, setPlotData] = useState<PlotDataPoint[]>([]);
  const [stepCount, setStepCount] = useState(0);
  const [openAccordion, setOpenAccordion] = useState<string | undefined>(undefined);
  
  const [thermalizationSteps, setThermalizationSteps] = useState(1000);  // 减少热化步数以更快响应
  const [samplingSteps, setSamplingSteps] = useState(5000);  // 减少采样步数以提高响应速度
  const [currentSusceptibility, setCurrentSusceptibility] = useState(0);

  // 修改：为每个温度维护独立的磁化强度历史记录
  const [temperatureMagnetizationMap, setTemperatureMagnetizationMap] = useState<Map<string, number[]>>(new Map());
  const [temperatureSusceptibilityMap, setTemperatureSusceptibilityMap] = useState<Map<string, number[]>>(new Map());

  const runningRef = useRef(isRunning);
  const animationFrameRef = useRef<number>();
  const lastUpdateTimeRef = useRef(0);
  const paramsRef = useRef(params);
  
  runningRef.current = isRunning;
  paramsRef.current = params;

  // 初始化能量和磁化强度
  useEffect(() => {
    try {
      const energy = calculateTotalEnergy(grid, params);
      const magnetization = calculateTotalMagnetization(grid);
      setCurrentEnergy(energy);
      setCurrentMagnetization(magnetization);
    } catch (error) {
      console.error('Failed to calculate initial energy/magnetization:', error);
      setCurrentEnergy(0);
      setCurrentMagnetization(0);
    }
  }, [grid, params]);

  // 当页面变为非活跃状态时停止运行
  useEffect(() => {
    if (!isActive && animationFrameRef.current) {
      cancelAnimationFrame(animationFrameRef.current);
      setIsRunning(false);
    }
  }, [isActive]);

  // 监听参数变化，当网格尺寸变化时重新初始化网格
  useEffect(() => {
    console.log('[IsingModelPage] 参数变化检测:', params);
    const newGrid = createIsingGrid(params.gridSize, initialState);
    setGrid(newGrid);
    setCurrentEnergy(calculateTotalEnergy(newGrid, params));
    setCurrentMagnetization(calculateTotalMagnetization(newGrid));
    setStepCount(0);
    setCurrentSusceptibility(0);
    // 重置时清空所有温度相关的历史数据
    setTemperatureMagnetizationMap(new Map());
    setTemperatureSusceptibilityMap(new Map());
    setPlotData([]);
    console.log('[IsingModelPage] 网格已重新初始化，尺寸:', params.gridSize);
  }, [params.gridSize, initialState, params]); // 依赖于gridSize和initialState变化

  const runSimulation = useCallback((timestamp: number) => {
    if (!runningRef.current || !isActive) return;

    if (timestamp - lastUpdateTimeRef.current >= 30) {
      lastUpdateTimeRef.current = timestamp;
      
      setGrid(currentGrid => {
        const steps = Math.round(stepsPerFrame);
        let tempGrid = currentGrid;
        let tempEnergy = currentEnergy;
        let tempMagnetization = currentMagnetization;
        
        for (let i = 0; i < steps; i++) {
          const result = metropolisStep(tempGrid, paramsRef.current, tempEnergy, tempMagnetization);
          tempGrid = result.newGrid;
          tempEnergy = result.newEnergy;
          tempMagnetization = result.newMagnetization;
        }
        
        setCurrentEnergy(tempEnergy);
        setCurrentMagnetization(tempMagnetization);
        setStepCount(prev => {
          const newStepCount = prev + steps;
          
          // 只在热化完成后开始收集数据
          if (newStepCount > thermalizationSteps) {
            const currentTemp = paramsRef.current.temperature;
            const tempKey = currentTemp.toFixed(3);
            
            // 为当前温度收集磁化强度数据 - 注意：磁化率计算需要原始值，不能取绝对值！
            setTemperatureMagnetizationMap(prevMap => {
              const newMap = new Map(prevMap);
              const existingMagnetizations = newMap.get(tempKey) || [];
              const updatedMagnetizations = [...existingMagnetizations, tempMagnetization].slice(-samplingSteps);
              newMap.set(tempKey, updatedMagnetizations);
              
              // 每50步计算一次磁化率，提高响应速度
              if (updatedMagnetizations.length > 50 && newStepCount % 50 === 0) {
                const susceptibility = calculateSusceptibility(updatedMagnetizations, paramsRef.current.temperature, paramsRef.current.gridSize);
                setCurrentSusceptibility(susceptibility);
                
                // 使用滑动平均更新磁化率数据
                setTemperatureSusceptibilityMap(prevSusMap => {
                  const newSusMap = new Map(prevSusMap);
                  const existingValues = newSusMap.get(tempKey) || [];
                  const updatedValues = [...existingValues, susceptibility].slice(-20); // 保留最近20个值用于滑动平均
                  newSusMap.set(tempKey, updatedValues);
                  
                  // 计算滑动平均并更新绘图数据
                  const averageSusceptibility = updatedValues.reduce((sum, val) => sum + val, 0) / updatedValues.length;
                  
                  setPlotData(prevData => {
                    // 检查是否已存在该温度点的数据
                    const existingIndex = prevData.findIndex(point => Math.abs(point.T - currentTemp) < 0.001);
                    
                    if (existingIndex >= 0) {
                      // 更新现有温度点的磁化率值
                      const newData = [...prevData];
                      newData[existingIndex] = {
                        T: currentTemp,
                        chi: averageSusceptibility,
                        step: newStepCount
                      };
                      return newData;
                    } else {
                      // 添加新的温度点
                      return [...prevData, {
                        T: currentTemp,
                        chi: averageSusceptibility,
                        step: newStepCount
                      }].slice(-200); // 保留最近200个不同温度点
                    }
                  });
                  
                  return newSusMap;
                });
              }
              
              return newMap;
            });
          }
          
          return newStepCount;
        });
        
        return tempGrid;
      });
    }
    
    if (isActive) {
      animationFrameRef.current = requestAnimationFrame(runSimulation);
    }
  }, [isActive, stepsPerFrame, currentEnergy, currentMagnetization, thermalizationSteps, samplingSteps]);

  const togglePlay = useCallback(() => {
    if (!isActive) return;
    
    setIsRunning(!isRunning);
    if (!isRunning) {
      lastUpdateTimeRef.current = performance.now();
      animationFrameRef.current = requestAnimationFrame(runSimulation);
    } else {
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
      }
    }
  }, [isRunning, runSimulation, isActive]);

  const reset = useCallback(() => {
    setIsRunning(false);
    if (animationFrameRef.current) {
      cancelAnimationFrame(animationFrameRef.current);
    }
    // 不重置用户设置的参数，只重置模拟状态
    const newGrid = createIsingGrid(params.gridSize, initialState);
    setGrid(newGrid);
    setCurrentEnergy(calculateTotalEnergy(newGrid, params));
    setCurrentMagnetization(calculateTotalMagnetization(newGrid));
    setStepCount(0);
    setCurrentSusceptibility(0);
    // 重置时清空所有温度相关的历史数据
    setTemperatureMagnetizationMap(new Map());
    setTemperatureSusceptibilityMap(new Map());
    setPlotData([]);
    console.log('[IsingModelPage] 模拟已重置，保持用户设置的参数');
  }, [params.gridSize, initialState, params]);

  const handleFlipSpin = useCallback((x: number, y: number) => {
    // 移除运行状态和活跃状态检查，允许随时交互
    
    setGrid(currentGrid => {
      if (!currentGrid[y] || !Array.isArray(currentGrid[y]) || currentGrid[y][x] === undefined) {
        return currentGrid;
      }
      
      const newGrid = currentGrid.map(row => [...row]);
      newGrid[y][x] *= -1;
      const newEnergy = calculateTotalEnergy(newGrid, params);
      const newMagnetization = calculateTotalMagnetization(newGrid);
      setCurrentEnergy(newEnergy);
      setCurrentMagnetization(newMagnetization);
      return newGrid;
    });
  }, [params]);

  const clearPlots = useCallback(() => {
    setPlotData([]);
    setCurrentSusceptibility(0);
    // 清空所有温度相关的历史数据
    setTemperatureMagnetizationMap(new Map());
    setTemperatureSusceptibilityMap(new Map());
  }, []);

  // 监控 params.gridSize 的变化
  useEffect(() => {
    console.log(`[IsingModelPage] params.gridSize 变化监控: ${params.gridSize}`);
  }, [params.gridSize]);

  // 当参数变化时重新计算能量和磁化强度
  useEffect(() => {
    setCurrentEnergy(calculateTotalEnergy(grid, params));
    setCurrentMagnetization(calculateTotalMagnetization(grid));
  }, [params, grid]);

  // 当网格大小变化时重置网格
  useEffect(() => {
    console.log(`[IsingModelPage] 网格大小变化: ${params.gridSize}, 初始状态: ${initialState}`);
    const newGrid = createIsingGrid(params.gridSize, initialState);
    setGrid(newGrid);
    setCurrentEnergy(calculateTotalEnergy(newGrid, params));
    setCurrentMagnetization(calculateTotalMagnetization(newGrid));
    setStepCount(0);
    setCurrentSusceptibility(0);
    console.log(`[IsingModelPage] 网格已重新创建: ${params.gridSize}x${params.gridSize}`);
  }, [params.gridSize, initialState]);

  // 计算标准化磁化强度
  const normalizedMagnetization = currentMagnetization / (params.gridSize * params.gridSize);
  const isThermalized = stepCount > thermalizationSteps;

  // AI导师相关hooks
  const { setSimulationContext } = useAITutorActions();
  const { registerSimulationControls, unregisterSimulationControls } = useAISimulationControl();

  // AI控制接口注册
  useEffect(() => {
    // 设置模拟上下文
    setSimulationContext({
      type: 'ising-model',
      isActive: isActive,
      currentParams: params
    });

    // 注册控制接口 - 通过ref操作UI控件
    const controls: IsingControls = {
      // 基础控制类 - 通过ref调用控制面板的方法
      startSimulation: (currentState?: any) => {
        console.log('[Ising控制] startSimulation被调用');
        controlPanelRef.current?.clickTogglePlay();
        return {
          success: true,
          currentState: {
            simulation_is_running: true,
            simulation_is_paused: false
          }
        };
      },
      
      pauseSimulation: (currentState?: any) => {
        console.log('[Ising控制] pauseSimulation被调用');
        if (isRunning) {
          controlPanelRef.current?.clickTogglePlay();
        }
        return {
          success: true,
          currentState: {
            simulation_is_running: false,
            simulation_is_paused: true
          }
        };
      },
      
      stepwiseSimulation: () => {
        console.log('[Ising控制] stepwiseSimulation被调用');
        if (isRunning) {
          controlPanelRef.current?.clickTogglePlay();
        }
        // 执行一步模拟
        try {
          const result = metropolisStep(grid, params, currentEnergy, currentMagnetization);
          setGrid(result.newGrid);
          setCurrentEnergy(result.newEnergy);
          setCurrentMagnetization(result.newMagnetization);
          setStepCount(prev => prev + 1);
          return {
            success: true,
            currentState: {
              simulation_is_running: false,
              simulation_is_paused: true,
              step_count: stepCount + 1,
              magnetization: result.newMagnetization,
              energy: result.newEnergy
            }
          };
        } catch (error) {
          console.error('Step simulation failed:', error);
          return {
            success: false,
            currentState: {},
            message: '单步执行失败'
          };
        }
      },
      
      resetSimulation: () => {
        console.log('[Ising控制] resetSimulation被调用');
        controlPanelRef.current?.clickReset();
        return {
          success: true,
          currentState: {
            simulation_is_running: false,
            simulation_is_paused: true
          }
        };
      },
      
      // 参数设置类 - 通过ref调用控制面板的方法
      setTemperatureValue: (temperature: number) => {
        console.log(`[Ising控制] setTemperatureValue被调用: ${temperature}`);
        try {
          const clampedTemp = Math.max(0.1, Math.min(5, temperature));
          if (controlPanelRef.current?.setTemperature) {
            controlPanelRef.current.setTemperature(clampedTemp);
            console.log(`[Ising控制] setTemperature调用成功`);
            return {
              success: true,
              actualValue: clampedTemp
            };
          } else {
            console.error('[Ising控制] controlPanelRef.current.setTemperature 不存在');
            return {
              success: false,
              actualValue: params.temperature,
              message: 'setTemperature方法不存在'
            };
          }
        } catch (error) {
          console.error('[Ising控制] setTemperature调用失败:', error);
          return {
            success: false,
            actualValue: params.temperature,
            message: `setTemperature调用失败: ${error}`
          };
        }
      },
      
      setExternalMagneticField: (field: number) => {
        console.log(`[Ising控制] setExternalMagneticField被调用: ${field}`);
        try {
          const clampedField = Math.max(-2, Math.min(2, field));
          if (controlPanelRef.current?.setMagneticField) {
            controlPanelRef.current.setMagneticField(clampedField);
            console.log(`[Ising控制] setMagneticField调用成功`);
            return {
              success: true,
              actualValue: clampedField
            };
          } else {
            console.error('[Ising控制] controlPanelRef.current.setMagneticField 不存在');
            return {
              success: false,
              actualValue: params.externalField,
              message: 'setMagneticField方法不存在'
            };
          }
        } catch (error) {
          console.error('[Ising控制] setMagneticField调用失败:', error);
          return {
            success: false,
            actualValue: params.externalField,
            message: `setMagneticField调用失败: ${error}`
          };
        }
      },
      
      setSimulationSpeed: (speed: number) => {
        console.log(`[Ising控制] setSimulationSpeed被调用: ${speed}`);
        try {
          const clampedSpeed = Math.max(1, Math.min(100, speed));
          if (controlPanelRef.current?.setStepsPerFrame) {
            controlPanelRef.current.setStepsPerFrame(clampedSpeed);
            console.log(`[Ising控制] setStepsPerFrame调用成功`);
            return {
              success: true,
              actualValue: clampedSpeed
            };
          } else {
            console.error('[Ising控制] controlPanelRef.current.setStepsPerFrame 不存在');
            return {
              success: false,
              actualValue: stepsPerFrame,
              message: 'setStepsPerFrame方法不存在'
            };
          }
        } catch (error) {
          console.error('[Ising控制] setStepsPerFrame调用失败:', error);
          return {
            success: false,
            actualValue: stepsPerFrame,
            message: `setStepsPerFrame调用失败: ${error}`
          };
        }
      },
      
      setGridSize: (size: number) => {
        console.log(`[Ising控制] setGridSize被调用: ${size}`);
        try {
          const clampedSize = Math.max(10, Math.min(500, Math.round(size)));
          console.log(`[Ising控制] 限制后的尺寸: ${clampedSize}`);
          
          // 直接调用控制面板的setGridSize方法
          if (controlPanelRef.current?.setGridSize) {
            controlPanelRef.current.setGridSize(clampedSize);
            console.log(`[Ising控制] setGridSize调用成功`);
            return {
              success: true,
              actualValue: clampedSize
            };
          } else {
            console.error('[Ising控制] controlPanelRef.current.setGridSize 不存在');
            return {
              success: false,
              actualValue: params.gridSize,
              message: 'setGridSize方法不存在'
            };
          }
        } catch (error) {
          console.error('[Ising控制] setGridSize调用失败:', error);
          return {
            success: false,
            actualValue: params.gridSize,
            message: `setGridSize调用失败: ${error}`
          };
        }
      },
      
      setThermalMotion: (enabled: boolean) => {
        console.log(`[Ising控制] setThermalMotion被调用: ${enabled}`);
        controlPanelRef.current?.setThermalMotion(enabled);
        return {
          success: true,
          actualValue: enabled
        };
      },
      
      setThermalWeight: (weight: number) => {
        console.log(`[Ising控制] setThermalWeight被调用: ${weight}`);
        controlPanelRef.current?.setThermalWeight(weight);
        const clampedWeight = Math.max(0.1, Math.min(2.0, weight));
        return {
          success: true,
          actualValue: clampedWeight
        };
      },
      
      setThermalizationSteps: (steps: number) => {
        console.log(`[Ising控制] setThermalizationSteps被调用: ${steps}`);
        controlPanelRef.current?.setThermalizationSteps(steps);
        const clampedSteps = Math.max(100, Math.min(100000, Math.round(steps)));
        return {
          success: true,
          actualValue: clampedSteps
        };
      },
      
      setSamplingSteps: (steps: number) => {
        console.log(`[Ising控制] setSamplingSteps被调用: ${steps}`);
        controlPanelRef.current?.setSamplingSteps(steps);
        const clampedSteps = Math.max(100, Math.min(100000, Math.round(steps)));
        return {
          success: true,
          actualValue: clampedSteps
        };
      },
      
      setSpinsUpColor: (color: string) => {
        console.log(`[Ising控制] setSpinsUpColor被调用: ${color}`);
        controlPanelRef.current?.setSpinsUpColor(color);
        return {
          success: true,
          actualValue: color
        };
      },
      
      setSpinsDownColor: (color: string) => {
        console.log(`[Ising控制] setSpinsDownColor被调用: ${color}`);
        controlPanelRef.current?.setSpinsDownColor(color);
        return {
          success: true,
          actualValue: color
        };
      },
      
      clearPlots: () => {
        console.log('[Ising控制] clearPlots被调用');
        controlPanelRef.current?.clickClearPlots();
        return {
          success: true,
          actualValue: true
        };
      },
      
      randomizeSpins: () => {
        console.log('[Ising控制] randomizeSpins被调用');
        controlPanelRef.current?.setInitialState('Random');
        return {
          success: true,
          actualValue: 'Random'
        };
      },
      
      setAllSpinsUp: () => {
        console.log('[Ising控制] setAllSpinsUp被调用');
        controlPanelRef.current?.setInitialState('Ordered');
        return {
          success: true,
          actualValue: 'Ordered'
        };
      },
      
      setAllSpinsDown: () => {
        console.log('[Ising控制] setAllSpinsDown被调用');
        controlPanelRef.current?.setInitialState('Ordered');
        return {
          success: true,
          actualValue: 'Ordered'
        };
      },
      
      // 状态查询类
      getCurrentState: () => ({
        isRunning,
        temperature: params.temperature,
        externalField: params.externalField,
        gridSize: params.gridSize,
        energy: currentEnergy,
        magnetization: currentMagnetization,
        stepCount
      }),
      
      // 兼容性：保持旧接口
      togglePlay: () => {
        controlPanelRef.current?.clickTogglePlay();
      },
      step: () => {
        if (isRunning) {
          controlPanelRef.current?.clickTogglePlay();
        }
        // 执行一步模拟
        try {
          const result = metropolisStep(grid, params, currentEnergy, currentMagnetization);
          setGrid(result.newGrid);
          setCurrentEnergy(result.newEnergy);
          setCurrentMagnetization(result.newMagnetization);
          setStepCount(prev => prev + 1);
        } catch (error) {
          console.error('Step simulation failed:', error);
        }
      },
      reset: () => {
        controlPanelRef.current?.clickReset();
      },
      setTemperature: (temp: number) => {
        controlPanelRef.current?.setTemperature(temp);
      },
      setMagneticField: (field: number) => {
        controlPanelRef.current?.setMagneticField(field);
      },
      setSpeed: (speed: number) => {
        controlPanelRef.current?.setStepsPerFrame(speed);
      },
      randomize: () => {
        controlPanelRef.current?.setInitialState('Random');
      },
      setAllUp: () => {
        controlPanelRef.current?.setInitialState('Ordered');
      },
      setAllDown: () => {
        controlPanelRef.current?.setInitialState('Ordered');
      },
      getState: () => ({
        isRunning,
        temperature: params.temperature,
        magneticField: params.externalField,
        speed: stepsPerFrame,
        magnetization: currentMagnetization,
        energy: currentEnergy
      })
    };

    if (isActive) {
      registerSimulationControls('ising-model', {
        type: 'ising',
        controls: controls
      });
    }

    return () => {
      if (isActive) {
        unregisterSimulationControls('ising-model');
      }
    };
  }, [
    isActive, params, isRunning, grid, initialState, stepsPerFrame, currentMagnetization, currentEnergy, stepCount,
    setSimulationContext, registerSimulationControls, unregisterSimulationControls
  ]);

  return (
    <div className="flex flex-col lg:flex-row h-[calc(100vh-150px)] w-full bg-background relative">
      <AITutorButton position="top-left" />
      <div className="flex-grow h-full w-full lg:w-auto relative border-2 border-violet-500/50 rounded-lg overflow-hidden">
        <IsingModelExplanation />
        <IsingModelCanvas 
          grid={grid}
          onFlipSpin={handleFlipSpin}
          spinUpColor={spinUpColor}
          spinDownColor={spinDownColor}
        />
        <IsingModelMagnetizationPanel 
          magnetization={normalizedMagnetization} 
          susceptibility={currentSusceptibility} 
          stepCount={stepCount}
          isThermalized={isThermalized}
          temperature={params.temperature}
        />
        {/* 已删除底部横幅菜单 */}
        {!isActive && (
          <div className="absolute inset-0 bg-black/50 flex items-center justify-center">
            <p className="text-white text-lg">模拟已暂停</p>
          </div>
        )}
      </div>
      
      <div className="w-full lg:w-80 flex flex-col">
        <IsingModelControlPanel
          ref={controlPanelRef}
          isRunning={isRunning}
          togglePlay={togglePlay}
          reset={reset}
          params={params}
          setParams={setParams}
          initialState={initialState}
          setInitialState={setInitialState}
          clearPlots={clearPlots}
          stepsPerFrame={stepsPerFrame}
          setStepsPerFrame={setStepsPerFrame}
          spinUpColor={spinUpColor}
          setSpinUpColor={setSpinUpColor}
          spinDownColor={spinDownColor}
          setSpinDownColor={setSpinDownColor}
          thermalizationSteps={thermalizationSteps}
          setThermalizationSteps={setThermalizationSteps}
          samplingSteps={samplingSteps}
          setSamplingSteps={setSamplingSteps}
          openAccordion={openAccordion}
          setOpenAccordion={setOpenAccordion}
        />
        <div className="flex-grow overflow-y-auto p-4 scrollbar-thin scrollbar-track-transparent scrollbar-thumb-violet-600/20 hover:scrollbar-thumb-violet-600/40">
          <IsingModelPlots data={plotData} />
        </div>
      </div>
    </div>
  );
};

export default IsingModelPage;
