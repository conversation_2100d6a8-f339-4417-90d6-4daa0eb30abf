import React, { useRef, useEffect, useCallback } from 'react';
import { SpinGrid } from '@/lib/ising-model/core';

interface IsingModelCanvasProps {
  grid: SpinGrid;
  onFlipSpin?: (x: number, y: number) => void;
  spinUpColor: string;
  spinDownColor: string;
}

function getContrastingArrowColor(hexColor: string): string {
  if (!hexColor) return '#FFFFFF';
  const color = hexColor.startsWith('#') ? hexColor.substring(1) : hexColor;
  if (color.length !== 6) return '#FFFFFF'; // Return default for invalid hex
  const r = parseInt(color.substring(0, 2), 16);
  const g = parseInt(color.substring(2, 4), 16);
  const b = parseInt(color.substring(4, 6), 16);
  // Using YIQ formula to determine brightness
  const yiq = ((r * 299) + (g * 587) + (b * 114)) / 1000;
  return (yiq >= 128) ? '#000000' : '#FFFFFF';
}

const IsingModelCanvas: React.FC<IsingModelCanvasProps> = ({ 
  grid, 
  onFlipSpin, 
  spinUpColor, 
  spinDownColor 
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const latestProps = useRef({ grid, spinUpColor, spinDownColor });
  latestProps.current = { grid, spinUpColor, spinDownColor };

  const drawCanvas = useCallback(() => {
    const canvas = canvasRef.current;
    const container = containerRef.current;
    const { grid: currentGrid, spinUpColor: currentSpinUpColor, spinDownColor: currentSpinDownColor } = latestProps.current;
    
    if (!canvas || !container || !currentGrid || !Array.isArray(currentGrid) || currentGrid.length === 0) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // 获取容器的实际尺寸
    const rect = container.getBoundingClientRect();
    
    // 设置画布的物理像素尺寸
    const dpr = window.devicePixelRatio || 1;
    canvas.width = rect.width * dpr;
    canvas.height = rect.height * dpr;

    // 设置画布的 CSS 尺寸
    canvas.style.width = `${rect.width}px`;
    canvas.style.height = `${rect.height}px`;

    // 应用设备像素比缩放
    ctx.scale(dpr, dpr);

    const gridSize = currentGrid.length;
    
    // 使用容器宽度计算格点大小，确保填满画布
    const cellSize = Math.ceil(rect.width / gridSize);

    ctx.clearRect(0, 0, rect.width, rect.height);
    
    // 设置字体大小为格点大小的合适比例
    const fontSize = Math.max(8, cellSize * 0.5);
    ctx.font = `${fontSize}px sans-serif`;
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';

    // 绘制格点矩阵
    for (let y = 0; y < gridSize; y++) {
      for (let x = 0; x < gridSize; x++) {
        if (currentGrid[y] && Array.isArray(currentGrid[y]) && currentGrid[y][x] !== undefined) {
          const cellColor = currentGrid[y][x] === 1 ? currentSpinUpColor : currentSpinDownColor;
          ctx.fillStyle = cellColor;
          
          // 使用计算出的格点大小绘制
          ctx.fillRect(x * cellSize, y * cellSize, cellSize, cellSize);
          
          // 绘制箭头
          ctx.fillStyle = getContrastingArrowColor(cellColor);
          const arrow = currentGrid[y][x] === 1 ? '↑' : '↓';
          ctx.fillText(
            arrow, 
            x * cellSize + cellSize / 2, 
            y * cellSize + cellSize / 2
          );

          // 添加网格线
          ctx.strokeStyle = '#1a1a1a';
          ctx.lineWidth = 0.5;
          ctx.strokeRect(x * cellSize, y * cellSize, cellSize, cellSize);
        }
      }
    }
  }, []);

  // 监听容器尺寸变化
  useEffect(() => {
    const canvas = canvasRef.current;
    const container = containerRef.current;
    if (!canvas || !container) return;

    const resizeObserver = new ResizeObserver(() => {
      drawCanvas();
    });

    resizeObserver.observe(container);
    
    // 初始绘制
    requestAnimationFrame(drawCanvas);

    return () => resizeObserver.disconnect();
  }, [drawCanvas]);

  useEffect(() => {
    drawCanvas();
  }, [grid, spinUpColor, spinDownColor, drawCanvas]);

  const handleClick = (e: React.MouseEvent<HTMLCanvasElement>) => {
    if (!onFlipSpin || !canvasRef.current || !grid) return;
    const canvas = canvasRef.current;
    const rect = canvas.getBoundingClientRect();
    const gridSize = grid.length;
    if (gridSize === 0) return;
    const cellSize = canvas.width / gridSize;
    
    const x = Math.floor((e.clientX - rect.left) / cellSize);
    const y = Math.floor((e.clientY - rect.top) / cellSize);

    if (x >= 0 && x < gridSize && y >= 0 && y < gridSize) {
        onFlipSpin(x, y);
    }
  }

  return (
    <div ref={containerRef} className="w-full h-full flex items-center justify-center bg-black">
       <canvas
        ref={canvasRef}
        onClick={handleClick}
        className="cursor-pointer"
        style={{ imageRendering: 'pixelated' }}
      />
    </div>
  );
};

export default IsingModelCanvas;
