import React, { useState, useEffect, useRef, useCallback } from 'react';
import { AntColonyOptimization } from '@/lib/physarum/ant-colony';
import type { SimulationConfig, Vector, SimulationStats } from '@/lib/physarum/ant-colony';
import AntColonyControlPanel, { PhysarumControlPanelRef } from './PhysarumControlPanel';
import AntColonyRulesExplanation from './PhysarumRulesExplanation';
import AntColonyCanvas from './AntColonyCanvas';
import { AITutorButton } from '@/components/ai-tutor';
import { useAITutorActions } from '@/components/ai-tutor/AITutorProvider';
import { useAISimulationControl } from '@/hooks/useAISimulationControl';
import type { PhysarumControls } from '@/types/simulation-controls';

const initialConfig: SimulationConfig = {
  antCount: 200,
  speed: 1.8,
  steerStrength: 0.1,
  wanderStrength: 0.05,
  homingStrength: 0.01,
  pheromoneDepositRate: 2,
  evaporationRate: 0.98,
  sensorAngleDegrees: 35,
  sensorDistance: 20,
  foodSize: 5,
  obstacleDetectionRadius: 15,
  obstacleRepulsion: 0.4,
};

interface AntColonyPageProps {
  isActive: boolean;
}

const AntColonyPage: React.FC<AntColonyPageProps> = ({ isActive }) => {
  const controlPanelRef = useRef<PhysarumControlPanelRef>(null);
  const [config, setConfig] = useState<SimulationConfig>(initialConfig);
  const [isRunning, setIsRunning] = useState(false);
  const [isDrawing, setIsDrawing] = useState(false);
  const [openAccordion, setOpenAccordion] = useState<string | undefined>(undefined);
  const [stats, setStats] = useState<SimulationStats>({
    foodStats: {
      collected: 0,
      total: 0,
      remaining: 0,
      percentage: '0%',
      elapsedTime: 0,
      isCompleted: false,
      completionTime: null
    },
    antStats: {
      searching: 0,
      returning: 0,
      total: 0
    },
    nestPulseTime: 0
  });
  const simulationRef = useRef<AntColonyOptimization | null>(null);
  const currentWallRef = useRef<Vector[]>([]);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const animationRef = useRef<number | null>(null);

  // AI导师相关hooks
  const { setSimulationContext } = useAITutorActions();
  const { registerSimulationControls, unregisterSimulationControls } = useAISimulationControl();

  const initializeSimulation = useCallback(() => {
    if (!isActive || simulationRef.current) return;
    
    const simulation = new AntColonyOptimization(800, 600, config);
    simulationRef.current = simulation;
    
    // 添加初始食物源
    const baseDistance = 200;
    const centerX = 400;
    const centerY = 300;
    
    for (let i = 0; i < 4; i++) {
      const angle = (i * Math.PI / 2) + (Math.random() * 0.5 - 0.25);
      const distance = baseDistance + Math.random() * 50;
      simulation.addFood(centerX + Math.cos(angle) * distance, centerY + Math.sin(angle) * distance);
    }
  }, [isActive]);

  const resetSimulation = useCallback(() => {
    simulationRef.current = null;
    initializeSimulation();
    setIsRunning(false);
  }, [initializeSimulation]);

  // 增量更新配置
  const handleConfigUpdate = useCallback((newConfig: SimulationConfig) => {
    setConfig(newConfig);
    
    if (simulationRef.current) {
      simulationRef.current.config = newConfig;
      updateStats();
    }
  }, []);

  // 状态更新函数
  const updateStats = useCallback(() => {
    if (simulationRef.current) {
      setStats(simulationRef.current.getStatistics());
    }
  }, []);

  // 使用 requestAnimationFrame 持续更新状态
  useEffect(() => {
    let animationFrameId: number;
    
    const updateLoop = () => {
      updateStats();
      if (isRunning) {
        animationFrameId = requestAnimationFrame(updateLoop);
      }
    };

    if (isRunning) {
      animationFrameId = requestAnimationFrame(updateLoop);
    }

    return () => {
      if (animationFrameId) {
        cancelAnimationFrame(animationFrameId);
      }
    };
  }, [isRunning, updateStats]);

  const handleAddFood = useCallback((x: number, y: number) => {
    if (simulationRef.current && !isDrawing) {
      simulationRef.current.addFood(x, y);
      updateStats();
    }
  }, [isDrawing, updateStats]);

  const handleStartDrawing = useCallback((x: number, y: number) => {
    setIsDrawing(true);
    currentWallRef.current = [{ x, y }];
    if (simulationRef.current) {
      simulationRef.current.addObstacle(currentWallRef.current);
    }
  }, []);

  const handleContinueDrawing = useCallback((x: number, y: number) => {
    if (isDrawing) {
      currentWallRef.current.push({ x, y });
    }
  }, [isDrawing]);

  const handleStopDrawing = useCallback(() => {
    setIsDrawing(false);
    currentWallRef.current = [];
  }, []);

  const clearObstacles = useCallback(() => {
    if (simulationRef.current) {
      simulationRef.current.clearObstacles();
    }
  }, []);

  const toggleSimulation = useCallback(() => {
    if (!isRunning && !simulationRef.current) {
      initializeSimulation();
    }
    setIsRunning(!isRunning);
  }, [isRunning, initializeSimulation]);

  // AI控制接口注册
  useEffect(() => {
    // 设置模拟上下文
    setSimulationContext({
      type: 'physarum',
      isActive: isActive,
      currentParams: config
    });

    // 注册控制接口 - 通过ref操作UI控件
    const controls: PhysarumControls = {
      // 基础控制类 - 通过ref调用控制面板的方法
      startSimulation: (currentState?: any) => {
        console.log('[Physarum控制] startSimulation被调用');
        if (!isRunning) {
          controlPanelRef.current?.clickTogglePlay();
        }
        return {
          success: true,
          currentState: {
            simulation_is_running: true,
            simulation_is_paused: false
          }
        };
      },
      
      pauseSimulation: (currentState?: any) => {
        console.log('[Physarum控制] pauseSimulation被调用');
        if (isRunning) {
          controlPanelRef.current?.clickTogglePlay();
        }
        return {
          success: true,
          currentState: {
            simulation_is_running: false,
            simulation_is_paused: true
          }
        };
      },
      
      stepwiseSimulation: () => {
        console.log('[Physarum控制] stepwiseSimulation被调用');
        // Physarum仿真没有单步模式，这里暂停然后手动更新一次
        if (isRunning) {
          controlPanelRef.current?.clickTogglePlay();
        }
        return {
          success: true,
          currentState: {
            simulation_is_running: false,
            simulation_is_paused: true
          }
        };
      },
      
      resetSimulation: () => {
        console.log('[Physarum控制] resetSimulation被调用');
        controlPanelRef.current?.clickReset();
        return {
          success: true,
          currentState: {
            simulation_is_running: false,
            simulation_is_paused: true
          }
        };
      },
      
      // 参数设置类 - 通过ref调用控制面板的方法
      setAgentCount: (count: number) => {
        console.log(`[Physarum控制] setAgentCount被调用: ${count}`);
        controlPanelRef.current?.setAntCount(count);
        const clampedCount = Math.max(1, Math.min(1000, Math.round(count)));
        return {
          success: true,
          actualValue: clampedCount
        };
      },
      
      setSensorAngleValue: (angle: number) => {
        console.log(`[Physarum控制] setSensorAngleValue被调用: ${angle}`);
        controlPanelRef.current?.setSensorAngleDegrees(angle);
        const clampedAngle = Math.max(0, Math.min(180, angle));
        return {
          success: true,
          actualValue: clampedAngle
        };
      },
      
      setSensorDistanceValue: (distance: number) => {
        console.log(`[Physarum控制] setSensorDistanceValue被调用: ${distance}`);
        controlPanelRef.current?.setSensorDistance(distance);
        const clampedDistance = Math.max(1, Math.min(50, distance));
        return {
          success: true,
          actualValue: clampedDistance
        };
      },
      
      setAgentStepSize: (size: number) => {
        console.log(`[Physarum控制] setAgentStepSize被调用: ${size}`);
        controlPanelRef.current?.setSpeed(size);
        const clampedSize = Math.max(0.1, Math.min(5, size));
        return {
          success: true,
          actualValue: clampedSize
        };
      },
      
      setPheromoneDeposition: (deposition: number) => {
        console.log(`[Physarum控制] setPheromoneDeposition被调用: ${deposition}`);
        controlPanelRef.current?.setPheromoneDepositRate(deposition);
        const clampedDeposition = Math.max(0.1, Math.min(10, deposition));
        return {
          success: true,
          actualValue: clampedDeposition
        };
      },
      
      setPheromoneDecayRate: (rate: number) => {
        console.log(`[Physarum控制] setPheromoneDecayRate被调用: ${rate}`);
        controlPanelRef.current?.setEvaporationRate(rate);
        const clampedRate = Math.max(0.001, Math.min(1, rate));
        return {
          success: true,
          actualValue: clampedRate
        };
      },
      
      setSimulationSpeed: (speed: number) => {
        console.log(`[Physarum控制] setSimulationSpeed被调用: ${speed}`);
        controlPanelRef.current?.setSpeed(speed);
        const clampedSpeed = Math.max(0.1, Math.min(5, speed));
        return {
          success: true,
          actualValue: clampedSpeed
        };
      },
      
      // 状态查询类
      getCurrentState: () => ({
        isRunning,
        agentCount: config.antCount,
        sensorAngle: config.sensorAngleDegrees,
        sensorDistance: config.sensorDistance,
        stepSize: config.speed,
        pheromoneDeposition: config.pheromoneDepositRate,
        decayRate: config.evaporationRate
      }),
      
      // 兼容性：保持旧接口
      togglePlay: () => {
        controlPanelRef.current?.clickTogglePlay();
      },
      reset: () => {
        controlPanelRef.current?.clickReset();
      },
      setSpeed: (speed: number) => {
        controlPanelRef.current?.setSpeed(speed);
      },
      setNumAgents: (count: number) => {
        controlPanelRef.current?.setAntCount(count);
      },
      setSensorAngle: (angle: number) => {
        controlPanelRef.current?.setSensorAngleDegrees(angle);
      },
      setSensorDistance: (distance: number) => {
        controlPanelRef.current?.setSensorDistance(distance);
      },
      setRotationAngle: (angle: number) => {
        controlPanelRef.current?.setSteerStrength(angle / 100);
      },
      setStepSize: (size: number) => {
        controlPanelRef.current?.setSpeed(size);
      },
      setDepositionAmount: (amount: number) => {
        controlPanelRef.current?.setPheromoneDepositRate(amount);
      },
      setDecayRate: (rate: number) => {
        controlPanelRef.current?.setEvaporationRate(rate);
      },
      getState: () => ({
        isRunning,
        numAgents: config.antCount,
        speed: config.speed,
        sensorAngle: config.sensorAngleDegrees,
        sensorDistance: config.sensorDistance,
        rotationAngle: config.steerStrength * 100,
        stepSize: config.speed,
        depositionAmount: config.pheromoneDepositRate,
        decayRate: config.evaporationRate
      })
    };

    if (isActive) {
      registerSimulationControls('physarum', {
        type: 'physarum',
        controls
      });
    }

    return () => {
      if (isActive) {
        unregisterSimulationControls('physarum');
      }
    };
  }, [
    isActive, config, isRunning,
    setSimulationContext, registerSimulationControls, unregisterSimulationControls
  ]);

  useEffect(() => {
    if (isActive) {
      initializeSimulation();
    }
    return () => {
      simulationRef.current = null;
    };
  }, [initializeSimulation, isActive]);

  return (
    <div className="flex gap-2 h-[calc(100vh-150px)] relative">
      <AITutorButton position="top-left" />
      <div className="flex-1 relative overflow-hidden rounded-xl border-2 border-green-500/20">
        <AntColonyCanvas
          simulation={isRunning ? simulationRef.current : null}
          onAddFood={handleAddFood}
          onStartDrawing={handleStartDrawing}
          onContinueDrawing={handleContinueDrawing}
          onStopDrawing={handleStopDrawing}
          isDrawing={isDrawing}
        />
        <AntColonyRulesExplanation />
      </div>
      <div className="w-[380px] bg-background/50 backdrop-blur-sm overflow-hidden">
        <AntColonyControlPanel
          ref={controlPanelRef}
          config={config}
          setConfig={handleConfigUpdate}
          isRunning={isRunning}
          onPause={toggleSimulation}
          onReset={resetSimulation}
          onClearObstacles={clearObstacles}
          stats={stats}
          openAccordion={openAccordion}
          setOpenAccordion={setOpenAccordion}
        />
      </div>
    </div>
  );
};

export default AntColonyPage;
