import React from 'react';
import { simulationControlManager } from './ai-tutor/SimulationControlManager';
import { testAllSimulations } from './FunctionCallingTest';

const DebugPanel: React.FC = () => {
  const testGameOfLifeControl = async () => {
    console.log('=== 测试生命游戏控制 ===');
    
    // 测试开始
    const startResult = await simulationControlManager.executeCommand({
      simulationType: 'game-of-life',
      action: 'start',
      description: '测试开始'
    });
    console.log('开始结果:', startResult);
    
    // 等待1秒
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // 测试设置速度
    const setSpeedResult = await simulationControlManager.executeCommand({
      simulationType: 'game-of-life',
      action: 'setSpeed',
      parameters: { speed: 200 },
      description: '测试设置速度'
    });
    console.log('设置速度结果:', setSpeedResult);
    
    // 测试重置
    const resetResult = await simulationControlManager.executeCommand({
      simulationType: 'game-of-life',
      action: 'reset',
      description: '测试重置'
    });
    console.log('重置结果:', resetResult);
  };

  const testBoidsControl = async () => {
    console.log('=== 测试Boids控制 ===');
    
    // 测试设置分离权重
    const setSeparationResult = await simulationControlManager.executeCommand({
      simulationType: 'boids',
      action: 'setSeparation',
      parameters: { value: 2.0 },
      description: '测试设置分离权重'
    });
    console.log('设置分离权重结果:', setSeparationResult);
  };

  const showRegisteredControls = () => {
    console.log('=== 已注册的控制接口 ===');
    // @ts-ignore - 访问私有属性进行调试
    const registered = simulationControlManager.registeredControls;
    console.log('已注册的控制类型:', Array.from(registered.keys()));
    registered.forEach((controls, type) => {
      console.log(`${type}:`, controls);
    });
  };

  return (
    <div style={{
      position: 'fixed',
      top: '10px',
      right: '10px',
      background: 'rgba(0,0,0,0.8)',
      color: 'white',
      padding: '10px',
      borderRadius: '5px',
      zIndex: 9999,
      fontSize: '12px'
    }}>
      <h4>调试面板</h4>
      <button onClick={showRegisteredControls} style={{ margin: '2px', padding: '5px' }}>
        显示已注册控制
      </button>
      <br />
      <button onClick={testGameOfLifeControl} style={{ margin: '2px', padding: '5px' }}>
        测试生命游戏控制
      </button>
      <br />
      <button onClick={testBoidsControl} style={{ margin: '2px', padding: '5px' }}>
        测试Boids控制
      </button>
      <br />
      <button onClick={() => testAllSimulations()} style={{ margin: '2px', padding: '5px', backgroundColor: '#0066cc' }}>
        🧪 全面测试
      </button>
    </div>
  );
};

export default DebugPanel;
