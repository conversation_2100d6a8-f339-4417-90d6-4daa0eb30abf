
import React, { useRef, useImperativeHandle, forwardRef } from 'react';
import { Button } from '@/components/ui/button';
import { Slider } from '@/components/ui/slider';
import { Label } from '@/components/ui/label';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';
import { Play, Pause, RefreshCw } from 'lucide-react';
import { BoidsParams } from '@/lib/boids/core';

interface ControlPanelProps {
  isRunning: boolean;
  togglePlay: () => void;
  reset: () => void;
  params: Omit<BoidsParams, 'width' | 'height'>;
  setParams: (params: Partial<Omit<BoidsParams, 'width' | 'height'>>) => void;
  boidCount: number;
  openAccordion: string | undefined;
  setOpenAccordion: React.Dispatch<React.SetStateAction<string | undefined>>;
}

export interface BoidsControlPanelRef {
  setBoidCount: (count: number) => void;
  setMaxSpeed: (speed: number) => void;
  setMaxForce: (force: number) => void;
  setPerceptionRadius: (radius: number) => void;
  setSeparationRadius: (radius: number) => void;
  setSeparationWeight: (weight: number) => void;
  setAlignmentWeight: (weight: number) => void;
  setCohesionWeight: (weight: number) => void;
  clickTogglePlay: () => void;
  clickReset: () => void;
}

const BoidsControlPanel = forwardRef<BoidsControlPanelRef, ControlPanelProps>(({
  isRunning,
  togglePlay,
  reset,
  params,
  setParams,
  boidCount,
  openAccordion,
  setOpenAccordion,
}, ref) => {
  const handleParamChange = (key: keyof Omit<BoidsParams, 'width' | 'height'>, value: number) => {
    setParams({ [key]: value });
  };

  // 暴露给外部调用的方法 - 这些方法会模拟UI控件的操作
  useImperativeHandle(ref, () => ({
    setBoidCount: (count: number) => {
      const clampedCount = Math.max(1, Math.min(500, Math.round(count)));
      handleParamChange('boidCount', clampedCount);
    },
    setMaxSpeed: (speed: number) => {
      const clampedSpeed = Math.max(0.5, Math.min(10, speed));
      handleParamChange('maxSpeed', clampedSpeed);
    },
    setMaxForce: (force: number) => {
      const clampedForce = Math.max(0.01, Math.min(1, force));
      handleParamChange('maxForce', clampedForce);
    },
    setPerceptionRadius: (radius: number) => {
      const clampedRadius = Math.max(10, Math.min(200, radius));
      handleParamChange('perceptionRadius', clampedRadius);
    },
    setSeparationRadius: (radius: number) => {
      const clampedRadius = Math.max(5, Math.min(100, radius));
      handleParamChange('separationRadius', clampedRadius);
    },
    setSeparationWeight: (weight: number) => {
      const clampedWeight = Math.max(0.5, Math.min(3, weight));
      handleParamChange('separationWeight', clampedWeight);
    },
    setAlignmentWeight: (weight: number) => {
      const clampedWeight = Math.max(0.5, Math.min(3, weight));
      handleParamChange('alignmentWeight', clampedWeight);
    },
    setCohesionWeight: (weight: number) => {
      const clampedWeight = Math.max(0.5, Math.min(3, weight));
      handleParamChange('cohesionWeight', clampedWeight);
    },
    clickTogglePlay: () => {
      togglePlay();
    },
    clickReset: () => {
      reset();
    },
  }));

  return (
    <div className="w-full md:w-80 lg:w-96 p-4 bg-background border-l overflow-y-auto flex flex-col gap-4 text-sm scrollbar-thin scrollbar-track-transparent scrollbar-thumb-sky-600/20 hover:scrollbar-thumb-sky-600/40">
      <h2 className="text-xl font-bold text-sky-400">鸟群行为模拟（Boids Model）</h2>
      <p className="text-sm text-sky-400/70">
        基于三个简单规则的群体行为模拟：分离、队列和聚集。观察复杂群体行为的涌现。
      </p>

      <div className="grid grid-cols-2 gap-2">
        <Button onClick={togglePlay} variant="outline" size="sm" className="border-sky-500 text-sky-400 hover:bg-sky-500/20">
          {isRunning ? <Pause /> : <Play />}
          <span>{isRunning ? '暂停' : '开始'}</span>
        </Button>
        <Button onClick={reset} variant="destructive" size="sm" className="bg-sky-600 hover:bg-sky-700">
          <RefreshCw />
          <span>重置</span>
        </Button>
      </div>

      <div className="bg-sky-950/50 p-3 rounded-lg border border-sky-500/30">
        <h3 className="font-semibold text-sky-300 mb-2">模拟状态</h3>
        <div className="text-sky-200">
          <span className="text-sky-200/70">Boid数量:</span> {boidCount}
        </div>
      </div>

      <Accordion type="single" collapsible className="w-full" value={openAccordion} onValueChange={setOpenAccordion}>
        <AccordionItem value="params">
          <AccordionTrigger className="text-sky-300">参数设置</AccordionTrigger>
          <AccordionContent className="space-y-6 pt-4">
            
            <div className="space-y-3">
              <Label className="text-sky-200">Boid数量: {params.boidCount}</Label>
              <Slider 
                min={1} 
                max={200} 
                step={1} 
                value={[params.boidCount]} 
                onValueChange={([v]) => handleParamChange('boidCount', v)}
                className="[&>span:first-child]:bg-secondary [&>span>span]:bg-sky-500 [&>span:last-child]:border-sky-500 [&>span:last-child]:bg-background"
              />
            </div>

            <div className="space-y-3">
              <Label className="text-sky-200">最大速度: {params.maxSpeed.toFixed(1)}</Label>
              <Slider 
                min={0.5} 
                max={5} 
                step={0.1} 
                value={[params.maxSpeed]} 
                onValueChange={([v]) => handleParamChange('maxSpeed', v)}
                className="[&>span:first-child]:bg-secondary [&>span>span]:bg-sky-500 [&>span:last-child]:border-sky-500 [&>span:last-child]:bg-background"
              />
            </div>

            <div className="space-y-3">
              <Label className="text-sky-200">最大作用力: {params.maxForce.toFixed(2)}</Label>
              <Slider 
                min={0.01} 
                max={0.2} 
                step={0.01} 
                value={[params.maxForce]} 
                onValueChange={([v]) => handleParamChange('maxForce', v)}
                className="[&>span:first-child]:bg-secondary [&>span>span]:bg-sky-500 [&>span:last-child]:border-sky-500 [&>span:last-child]:bg-background"
              />
            </div>

            <div className="space-y-3">
              <Label className="text-sky-200">分离半径: {params.separationRadius.toFixed(0)}</Label>
              <Slider 
                min={10} 
                max={100} 
                step={5} 
                value={[params.separationRadius]} 
                onValueChange={([v]) => handleParamChange('separationRadius', v)}
                className="[&>span:first-child]:bg-secondary [&>span>span]:bg-sky-500 [&>span:last-child]:border-sky-500 [&>span:last-child]:bg-background"
              />
            </div>

            <div className="space-y-3">
              <Label className="text-sky-200">感知半径: {params.perceptionRadius.toFixed(0)}</Label>
              <Slider 
                min={20} 
                max={150} 
                step={10} 
                value={[params.perceptionRadius]} 
                onValueChange={([v]) => handleParamChange('perceptionRadius', v)}
                className="[&>span:first-child]:bg-secondary [&>span>span]:bg-sky-500 [&>span:last-child]:border-sky-500 [&>span:last-child]:bg-background"
              />
            </div>

            <div className="space-y-3">
              <Label className="text-sky-200">分离权重: {params.separationWeight.toFixed(1)}</Label>
              <Slider 
                min={0.5} 
                max={3} 
                step={0.1} 
                value={[params.separationWeight]} 
                onValueChange={([v]) => handleParamChange('separationWeight', v)}
                className="[&>span:first-child]:bg-secondary [&>span>span]:bg-sky-500 [&>span:last-child]:border-sky-500 [&>span:last-child]:bg-background"
              />
            </div>

            <div className="space-y-3">
              <Label className="text-sky-200">队列权重: {params.alignmentWeight.toFixed(1)}</Label>
              <Slider 
                min={0.5} 
                max={3} 
                step={0.1} 
                value={[params.alignmentWeight]} 
                onValueChange={([v]) => handleParamChange('alignmentWeight', v)}
                className="[&>span:first-child]:bg-secondary [&>span>span]:bg-sky-500 [&>span:last-child]:border-sky-500 [&>span:last-child]:bg-background"
              />
            </div>

            <div className="space-y-3">
              <Label className="text-sky-200">聚集权重: {params.cohesionWeight.toFixed(1)}</Label>
              <Slider 
                min={0.5} 
                max={3} 
                step={0.1} 
                value={[params.cohesionWeight]} 
                onValueChange={([v]) => handleParamChange('cohesionWeight', v)}
                className="[&>span:first-child]:bg-secondary [&>span>span]:bg-sky-500 [&>span:last-child]:border-sky-500 [&>span:last-child]:bg-background"
              />
            </div>

          </AccordionContent>
        </AccordionItem>
      </Accordion>
    </div>
  );
});

export default BoidsControlPanel;
