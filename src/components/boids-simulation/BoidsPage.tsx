import React, { useState, useCallback, useRef, useEffect } from 'react';
import { Boid, BoidsParams, initialBoidsParams, Vector } from '@/lib/boids/core';
import BoidsCanvas from './BoidsCanvas';
import BoidsControlPanel, { BoidsControlPanelRef } from './BoidsControlPanel';
import BoidsRulesExplanation from './BoidsRulesExplanation';
import { AITutorButton } from '@/components/ai-tutor';
import { useAITutorActions } from '@/components/ai-tutor/AITutorProvider';
import { useAISimulationControl } from '@/hooks/useAISimulationControl';
import type { BoidsControls } from '@/types/simulation-controls';

interface BoidsPageProps {
  isActive: boolean;
}

const BoidsPage: React.FC<BoidsPageProps> = ({ isActive }) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const controlPanelRef = useRef<BoidsControlPanelRef>(null);
  const [params, setParams] = useState<BoidsParams>({
      ...initialBoidsParams,
      width: 800,
      height: 600
  });
  const [flock, setFlock] = useState<Boid[]>([]);
  const [isRunning, setIsRunning] = useState(false); // 默认不运行
  const [openAccordion, setOpenAccordion] = useState<string | undefined>(undefined);

  // AI导师相关hooks
  const { setSimulationContext } = useAITutorActions();
  const { registerSimulationControls, unregisterSimulationControls } = useAISimulationControl();

  // 生成正态分布随机数的函数
  const generateNormalRandom = (mean: number, stdDev: number) => {
    let u = 0, v = 0;
    while(u === 0) u = Math.random();
    while(v === 0) v = Math.random();
    const z = Math.sqrt(-2.0 * Math.log(u)) * Math.cos(2.0 * Math.PI * v);
    return z * stdDev + mean;
  };

  const createFlock = useCallback((p: BoidsParams) => {
    const newFlock: Boid[] = [];
    for (let i = 0; i < p.boidCount; i++) {
        const boid = new Boid(Math.random() * p.width, Math.random() * p.height, p);
        // 给每个鸟设置随机的初始速度方向和大小
        const angle = Math.random() * 2 * Math.PI;
        const speed = Math.abs(generateNormalRandom(p.maxSpeed, p.maxSpeed * 0.3));
        boid.velocity = new Vector(Math.cos(angle) * speed, Math.sin(angle) * speed);
        newFlock.push(boid);
    }
    setFlock(newFlock);
  }, []);

  const reset = useCallback(() => {
    if(containerRef.current) {
        const { clientWidth, clientHeight } = containerRef.current;
        const newParams = { ...initialBoidsParams, width: clientWidth, height: clientHeight };
        setParams(newParams);
        createFlock(newParams);
        setIsRunning(false); // 重置时不自动运行
    }
  }, [createFlock]);

  useEffect(() => {
    const handleResize = () => {
      if (containerRef.current && isActive) {
        const { clientWidth, clientHeight } = containerRef.current;
        setParams(p => ({ ...p, width: clientWidth, height: clientHeight }));
      }
    };

    if (isActive) {
      reset(); // Initial setup
    }
    
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, [reset, isActive]);

  // AI控制接口注册
  useEffect(() => {
    // 设置模拟上下文
    setSimulationContext({
      type: 'boids',
      isActive: isActive,
      currentParams: params
    });

    // 注册控制接口 - 通过ref操作UI控件
    const controls: BoidsControls = {
      // 基础控制类 - 通过ref调用控制面板的方法
      startSimulation: (currentState?: any) => {
        console.log('[Boids控制] startSimulation被调用');
        controlPanelRef.current?.clickTogglePlay();
        return {
          success: true,
          currentState: {
            simulation_is_running: true,
            simulation_is_paused: false
          }
        };
      },
      
      pauseSimulation: (currentState?: any) => {
        console.log('[Boids控制] pauseSimulation被调用');
        if (isRunning) {
          controlPanelRef.current?.clickTogglePlay();
        }
        return {
          success: true,
          currentState: {
            simulation_is_running: false,
            simulation_is_paused: true
          }
        };
      },
      
      stepwiseSimulation: () => {
        console.log('[Boids控制] stepwiseSimulation被调用');
        // 对于Boids，单步运行意味着暂停一步，然后立即暂停
        if (isRunning) {
          controlPanelRef.current?.clickTogglePlay();
        }
        // 执行一帧更新
        const updatedFlock = flock.map(boid => {
          boid.flock(flock);
          boid.update();
          boid.edges();
          return boid;
        });
        setFlock([...updatedFlock]);
        
        return {
          success: true,
          currentState: {
            simulation_is_running: false,
            simulation_is_paused: true,
            boidCount: flock.length
          }
        };
      },
      
      resetSimulation: () => {
        console.log('[Boids控制] resetSimulation被调用');
        controlPanelRef.current?.clickReset();
        return {
          success: true,
          currentState: {
            simulation_is_running: false,
            simulation_is_paused: true,
            boidCount: params.boidCount
          }
        };
      },
      
      // 参数设置类 - 通过ref调用控制面板的方法
      setBoidCount: (count: number) => {
        console.log(`[Boids控制] setBoidCount被调用: ${count}`);
        controlPanelRef.current?.setBoidCount(count);
        const clampedCount = Math.max(1, Math.min(200, Math.round(count)));
        return {
          success: true,
          actualValue: clampedCount
        };
      },
      
      setSeparationWeight: (weight: number) => {
        console.log(`[Boids控制] setSeparationWeight被调用: ${weight}`);
        controlPanelRef.current?.setSeparationWeight(weight);
        const clampedWeight = Math.max(0.5, Math.min(3.0, weight));
        return {
          success: true,
          actualValue: clampedWeight
        };
      },
      
      setAlignmentWeight: (weight: number) => {
        console.log(`[Boids控制] setAlignmentWeight被调用: ${weight}`);
        controlPanelRef.current?.setAlignmentWeight(weight);
        const clampedWeight = Math.max(0.5, Math.min(3.0, weight));
        return {
          success: true,
          actualValue: clampedWeight
        };
      },
      
      setCohesionWeight: (weight: number) => {
        console.log(`[Boids控制] setCohesionWeight被调用: ${weight}`);
        controlPanelRef.current?.setCohesionWeight(weight);
        const clampedWeight = Math.max(0.5, Math.min(3.0, weight));
        return {
          success: true,
          actualValue: clampedWeight
        };
      },
      
      setMaxSpeed: (speed: number) => {
        console.log(`[Boids控制] setMaxSpeed被调用: ${speed}`);
        controlPanelRef.current?.setMaxSpeed(speed);
        const clampedSpeed = Math.max(0.5, Math.min(5.0, speed));
        return {
          success: true,
          actualValue: clampedSpeed
        };
      },
      
      setPerceptionRadius: (radius: number) => {
        console.log(`[Boids控制] setPerceptionRadius被调用: ${radius}`);
        controlPanelRef.current?.setPerceptionRadius(radius);
        const clampedRadius = Math.max(20, Math.min(150, radius));
        return {
          success: true,
          actualValue: clampedRadius
        };
      },
      
      setSeparationRadius: (radius: number) => {
        console.log(`[Boids控制] setSeparationRadius被调用: ${radius}`);
        controlPanelRef.current?.setSeparationRadius(radius);
        const clampedRadius = Math.max(5, Math.min(100, radius));
        return {
          success: true,
          actualValue: clampedRadius
        };
      },
      
      setMaxForce: (force: number) => {
        console.log(`[Boids控制] setMaxForce被调用: ${force}`);
        controlPanelRef.current?.setMaxForce(force);
        const clampedForce = Math.max(0.01, Math.min(1, force));
        return {
          success: true,
          actualValue: clampedForce
        };
      },
      
      // 状态查询类 - 返回实时参数取值
      getCurrentState: () => ({
        isRunning,
        boidCount: params.boidCount,
        separationWeight: params.separationWeight,
        alignmentWeight: params.alignmentWeight,
        cohesionWeight: params.cohesionWeight,
        maxSpeed: params.maxSpeed,
        perceptionRadius: params.perceptionRadius
      }),
      
      // 兼容性方法
      togglePlay: () => {
        controlPanelRef.current?.clickTogglePlay();
      },
      
      reset: () => {
        controlPanelRef.current?.clickReset();
      },
      
      getState: () => ({
        isRunning,
        boidCount: params.boidCount,
        separationWeight: params.separationWeight,
        alignmentWeight: params.alignmentWeight,
        cohesionWeight: params.cohesionWeight,
        maxSpeed: params.maxSpeed,
        perceptionRadius: params.perceptionRadius
      })
    };

    registerSimulationControls('boids', {
      type: 'boids',
      controls
    });

    // 清理函数
    return () => {
      unregisterSimulationControls('boids');
    };
  }, [
    isActive, params, isRunning, 
    setSimulationContext, registerSimulationControls, unregisterSimulationControls,
    reset, createFlock
  ]);

  const togglePlay = useCallback(() => setIsRunning(prev => !prev), []);
  
  const addBoid = useCallback((x: number, y: number) => {
    // 创建随机方向和速度的 Boid
    const angle = Math.random() * 2 * Math.PI;
    const speed = Math.abs(generateNormalRandom(params.maxSpeed, params.maxSpeed * 0.3));
    const vx = Math.cos(angle) * speed;
    const vy = Math.sin(angle) * speed;
    
    const boid = new Boid(x, y, params);
    boid.velocity = new Vector(vx, vy);
    
    setFlock(currentFlock => [...currentFlock, boid]);
  }, [params, generateNormalRandom]);

  const handleSetParams = useCallback((newControlParams: Partial<Omit<BoidsParams, 'width' | 'height'>>) => {
    setParams(currentParams => {
      const updatedParams = { ...currentParams, ...newControlParams };
      if (
        newControlParams.boidCount !== undefined &&
        newControlParams.boidCount !== currentParams.boidCount
      ) {
        createFlock(updatedParams);
      }
      return updatedParams;
    });
  }, [createFlock]);

  const { width, height, ...controlParams } = params;
  
  return (
    <div className="flex flex-col md:flex-row h-[calc(100vh-150px)] w-full bg-background relative">
      <AITutorButton position="top-left" />
      <div ref={containerRef} className="flex-grow h-full w-full md:w-auto relative border-2 border-cyan-300/30 rounded-lg overflow-hidden">
        <BoidsCanvas
          flock={flock}
          params={params}
          isRunning={isRunning && isActive}
          onCanvasClick={addBoid}
        />
        <BoidsRulesExplanation />
      </div>
      <BoidsControlPanel
        ref={controlPanelRef}
        isRunning={isRunning}
        togglePlay={togglePlay}
        reset={reset}
        params={controlParams}
        setParams={handleSetParams}
        boidCount={flock.length}
        openAccordion={openAccordion}
        setOpenAccordion={setOpenAccordion}
      />
    </div>
  );
};

export default BoidsPage;
