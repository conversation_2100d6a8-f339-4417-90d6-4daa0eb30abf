import { describe, it, expect } from 'vitest';
import { createGrid, computeNextGeneration } from '../lib/game-of-life/core';
import { createIsingGrid, calculateTotalEnergy, calculateTotalMagnetization } from '../lib/ising-model/core';
import { safeGridAccess, safeGridSet, isValidGridPosition, validateGrid, createSafeGrid } from '../lib/grid-safety';

describe('Grid Safety Tests', () => {
  describe('Game of Life Core', () => {
    it('should handle zero dimensions', () => {
      const grid = createGrid(0, 0);
      expect(grid).toHaveLength(1);
      expect(grid[0]).toHaveLength(1);
    });

    it('should handle negative dimensions', () => {
      const grid = createGrid(-5, -3);
      expect(grid).toHaveLength(1);
      expect(grid[0]).toHaveLength(1);
    });

    it('should handle undefined grid in computeNextGeneration', () => {
      const result = computeNextGeneration([], 10, 10, false, {
        survivalMin: 2,
        survivalMax: 3,
        birthMin: 3,
        birthMax: 3,
      });
      expect(result.nextGrid).toHaveLength(1);
      expect(result.liveCells).toBe(0);
    });
  });

  describe('Ising Model Core', () => {
    it('should handle zero grid size', () => {
      const grid = createIsingGrid(0, 'Random');
      expect(grid).toHaveLength(1);
      expect(grid[0]).toHaveLength(1);
    });

    it('should handle negative grid size', () => {
      const grid = createIsingGrid(-10, 'Random');
      expect(grid).toHaveLength(1);
      expect(grid[0]).toHaveLength(1);
    });

    it('should handle empty grid in energy calculation', () => {
      const energy = calculateTotalEnergy([], { gridSize: 10, temperature: 1, externalField: 0, thermalWeight: 1, enableThermalMotion: true });
      expect(energy).toBe(0);
    });

    it('should handle empty grid in magnetization calculation', () => {
      const magnetization = calculateTotalMagnetization([]);
      expect(magnetization).toBe(0);
    });
  });

  describe('Grid Safety Utils', () => {
    it('should handle undefined grid in safeGridAccess', () => {
      const result = safeGridAccess(undefined, 0, 0, 42);
      expect(result).toBe(42);
    });

    it('should handle null grid in safeGridAccess', () => {
      const result = safeGridAccess(null, 0, 0, 42);
      expect(result).toBe(42);
    });

    it('should handle empty grid in safeGridAccess', () => {
      const result = safeGridAccess([], 0, 0, 42);
      expect(result).toBe(42);
    });

    it('should handle out of bounds access', () => {
      const grid = [[1, 2], [3, 4]];
      const result = safeGridAccess(grid, 5, 5, 42);
      expect(result).toBe(42);
    });

    it('should handle negative indices', () => {
      const grid = [[1, 2], [3, 4]];
      const result = safeGridAccess(grid, -1, -1, 42);
      expect(result).toBe(42);
    });

    it('should return correct value for valid access', () => {
      const grid = [[1, 2], [3, 4]];
      const result = safeGridAccess(grid, 1, 1, 42);
      expect(result).toBe(4);
    });

    it('should validate grid correctly', () => {
      expect(validateGrid(undefined)).toBe(false);
      expect(validateGrid(null)).toBe(false);
      expect(validateGrid([])).toBe(false);
      expect(validateGrid([[]])).toBe(false);
      expect(validateGrid([[1, 2], [3, 4]])).toBe(true);
    });

    it('should create safe grid with minimum dimensions', () => {
      const grid = createSafeGrid(0, 0, 42);
      expect(grid).toHaveLength(1);
      expect(grid[0]).toHaveLength(1);
      expect(grid[0][0]).toBe(42);
    });
  });
});
