<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>指令分解器调试测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-result {
            background: #f8f9fa;
            padding: 15px;
            margin: 10px 0;
            border-left: 4px solid #007bff;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .success { border-left-color: #28a745; }
        .error { border-left-color: #dc3545; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <h1>指令分解器调试测试</h1>
    
    <div class="test-section">
        <h2>测试1: 函数名识别</h2>
        <button onclick="testFunctionNameRecognition()">运行测试</button>
        <div id="test1-result" class="test-result"></div>
    </div>
    
    <div class="test-section">
        <h2>测试2: 复杂指令分解</h2>
        <button onclick="testComplexInstructionDecomposition()">运行测试</button>
        <div id="test2-result" class="test-result"></div>
    </div>
    
    <div class="test-section">
        <h2>测试3: 实际场景模拟</h2>
        <button onclick="testRealScenario()">运行测试</button>
        <div id="test3-result" class="test-result"></div>
    </div>

    <script>
        // 模拟指令分解器的核心逻辑
        class MockInstructionDecomposer {
            isParameterSettingCall(functionName) {
                const parameterKeywords = [
                    'setGridSize', 'setSimilarityThreshold', 'setPopulationDensity', 
                    'setGroupRatio', 'setTemperature', 'setSpeed', 'setNumBoids',
                    'setSeparation', 'setAlignment', 'setCohesion', 'setMaxSpeed',
                    'setNumAgents', 'setSensorAngle', 'setSensorDistance', 'setRotationAngle',
                    
                    // 实际函数名中的关键词（下划线格式）
                    'set_grid_size', 'set_similarity_threshold', 'set_density', 'set_group_ratio',
                    'set_temperature', 'set_speed', 'set_num_boids', 'set_separation',
                    'set_alignment', 'set_cohesion', 'set_max_speed', 'set_num_agents',
                    'set_sensor_angle', 'set_sensor_distance', 'set_rotation_angle',
                    'set_magnetic_field'
                ];
                
                return parameterKeywords.some(keyword => functionName.includes(keyword));
            }
            
            isSimulationControlCall(functionName) {
                const controlKeywords = [
                    'startSimulation', 'pauseSimulation', 'stopSimulation', 
                    'runSimulation', 'play', 'pause', 'stop', 'step', 'reset',
                    
                    // 实际函数名中的关键词
                    'start', 'pause', 'stop', 'step', 'reset', 'randomize',
                    'toggle_play', 'play_pause'
                ];
                
                return controlKeywords.some(keyword => functionName.includes(keyword));
            }
            
            analyzeUserMessage(message, toolCalls) {
                console.log('[测试] 分析用户消息:', message);
                console.log('[测试] 函数调用数量:', toolCalls.length);
                console.log('[测试] 函数调用列表:', toolCalls.map(call => call.function.name));

                if (toolCalls.length <= 1) {
                    console.log('[测试] 只有一个函数调用，无需分解');
                    return { needsDecomposition: false };
                }

                // 检查是否包含参数设置和仿真控制的组合
                const parameterCalls = toolCalls.filter(call => 
                    this.isParameterSettingCall(call.function.name)
                );
                
                const controlCalls = toolCalls.filter(call =>
                    this.isSimulationControlCall(call.function.name)
                );

                console.log('[测试] 参数设置调用:', parameterCalls.map(call => call.function.name));
                console.log('[测试] 控制调用:', controlCalls.map(call => call.function.name));

                if (parameterCalls.length > 0 && controlCalls.length > 0) {
                    console.log('[测试] 检测到参数设置和仿真控制的组合，需要分解');
                    return {
                        needsDecomposition: true,
                        reason: '检测到参数设置和仿真控制的组合操作，需要分步执行'
                    };
                }

                // 检查是否有多个参数设置
                if (parameterCalls.length > 1) {
                    console.log('[测试] 检测到多个参数设置，需要分解');
                    return {
                        needsDecomposition: true,
                        reason: '检测到多个参数设置操作，建议分步执行以确保每个参数正确设置'
                    };
                }

                console.log('[测试] 无需分解');
                return { needsDecomposition: false };
            }
        }
        
        const decomposer = new MockInstructionDecomposer();
        
        function testFunctionNameRecognition() {
            const result = document.getElementById('test1-result');
            let output = '=== 函数名识别测试 ===\n\n';
            
            const testCases = [
                'schelling_set_grid_size',
                'schelling_start',
                'schelling_set_similarity_threshold',
                'schelling_pause',
                'ising_model_set_temperature',
                'game_of_life_start'
            ];
            
            testCases.forEach(functionName => {
                const isParameter = decomposer.isParameterSettingCall(functionName);
                const isControl = decomposer.isSimulationControlCall(functionName);
                
                output += `函数: ${functionName}\n`;
                output += `  参数设置: ${isParameter ? '✅' : '❌'}\n`;
                output += `  仿真控制: ${isControl ? '✅' : '❌'}\n\n`;
            });
            
            result.textContent = output;
            result.className = 'test-result success';
        }
        
        function testComplexInstructionDecomposition() {
            const result = document.getElementById('test2-result');
            let output = '=== 复杂指令分解测试 ===\n\n';
            
            const testCases = [
                {
                    message: "设置网格大小为150，然后运行仿真",
                    toolCalls: [
                        {
                            id: 'call_1',
                            type: 'function',
                            function: {
                                name: 'schelling_set_grid_size',
                                arguments: '{"size": 150}'
                            }
                        },
                        {
                            id: 'call_2',
                            type: 'function',
                            function: {
                                name: 'schelling_start',
                                arguments: '{}'
                            }
                        }
                    ]
                },
                {
                    message: "只设置网格大小为100",
                    toolCalls: [
                        {
                            id: 'call_1',
                            type: 'function',
                            function: {
                                name: 'schelling_set_grid_size',
                                arguments: '{"size": 100}'
                            }
                        }
                    ]
                }
            ];
            
            testCases.forEach((testCase, index) => {
                output += `测试案例 ${index + 1}:\n`;
                output += `消息: "${testCase.message}"\n`;
                
                const analysis = decomposer.analyzeUserMessage(testCase.message, testCase.toolCalls);
                
                output += `需要分解: ${analysis.needsDecomposition ? '✅ 是' : '❌ 否'}\n`;
                if (analysis.reason) {
                    output += `原因: ${analysis.reason}\n`;
                }
                output += '\n';
            });
            
            result.textContent = output;
            result.className = 'test-result success';
        }
        
        function testRealScenario() {
            const result = document.getElementById('test3-result');
            let output = '=== 实际场景模拟测试 ===\n\n';
            
            // 模拟您遇到的实际情况
            const realScenario = {
                message: "设置晶格数量为150，然后运行仿真",
                toolCalls: [
                    {
                        id: 'call_1',
                        type: 'function',
                        function: {
                            name: 'schelling_set_grid_size',
                            arguments: '{"size": 150}'
                        }
                    },
                    {
                        id: 'call_2',
                        type: 'function',
                        function: {
                            name: 'schelling_start',
                            arguments: '{}'
                        }
                    }
                ]
            };
            
            output += `用户消息: "${realScenario.message}"\n\n`;
            
            output += '函数调用详情:\n';
            realScenario.toolCalls.forEach((call, index) => {
                output += `  ${index + 1}. ${call.function.name}\n`;
                output += `     参数: ${call.function.arguments}\n`;
            });
            output += '\n';
            
            // 执行分析
            console.clear();
            const analysis = decomposer.analyzeUserMessage(realScenario.message, realScenario.toolCalls);
            
            output += '分析结果:\n';
            output += `  需要分解: ${analysis.needsDecomposition ? '✅ 是' : '❌ 否'}\n`;
            if (analysis.reason) {
                output += `  原因: ${analysis.reason}\n`;
            }
            
            output += '\n预期行为:\n';
            if (analysis.needsDecomposition) {
                output += '  1. 执行第一步: 设置网格大小为150\n';
                output += '  2. 等待用户确认\n';
                output += '  3. 用户确认后执行第二步: 启动仿真\n';
            } else {
                output += '  直接执行所有函数调用（这是当前的问题所在）\n';
            }
            
            result.textContent = output;
            result.className = analysis.needsDecomposition ? 'test-result success' : 'test-result error';
        }
    </script>
</body>
</html>
