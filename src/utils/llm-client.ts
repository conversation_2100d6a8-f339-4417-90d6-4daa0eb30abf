import type { 
  LLMConfig, 
  LLMResponse, 
  LLMStreamResponse, 
  ConnectionTestResult,
  FunctionDefinition,
  ToolCall
} from '@/types/ai-tutor';

/**
 * LLM API客户端
 * 支持多种LLM平台的统一接口
 */

interface APIMessage {
  role: 'system' | 'user' | 'assistant' | 'tool';
  content: string;
  tool_calls?: ToolCall[];
  tool_call_id?: string;
}

interface OpenAIResponse {
  choices: Array<{
    message: {
      content: string;
    };
    finish_reason: string;
  }>;
  usage?: {
    total_tokens: number;
  };
}

interface StreamChunk {
  choices: Array<{
    delta: {
      content?: string;
    };
    finish_reason?: string;
  }>;
}

/**
 * LLM客户端类
 */
export class LLMClient {
  private config: LLMConfig;
  private abortController?: AbortController;

  constructor(config: LLMConfig) {
    this.config = config;
  }

  /**
   * 更新配置
   */
  updateConfig(config: LLMConfig): void {
    this.config = config;
  }

  /**
   * 取消当前请求
   */
  abort() {
    if (this.abortController) {
      this.abortController.abort();
    }
  }

  /**
   * 发送消息并获取完整响应（支持Function Calling）
   */
  async sendMessage(
    messages: APIMessage[], 
    systemPrompt?: string,
    functions?: FunctionDefinition[]
  ): Promise<LLMResponse> {
    const startTime = Date.now();
    this.abortController = new AbortController();

    try {
      const response = await this.makeRequest(messages, systemPrompt, false, functions);
      const data = await response.json();
      
      const duration = Date.now() - startTime;
      
      if (!response.ok) {
        throw new Error(data.error?.message || `API请求失败: ${response.status}`);
      }

      const content = this.extractContent(data);
      const tokens = this.extractTokenCount(data);
      const toolCalls = this.extractToolCalls(data);

      return {
        content,
        tokens,
        duration,
        toolCalls
      };
    } catch (error) {
      if (error instanceof Error && error.name === 'AbortError') {
        throw new Error('请求已取消');
      }
      throw error;
    }
  }

  /**
   * 发送消息并获取流式响应
   */
  async sendStreamMessage(
    messages: APIMessage[],
    systemPrompt: string | undefined,
    onChunk: (chunk: LLMStreamResponse) => void,
    functions?: FunctionDefinition[]
  ): Promise<void> {
    const startTime = Date.now();
    this.abortController = new AbortController();

    try {
      const response = await this.makeRequest(messages, systemPrompt, true, functions);
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error?.message || `API请求失败: ${response.status}`);
      }

      const reader = response.body?.getReader();
      if (!reader) {
        throw new Error('无法获取响应流');
      }

      const decoder = new TextDecoder();
      let buffer = '';
      let fullContent = '';

      while (true) {
        const { done, value } = await reader.read();
        
        if (done) break;

        buffer += decoder.decode(value, { stream: true });
        const lines = buffer.split('\n');
        buffer = lines.pop() || '';

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            const data = line.slice(6);
            
            if (data === '[DONE]') {
              onChunk({
                content: fullContent,
                isComplete: true
              });
              return;
            }

            try {
              const parsed = JSON.parse(data);
              const content = this.extractStreamContent(parsed);
              
              if (content) {
                fullContent += content;
                onChunk({
                  content: fullContent,
                  isComplete: false
                });
              }
            } catch (e) {
              // 忽略JSON解析错误
            }
          }
        }
      }

      onChunk({
        content: fullContent,
        isComplete: true
      });
    } catch (error) {
      if (error instanceof Error && error.name === 'AbortError') {
        throw new Error('请求已取消');
      }
      throw error;
    }
  }

  /**
   * 测试连接
   */
  async testConnection(): Promise<ConnectionTestResult> {
    const startTime = Date.now();
    
    try {
      const testMessages: APIMessage[] = [
        { role: 'user', content: 'Hello' }
      ];

      const response = await this.makeRequest(testMessages, undefined, false);
      const latency = Date.now() - startTime;

      if (!response.ok) {
        const errorData = await response.json();
        return {
          success: false,
          error: errorData.error?.message || `连接失败: ${response.status}`
        };
      }

      const data = await response.json();
      
      return {
        success: true,
        latency,
        modelInfo: {
          name: this.config.model,
          capabilities: ['text-generation', 'conversation']
        }
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '连接测试失败'
      };
    }
  }

  /**
  /**
   * 构建请求
   */
  private async makeRequest(
    messages: APIMessage[],
    systemPrompt?: string,
    stream: boolean = false,
    functions?: FunctionDefinition[]
  ): Promise<Response> {
    const url = this.buildApiUrl();
    const headers = this.buildHeaders();
    const body = this.buildRequestBody(messages, systemPrompt, stream, functions);

    return fetch(url, {
      method: 'POST',
      headers,
      body: JSON.stringify(body),
      signal: this.abortController?.signal
    });
  }

  /**
   * 构建API URL
   */
  private buildApiUrl(): string {
    let endpoint = this.config.endpoint;
    
    // 确保endpoint以正确的路径结尾
    if (!endpoint.endsWith('/')) {
      endpoint += '/';
    }

    switch (this.config.provider) {
      case 'openai':
        return `${endpoint}chat/completions`;
      case 'claude':
        return `${endpoint}messages`;
      case 'xai':
        return `${endpoint}chat/completions`;
      case 'custom':
        return `${endpoint}chat/completions`; // 假设使用OpenAI兼容格式
      default:
        return `${endpoint}chat/completions`;
    }
  }

  /**
   * 构建请求头
   */
  private buildHeaders(): Record<string, string> {
    const baseHeaders: Record<string, string> = {
      'Content-Type': 'application/json'
    };

    switch (this.config.provider) {
      case 'openai':
      case 'xai':
      case 'custom':
        baseHeaders['Authorization'] = `Bearer ${this.config.apiKey}`;
        break;
      case 'claude':
        baseHeaders['x-api-key'] = this.config.apiKey;
        baseHeaders['anthropic-version'] = '2023-06-01';
        break;
    }

    return baseHeaders;
  }

  /**
   * 构建请求体
   */
  private buildRequestBody(
    messages: APIMessage[],
    systemPrompt?: string,
    stream: boolean = false,
    functions?: FunctionDefinition[]
  ): any {
    const allMessages = [...messages];

    // 添加系统提示词
    if (systemPrompt || this.config.systemPrompt) {
      const finalSystemPrompt = systemPrompt || this.config.systemPrompt;
      allMessages.unshift({
        role: 'system',
        content: finalSystemPrompt
      });
    }

    const baseBody: any = {
      model: this.config.model,
      messages: allMessages,
      temperature: this.config.temperature,
      max_tokens: this.config.maxTokens,
      stream
    };

    // 添加Function Calling支持
    if (functions && functions.length > 0 && this.config.enableFunctionCalling) {
      switch (this.config.provider) {
        case 'openai':
        case 'xai':
        case 'custom':
          baseBody.tools = functions.map(func => ({
            type: 'function',
            function: func
          }));
          baseBody.tool_choice = 'auto';
          break;
        case 'claude':
          baseBody.tools = functions.map(func => ({
            name: func.name,
            description: func.description,
            input_schema: func.parameters
          }));
          break;
      }
    }

    // 根据不同提供商调整请求格式
    switch (this.config.provider) {
      case 'claude':
        return {
          ...baseBody,
          max_tokens: this.config.maxTokens // Claude使用max_tokens而不是max_completion_tokens
        };
      default:
        return baseBody;
    }
  }

  /**
   * 提取响应内容
   */
  private extractContent(data: any): string {
    switch (this.config.provider) {
      case 'openai':
      case 'xai':
      case 'custom':
        return data.choices?.[0]?.message?.content || '';
      case 'claude':
        return data.content?.[0]?.text || '';
      default:
        return data.choices?.[0]?.message?.content || '';
    }
  }

  /**
   * 提取流式响应内容
   */
  private extractStreamContent(data: any): string {
    switch (this.config.provider) {
      case 'openai':
      case 'xai':
      case 'custom':
        return data.choices?.[0]?.delta?.content || '';
      case 'claude':
        return data.delta?.text || '';
      default:
        return data.choices?.[0]?.delta?.content || '';
    }
  }

  /**
   * 提取token数量
   */
  private extractTokenCount(data: any): number | undefined {
    switch (this.config.provider) {
      case 'openai':
      case 'xai':
      case 'custom':
        return data.usage?.total_tokens;
      case 'claude':
        return data.usage?.input_tokens + data.usage?.output_tokens;
      default:
        return data.usage?.total_tokens;
    }
  }

  /**
   * 提取工具调用
   */
  private extractToolCalls(data: any): ToolCall[] | undefined {
    switch (this.config.provider) {
      case 'openai':
      case 'xai':
      case 'custom':
        return data.choices?.[0]?.message?.tool_calls;
      case 'claude':
        // Claude的工具调用格式可能不同，需要根据实际API调整
        return data.content?.filter((c: any) => c.type === 'tool_use')?.map((c: any) => ({
          id: c.id,
          type: 'function',
          function: {
            name: c.name,
            arguments: JSON.stringify(c.input)
          }
        }));
      default:
        return data.choices?.[0]?.message?.tool_calls;
    }
  }

  /**
   * 提取响应中的tokens信息
   */
  private extractTokens(data: any): number {
    switch (this.config.provider) {
      case 'openai':
      case 'xai':
      case 'custom':
        return data.usage?.total_tokens || 0;
      case 'claude':
        return (data.usage?.input_tokens || 0) + (data.usage?.output_tokens || 0);
      default:
        return data.usage?.total_tokens || 0;
    }
  }
}

/**
 * 工厂函数，创建LLM客户端实例
 */
export function createLLMClient(config: LLMConfig): LLMClient {
  return new LLMClient(config);
}

/**
 * 验证LLM配置
 */
export function validateLLMConfig(config: Partial<LLMConfig>): string[] {
  const errors: string[] = [];

  if (!config.provider) {
    errors.push('请选择LLM提供商');
  }

  if (!config.endpoint) {
    errors.push('请输入API端点');
  } else if (!config.endpoint.startsWith('http')) {
    errors.push('API端点必须以http或https开头');
  }

  if (!config.apiKey) {
    errors.push('请输入API密钥');
  }

  if (!config.model) {
    errors.push('请输入模型名称');
  }

  if (config.temperature !== undefined && (config.temperature < 0 || config.temperature > 2)) {
    errors.push('Temperature必须在0-2之间');
  }

  if (config.maxTokens !== undefined && (config.maxTokens < 1 || config.maxTokens > 4000)) {
    errors.push('Max Tokens必须在1-4000之间');
  }

  return errors;
}

/**
 * 默认的系统提示词，针对AI导师角色定制
 */
export const DEFAULT_SYSTEM_PROMPT = `你是一位专业的AI导师，专门指导用户学习和探索细胞自动机（Cellular Automata）的世界。你的任务是：

1. **教学风格**：
   - 保持耐心和友好的语气
   - 根据用户的问题调整解释的深度
   - 使用生动的比喻和实例来解释复杂概念
   - 鼓励用户动手实验和探索

2. **知识领域**：
   - Conway's Game of Life（生命游戏）
   - Boids群体行为仿真
   - Ising模型和磁性相变
   - Physarum黏菌算法
   - Schelling分离模型
   - 细胞自动机的数学原理和应用

3. **交互方式**：
   - 主动询问用户的学习背景和兴趣点
   - 建议适合的参数设置和实验
   - 解释观察到的现象背后的科学原理
   - 提供进一步学习的方向和资源

4. **回复格式**：
   - 使用Markdown格式来组织内容
   - 适当使用**粗体**来强调重点
   - 使用列表来组织步骤或要点
   - 保持回复简洁但信息丰富

5. **指令处理原则**：
   - 当用户提出包含多个操作的复杂指令时，系统会自动将其分解为多个基本操作步骤
   - 每次只执行一个基本操作，确保每个步骤都能正确完成
   - 在执行参数设置后，会等待用户确认再进行仿真控制操作
   - 例如："将网格大小设置为200，运行仿真看效果" 会被分解为：
     1. 设置网格大小为200
     2. 等待用户确认后再运行仿真
   - 这样可以避免执行混乱，确保每个操作都能被正确处理

请记住，你的目标是让学习变得有趣和有意义，帮助用户深入理解复杂系统的美妙之处。当处理复杂指令时，要耐心地引导用户完成每个步骤。`;

export default LLMClient;
