import CryptoJS from 'crypto-js';

/**
 * 数据加密存储工具
 * 提供安全的本地存储功能，特别用于保护API密钥等敏感信息
 */

// 生成固定的加密密钥（基于用户设备特征）
const generateEncryptionKey = (): string => {
  // 使用浏览器特征生成相对稳定的密钥
  const userAgent = navigator.userAgent;
  const screen = `${window.screen.width}x${window.screen.height}`;
  const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
  const language = navigator.language;
  
  // 组合这些信息生成密钥
  const fingerprint = `${userAgent}-${screen}-${timezone}-${language}`;
  return CryptoJS.SHA256(fingerprint).toString();
};

// 全局加密密钥
const ENCRYPTION_KEY = generateEncryptionKey();

/**
 * 加密数据
 */
export const encryptData = (data: string): string => {
  try {
    const encrypted = CryptoJS.AES.encrypt(data, ENCRYPTION_KEY).toString();
    return encrypted;
  } catch (error) {
    console.error('数据加密失败:', error);
    throw new Error('数据加密失败');
  }
};

/**
 * 解密数据
 */
export const decryptData = (encryptedData: string): string => {
  try {
    const decrypted = CryptoJS.AES.decrypt(encryptedData, ENCRYPTION_KEY);
    const originalData = decrypted.toString(CryptoJS.enc.Utf8);
    
    if (!originalData) {
      throw new Error('解密后数据为空');
    }
    
    return originalData;
  } catch (error) {
    console.error('数据解密失败:', error);
    throw new Error('数据解密失败');
  }
};

/**
 * 安全的localStorage封装
 */
export class SecureStorage {
  private static readonly AI_TUTOR_PREFIX = 'ai_tutor_';

  /**
   * 存储加密数据
   */
  static setItem(key: string, value: any): void {
    try {
      const serializedValue = JSON.stringify(value);
      const encryptedValue = encryptData(serializedValue);
      const fullKey = this.AI_TUTOR_PREFIX + key;
      
      localStorage.setItem(fullKey, encryptedValue);
    } catch (error) {
      console.error('安全存储失败:', error);
      throw new Error(`存储数据失败: ${key}`);
    }
  }

  /**
   * 获取并解密数据
   */
  static getItem<T = any>(key: string): T | null {
    try {
      const fullKey = this.AI_TUTOR_PREFIX + key;
      const encryptedValue = localStorage.getItem(fullKey);
      
      if (!encryptedValue) {
        return null;
      }

      const decryptedValue = decryptData(encryptedValue);
      return JSON.parse(decryptedValue) as T;
    } catch (error) {
      console.error('安全读取失败:', error);
      // 如果解密失败，删除损坏的数据
      this.removeItem(key);
      return null;
    }
  }

  /**
   * 删除数据
   */
  static removeItem(key: string): void {
    try {
      const fullKey = this.AI_TUTOR_PREFIX + key;
      localStorage.removeItem(fullKey);
    } catch (error) {
      console.error('删除数据失败:', error);
    }
  }

  /**
   * 检查数据是否存在
   */
  static hasItem(key: string): boolean {
    try {
      const fullKey = this.AI_TUTOR_PREFIX + key;
      return localStorage.getItem(fullKey) !== null;
    } catch (error) {
      console.error('检查数据存在性失败:', error);
      return false;
    }
  }

  /**
   * 清除所有AI导师相关数据
   */
  static clearAll(): void {
    try {
      const keysToRemove: string[] = [];
      
      // 找出所有AI导师相关的key
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key && key.startsWith(this.AI_TUTOR_PREFIX)) {
          keysToRemove.push(key);
        }
      }
      
      // 删除所有相关数据
      keysToRemove.forEach(key => {
        localStorage.removeItem(key);
      });
    } catch (error) {
      console.error('清除所有数据失败:', error);
    }
  }

  /**
   * 获取所有AI导师相关的key
   */
  static getAllKeys(): string[] {
    try {
      const keys: string[] = [];
      
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key && key.startsWith(this.AI_TUTOR_PREFIX)) {
          // 返回不带前缀的key
          keys.push(key.replace(this.AI_TUTOR_PREFIX, ''));
        }
      }
      
      return keys;
    } catch (error) {
      console.error('获取所有key失败:', error);
      return [];
    }
  }

  /**
   * 导出配置（用于备份）
   */
  static exportConfig(): string {
    try {
      const config: Record<string, any> = {};
      const keys = this.getAllKeys();
      
      keys.forEach(key => {
        const value = this.getItem(key);
        if (value !== null) {
          config[key] = value;
        }
      });
      
      return JSON.stringify(config, null, 2);
    } catch (error) {
      console.error('导出配置失败:', error);
      throw new Error('导出配置失败');
    }
  }

  /**
   * 导入配置（用于恢复）
   */
  static importConfig(configJson: string): void {
    try {
      const config = JSON.parse(configJson);
      
      Object.entries(config).forEach(([key, value]) => {
        this.setItem(key, value);
      });
    } catch (error) {
      console.error('导入配置失败:', error);
      throw new Error('导入配置失败');
    }
  }
}

/**
 * 专门用于API密钥的安全存储
 */
export class APIKeyManager {
  private static readonly API_KEY_STORAGE_KEY = 'llm_config';

  /**
   * 存储API密钥和相关配置
   */
  static store(config: {
    provider: string;
    endpoint: string;
    apiKey: string;
    model: string;
    [key: string]: any;
  }): void {
    try {
      // 对API密钥进行二次加密
      const secureConfig = {
        ...config,
        apiKey: encryptData(config.apiKey)
      };
      
      SecureStorage.setItem(this.API_KEY_STORAGE_KEY, secureConfig);
    } catch (error) {
      console.error('API密钥存储失败:', error);
      throw new Error('API密钥存储失败');
    }
  }

  /**
   * 获取API密钥和配置
   */
  static retrieve(): any | null {
    try {
      const config = SecureStorage.getItem(this.API_KEY_STORAGE_KEY);
      
      if (!config || !config.apiKey) {
        return null;
      }

      // 解密API密钥
      return {
        ...config,
        apiKey: decryptData(config.apiKey)
      };
    } catch (error) {
      console.error('API密钥获取失败:', error);
      return null;
    }
  }

  /**
   * 删除API密钥
   */
  static clear(): void {
    SecureStorage.removeItem(this.API_KEY_STORAGE_KEY);
  }

  /**
   * 检查是否有存储的API密钥
   */
  static hasStoredKey(): boolean {
    return SecureStorage.hasItem(this.API_KEY_STORAGE_KEY);
  }
}

/**
 * 聊天历史管理
 */
export class ChatHistoryManager {
  private static readonly CHAT_HISTORY_KEY = 'chat_history';
  private static readonly MAX_HISTORY_SIZE = 100; // 最多保存100条消息

  /**
   * 保存聊天历史
   */
  static save(messages: any[]): void {
    try {
      // 限制历史记录大小
      const limitedMessages = messages.slice(-this.MAX_HISTORY_SIZE);
      
      const historyData = {
        messages: limitedMessages,
        updatedAt: Date.now()
      };
      
      SecureStorage.setItem(this.CHAT_HISTORY_KEY, historyData);
    } catch (error) {
      console.error('聊天历史保存失败:', error);
    }
  }

  /**
   * 获取聊天历史
   */
  static load(): any[] {
    try {
      const historyData = SecureStorage.getItem(this.CHAT_HISTORY_KEY);
      
      if (!historyData || !historyData.messages) {
        return [];
      }
      
      return historyData.messages;
    } catch (error) {
      console.error('聊天历史读取失败:', error);
      return [];
    }
  }

  /**
   * 清除聊天历史
   */
  static clear(): void {
    SecureStorage.removeItem(this.CHAT_HISTORY_KEY);
  }

  /**
   * 获取历史记录大小
   */
  static getSize(): number {
    try {
      const messages = this.load();
      return messages.length;
    } catch (error) {
      return 0;
    }
  }
}

// 默认导出主要的存储类
export default SecureStorage;
