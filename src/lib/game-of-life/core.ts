export function createGrid(width: number, height: number): number[][] {
  // 确保width和height至少为1，避免创建无效的数组
  const safeWidth = Math.max(1, Math.floor(width) || 1);
  const safeHeight = Math.max(1, Math.floor(height) || 1);
  
  return Array.from({ length: safeHeight }, () => Array(safeWidth).fill(0));
}

export interface GameRules {
  survivalMin: number;
  survivalMax: number;
  birthMin: number;
  birthMax: number;
}

export function computeNextGeneration(grid: number[][], width: number, height: number, wrapEdges: boolean, rules: GameRules): { nextGrid: number[][], liveCells: number } {
  // 验证输入参数
  if (!grid || !Array.isArray(grid) || grid.length === 0 || !grid[0] || !Array.isArray(grid[0])) {
    return { nextGrid: createGrid(width, height), liveCells: 0 };
  }
  
  const safeWidth = Math.max(1, Math.floor(width) || 1);
  const safeHeight = Math.max(1, Math.floor(height) || 1);
  
  const nextGrid = createGrid(safeWidth, safeHeight);
  let liveCells = 0;

  for (let y = 0; y < safeHeight; y++) {
    for (let x = 0; x < safeWidth; x++) {
      let liveNeighbors = 0;
      for (let i = -1; i <= 1; i++) {
        for (let j = -1; j <= 1; j++) {
          if (i === 0 && j === 0) continue;

          let nx = x + j;
          let ny = y + i;

          if (wrapEdges) {
            nx = (nx + safeWidth) % safeWidth;
            ny = (ny + safeHeight) % safeHeight;
          }

          if (nx >= 0 && nx < safeWidth && ny >= 0 && ny < safeHeight && 
              grid[ny] && typeof grid[ny][nx] === 'number') {
            liveNeighbors += grid[ny][nx];
          }
        }
      }

      const cell = grid[y] && typeof grid[y][x] === 'number' ? grid[y][x] : 0;
      if (cell === 1) {
        if (liveNeighbors >= rules.survivalMin && liveNeighbors <= rules.survivalMax) {
          nextGrid[y][x] = 1;
        }
      } else {
        if (liveNeighbors >= rules.birthMin && liveNeighbors <= rules.birthMax) {
          nextGrid[y][x] = 1;
        }
      }
      
      if (nextGrid[y][x] === 1) {
        liveCells++;
      }
    }
  }

  return { nextGrid, liveCells };
}
