/**
 * 安全的数组访问工具函数
 * 用于防止"Cannot read properties of undefined (reading '0')"错误
 */

export function safeGridAccess<T>(
  grid: T[][] | undefined | null,
  row: number,
  col: number,
  defaultValue: T
): T {
  if (!grid || !Array.isArray(grid) || grid.length === 0) {
    return defaultValue;
  }

  if (row < 0 || row >= grid.length) {
    return defaultValue;
  }

  const gridRow = grid[row];
  if (!gridRow || !Array.isArray(gridRow) || gridRow.length === 0) {
    return defaultValue;
  }

  if (col < 0 || col >= gridRow.length) {
    return defaultValue;
  }

  return gridRow[col] !== undefined ? gridRow[col] : defaultValue;
}

export function safeGridSet<T>(
  grid: T[][] | undefined | null,
  row: number,
  col: number,
  value: T
): boolean {
  if (!grid || !Array.isArray(grid) || grid.length === 0) {
    return false;
  }

  if (row < 0 || row >= grid.length) {
    return false;
  }

  const gridRow = grid[row];
  if (!gridRow || !Array.isArray(gridRow) || gridRow.length === 0) {
    return false;
  }

  if (col < 0 || col >= gridRow.length) {
    return false;
  }

  gridRow[col] = value;
  return true;
}

export function isValidGridPosition<T>(
  grid: T[][] | undefined | null,
  row: number,
  col: number
): boolean {
  if (!grid || !Array.isArray(grid) || grid.length === 0) {
    return false;
  }

  if (row < 0 || row >= grid.length) {
    return false;
  }

  const gridRow = grid[row];
  if (!gridRow || !Array.isArray(gridRow) || gridRow.length === 0) {
    return false;
  }

  return col >= 0 && col < gridRow.length && gridRow[col] !== undefined;
}

export function validateGrid<T>(grid: T[][] | undefined | null): grid is T[][] {
  if (!grid || !Array.isArray(grid) || grid.length === 0) {
    return false;
  }

  return grid.every(row => Array.isArray(row) && row.length > 0);
}

export function createSafeGrid<T>(
  width: number,
  height: number,
  defaultValue: T
): T[][] {
  const safeWidth = Math.max(1, Math.floor(width) || 1);
  const safeHeight = Math.max(1, Math.floor(height) || 1);
  
  return Array.from({ length: safeHeight }, () => 
    Array.from({ length: safeWidth }, () => defaultValue)
  );
}

/**
 * 错误边界组件专用的重置函数
 */
export function handleGridError(error: Error, componentName: string) {
  console.error(`Grid error in ${componentName}:`, error);
  
  // 在开发环境中提供更多信息
  if (process.env.NODE_ENV === 'development') {
    console.trace('Grid error stack trace');
  }
  
  // 可以在这里添加错误报告逻辑
  // 比如发送到错误监控服务
}
