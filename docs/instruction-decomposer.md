# 指令分解器 (Instruction Decomposer)

## 概述

指令分解器是一个智能系统，用于将用户提供的复杂指令自动分解为多个基本操作步骤，并按顺序执行。这确保了每个操作都能正确完成，避免了同时执行多个操作时可能出现的混乱。

## 问题背景

在之前的实现中，当用户提供包含多个操作的复杂指令时（例如："将网格大小设置为200，运行仿真看效果"），LLM会同时调用多个函数，这可能导致：

1. 执行混乱或不执行
2. 参数设置和仿真控制同时进行，导致状态不一致
3. 用户无法确认每个步骤是否正确完成

## 解决方案

### 自动指令分解

系统会自动分析用户的指令，识别是否包含多个操作：

- **参数设置操作**：如设置网格大小、相似性阈值、人口密度等
- **仿真控制操作**：如启动仿真、暂停仿真、重置等

当检测到需要分解时，系统会将指令按优先级分组：
1. 参数设置操作（每个参数一个步骤）
2. 其他操作
3. 仿真控制操作

### 分步执行流程

1. **第一步自动执行**：系统执行第一个步骤并显示结果
2. **等待用户确认**：显示"继续执行下一步"按钮
3. **用户确认后继续**：点击按钮执行下一步
4. **重复直到完成**：直到所有步骤都执行完毕

## 使用示例

### 复杂指令示例

**用户输入：**
```
将网格大小设置为200，相似性阈值设置为0.7，然后运行仿真
```

**系统分解为：**
1. 设置网格大小为200 ✅ (自动执行)
2. 设置相似性阈值为0.7 ⏳ (等待确认)
3. 启动仿真 ⏳ (等待确认)

### 单个操作示例

**用户输入：**
```
将网格大小设置为100
```

**系统处理：**
- 直接执行，无需分解

## 技术实现

### 核心组件

1. **InstructionDecomposer**: 主要的分解器类
2. **InstructionStep**: 单个步骤的数据结构
3. **DecomposedInstructions**: 分解后的指令序列

### 关键方法

```typescript
// 分析是否需要分解
analyzeUserMessage(message: string, toolCalls: ToolCall[]): {
  needsDecomposition: boolean;
  reason?: string;
}

// 分解指令为步骤
decomposeInstructions(userMessage: string, toolCalls: ToolCall[]): DecomposedInstructions

// 获取当前需要执行的步骤
getCurrentStep(): InstructionStep | null

// 完成当前步骤并移动到下一步
completeCurrentStep(result: AIControlResult[]): {
  hasNextStep: boolean;
  nextStep?: InstructionStep;
  isAllCompleted: boolean;
}
```

### UI 集成

在 `AITutorChat.tsx` 中集成了分步执行的UI：

- **进度提示**：显示当前步骤和总步骤数
- **下一步按钮**：用户确认继续执行
- **状态管理**：跟踪当前执行状态

## 配置和自定义

### 支持的操作类型

**参数设置操作：**
- `setGridSize` - 设置网格大小
- `setSimilarityThreshold` - 设置相似性阈值
- `setPopulationDensity` - 设置人口密度
- `setGroupRatio` - 设置群体比例
- 等等...

**仿真控制操作：**
- `startSimulation` - 启动仿真
- `pauseSimulation` - 暂停仿真
- `stepSimulation` - 单步执行
- `resetSimulation` - 重置仿真

### 分解规则

1. **参数 + 控制组合**：如果同时包含参数设置和仿真控制，必须分解
2. **多个参数设置**：如果有多个参数设置，建议分解以确保每个参数正确设置
3. **单个操作**：不需要分解，直接执行

## 最佳实践

### 对于开发者

1. **添加新的操作类型**：在 `isParameterSettingCall` 和 `isSimulationControlCall` 方法中添加新的关键词
2. **自定义步骤描述**：在 `getStepDescription` 方法中添加新操作的描述
3. **错误处理**：确保每个步骤都有适当的错误处理

### 对于用户

1. **复杂指令**：可以自然地描述多个操作，系统会自动分解
2. **确认每步**：仔细检查每个步骤的执行结果再继续
3. **中断执行**：如果某步出错，可以重新开始而不影响之前的设置

## 测试和调试

### 测试工具

使用 `InstructionDecomposerDemo` 组件可以：
- 演示指令分解过程
- 查看执行日志
- 测试不同的指令组合

### 调试信息

系统会在控制台输出详细的调试信息：
- 指令分析结果
- 分解后的步骤
- 每步的执行结果
- 进度更新

## 未来扩展

1. **智能重试**：失败步骤的自动重试机制
2. **步骤预览**：在执行前显示所有步骤的预览
3. **批量确认**：允许用户一次性确认多个步骤
4. **自定义分解规则**：允许用户自定义分解逻辑
