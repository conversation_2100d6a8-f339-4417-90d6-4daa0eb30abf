/**
 * 测试Schelling模型闭包状态问题修复
 * 验证AI导师多次调用控件函数是否正常工作
 */

console.log('🧪 [测试] Schelling模型闭包状态问题修复验证');

// 模拟React组件的状态变化
let simulatedState = {
  isActive: true,
  isRunning: false,
  params: { gridSize: 50 },
  stats: { segregationIndex: 0, happyAgentPct: 0.8 },
  controlPanelRef: {
    current: {
      clickTogglePlay: function() {
        console.log('📱 [模拟] 控制面板clickTogglePlay被调用');
        // 模拟状态变化
        simulatedState.isRunning = !simulatedState.isRunning;
        console.log(`📱 [模拟] 运行状态变更为: ${simulatedState.isRunning}`);
      },
      setGridSize: function(size) {
        console.log(`📱 [模拟] 控制面板setGridSize被调用: ${size}`);
        simulatedState.params.gridSize = size;
        console.log(`📱 [模拟] 网格大小变更为: ${size}`);
        return { success: true, actualValue: size };
      }
    }
  }
};

// 模拟修复前的实现（有闭包问题）
function createOldStartSimulation() {
  // 这里捕获了创建时的状态值
  const isActive = simulatedState.isActive;
  const isRunning = simulatedState.isRunning;
  const controlPanelRef = simulatedState.controlPanelRef;
  
  return function startSimulation() {
    console.log(`❌ [旧实现] 检查状态: isActive=${isActive}, isRunning=${isRunning}`);
    
    if (!isActive) {
      return { success: false, message: '页面不活跃' };
    }
    
    if (!isRunning) {
      controlPanelRef.current.clickTogglePlay();
    }
    
    return {
      success: true,
      currentState: {
        simulation_is_running: !isRunning ? true : isRunning
      }
    };
  };
}

// 模拟修复后的实现（解决闭包问题）
function createNewStartSimulation() {
  return function startSimulation() {
    // 实时获取当前状态，避免闭包问题
    const getCurrentIsActive = () => simulatedState.isActive;
    const getCurrentIsRunning = () => simulatedState.isRunning;
    const getCurrentControlPanel = () => simulatedState.controlPanelRef.current;
    
    const currentIsActive = getCurrentIsActive();
    const currentIsRunning = getCurrentIsRunning();
    const currentControlPanel = getCurrentControlPanel();
    
    console.log(`✅ [新实现] 检查状态: isActive=${currentIsActive}, isRunning=${currentIsRunning}`);
    
    if (!currentIsActive) {
      return { success: false, message: '页面不活跃' };
    }
    
    if (!currentIsRunning) {
      currentControlPanel.clickTogglePlay();
    }
    
    return {
      success: true,
      currentState: {
        simulation_is_running: !currentIsRunning ? true : currentIsRunning
      }
    };
  };
}

// 模拟修复后的setGridSize实现
function createNewSetGridSize() {
  return function setGridSize(size) {
    const getCurrentParams = () => simulatedState.params;
    const getCurrentControlPanel = () => simulatedState.controlPanelRef.current;
    
    const currentParams = getCurrentParams();
    const currentControlPanel = getCurrentControlPanel();
    
    console.log(`✅ [新实现] 设置网格大小: ${size}, 当前大小: ${currentParams.gridSize}`);
    
    if (currentControlPanel?.setGridSize) {
      const result = currentControlPanel.setGridSize(size);
      return {
        success: true,
        previousValue: currentParams.gridSize,
        actualValue: size,
        message: `网格大小从 ${currentParams.gridSize} 调整为 ${size}`
      };
    }
    
    return { success: false, message: 'setGridSize方法不存在' };
  };
}

// 运行测试
console.log('\n🚀 开始测试...');

// 测试1: 验证闭包问题
console.log('\n📋 测试1: 验证闭包问题');
console.log('初始状态:', simulatedState);

const oldStartSimulation = createOldStartSimulation();
const newStartSimulation = createNewStartSimulation();

// 第一次调用
console.log('\n🔄 第一次调用:');
const oldResult1 = oldStartSimulation();
const newResult1 = newStartSimulation();

console.log('旧实现结果:', oldResult1);
console.log('新实现结果:', newResult1);

// 状态变化（模拟组件重新渲染）
console.log('\n📊 状态变化后:');
console.log('当前状态:', simulatedState);

// 第二次调用
console.log('\n🔄 第二次调用:');
const oldResult2 = oldStartSimulation();
const newResult2 = newStartSimulation();

console.log('旧实现结果:', oldResult2);
console.log('新实现结果:', newResult2);

// 测试2: 验证setGridSize问题
console.log('\n📋 测试2: 验证setGridSize问题');

// 重置状态
simulatedState = {
  isActive: true,
  isRunning: false,
  params: { gridSize: 50 },
  stats: { segregationIndex: 0, happyAgentPct: 0.8 },
  controlPanelRef: {
    current: {
      setGridSize: function(size) {
        console.log(`📱 [模拟] 控制面板setGridSize被调用: ${size}`);
        simulatedState.params.gridSize = size;
        console.log(`📱 [模拟] 网格大小变更为: ${size}`);
        return { success: true, actualValue: size };
      }
    }
  }
};

const newSetGridSize = createNewSetGridSize();

// 第一次设置
console.log('\n🔄 第一次设置网格大小为100:');
const gridResult1 = newSetGridSize(100);
console.log('结果:', gridResult1);
console.log('状态:', simulatedState.params);

// 第二次设置
console.log('\n🔄 第二次设置网格大小为200:');
const gridResult2 = newSetGridSize(200);
console.log('结果:', gridResult2);
console.log('状态:', simulatedState.params);

// 测试结果分析
console.log('\n📊 测试结果分析:');

const testsPassed = [
  // 测试1: 旧实现应该有闭包问题，新实现应该正常工作
  oldResult2.currentState.simulation_is_running === false, // 旧实现有问题
  newResult2.currentState.simulation_is_running === true,  // 新实现正常
  
  // 测试2: 网格大小设置应该正常工作
  gridResult1.success === true,
  gridResult2.success === true,
  simulatedState.params.gridSize === 200 // 最终状态应该是200
];

const passedCount = testsPassed.filter(Boolean).length;
const totalCount = testsPassed.length;

console.log(`✅ 通过测试: ${passedCount}/${totalCount}`);

if (passedCount === totalCount) {
  console.log('🎉 所有测试通过！闭包问题已修复');
} else {
  console.log('❌ 部分测试失败，需要进一步检查');
}

console.log('\n🔍 修复要点总结:');
console.log('1. ✅ 避免在useEffect闭包中直接使用状态变量');
console.log('2. ✅ 在函数内部实时获取当前状态值');
console.log('3. ✅ 使用函数形式获取最新的状态引用');
console.log('4. ✅ 确保控制面板引用也是实时获取的');

console.log('\n📋 修复前后对比:');
console.log('修复前: 闭包捕获创建时的状态，后续调用使用过时状态');
console.log('修复后: 每次调用都实时获取当前状态，确保状态同步');

console.log('\n✨ 测试完成！');
